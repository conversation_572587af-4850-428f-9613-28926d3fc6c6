# 表单查询组件使用说明

## 概述

表单查询组件是一个专门用于与表格组件联动查询的表单组件。它能够收集用户输入的查询条件，动态调用REST API，并将查询结果传递给表格组件进行数据刷新。

## 功能特性

### 支持的字段类型
- **文本输入框** (`text`) - 单行文本输入，支持关键词查询
- **数字输入框** (`number`) - 数字输入，支持数值范围查询
- **下拉选择** (`select`) - 单选下拉框，支持枚举值查询
- **日期选择器** (`date`) - 单日期选择，支持日期查询
- **日期范围选择器** (`daterange`) - 日期范围选择，自动拆分为开始和结束参数

### 查询功能
- **动态REST调用** - 根据查询条件动态调用REST接口
- **参数映射** - 支持自定义字段名到API参数名的映射
- **多组件联动** - 可同时触发多个表格组件的数据刷新
- **自动查询** - 支持字段变化时自动触发查询（可配置防抖延迟）
- **初始查询** - 支持组件加载时自动执行查询

### 布局配置
- **多列布局** - 支持1-4列的网格布局
- **标签位置** - 支持顶部、左侧、右侧标签位置
- **按钮位置** - 支持字段末位（内联）和新行末位两种布局
  - `inline` - 按钮显示在最后一个字段的旁边，节省垂直空间
  - `newline` - 按钮显示在新的一行，布局更清晰
- **按钮对齐** - 支持左对齐、居中、右对齐的按钮布局
- **响应式设计** - 自适应不同屏幕尺寸

## 使用方法

### 1. 添加表单查询组件
在仪表板编辑模式下，从顶部工具栏的"表单查询"按钮添加组件到画布上。

### 2. 配置基础信息
在右侧属性面板的"基础配置"中设置：
- **显示标题** - 是否显示表单标题
- **标题文本** - 表单标题内容
- **目标组件** - 选择需要联动的表格组件（支持多选）

### 3. 配置查询字段
在"字段配置"面板中：
1. 点击"添加字段"按钮
2. 在弹出的对话框中配置字段属性：
   - **字段类型** - 选择合适的输入控件类型
   - **字段标签** - 显示给用户的标签文本
   - **字段名称** - 用于数据收集的字段名
   - **占位符** - 输入框的提示文本
   - **默认值** - 字段的初始值
   - **参数映射** - 配置API参数名映射
   - **验证规则** - 设置必填、长度等验证规则

### 4. 配置布局样式
在"布局配置"面板中调整：
- **表单列数** - 设置字段的网格列数
- **标签位置** - 调整标签相对于输入框的位置
- **标签宽度** - 设置标签的宽度
- **组件尺寸** - 选择输入框的大小
- **显示边框** - 是否显示表单边框
- **字段间距** - 调整字段之间的间距
- **按钮对齐** - 设置操作按钮的对齐方式

### 5. 配置查询行为
在"查询配置"面板中设置：
- **自动查询** - 字段变化时是否自动触发查询
- **查询延迟** - 自动查询的防抖延迟时间（毫秒）
- **初始查询** - 组件加载时是否自动查询
- **重置后查询** - 重置表单后是否自动查询

### 6. 配置按钮显示
在"按钮配置"面板中设置：
- **查询按钮** - 是否显示查询按钮及其文本
- **重置按钮** - 是否显示重置按钮及其文本
- **清空按钮** - 是否显示清空按钮及其文本

## 参数映射说明

### 普通字段映射
对于文本、数字、选择等普通字段，可以配置参数名映射：
```
字段名: keyword
参数映射: search_keyword
实际API参数: ?search_keyword=用户输入值
```

### 日期范围映射
对于日期范围字段，会自动拆分为两个参数：
```
字段名: dateRange
开始参数名: start_date
结束参数名: end_date
实际API参数: ?start_date=2024-01-01&end_date=2024-01-31
```

## 与表格组件联动

### 前提条件
1. 表格组件必须配置为**REST数据源**
2. 表格组件必须有完整的REST接口配置
3. 在表单查询组件中正确选择目标表格组件

### 联动流程
1. 用户在表单中输入查询条件
2. 点击查询按钮或触发自动查询
3. 表单查询组件构建查询参数
4. 通过事件总线通知目标表格组件更新REST参数
5. 表格组件使用新参数重新调用REST接口
6. 表格显示更新后的数据

### 参数传递机制
表单查询组件会将查询条件转换为REST接口参数，并通过以下事件与表格组件通信：
- `update-query-params-${componentId}` - 更新查询参数
- `query-data-${componentId}` - 触发数据查询

## 最佳实践

### 1. 字段设计
- 使用清晰的字段标签，便于用户理解
- 为常用查询条件设置合适的默认值
- 合理使用必填验证，避免过度限制

### 2. 布局优化
- 根据查询条件数量选择合适的列数
- 重要的查询条件放在前面
- 保持按钮位置的一致性

### 3. 性能优化
- 对于自动查询，设置合理的防抖延迟
- 避免过于频繁的API调用
- 考虑使用初始查询加载默认数据

### 4. 用户体验
- 提供清晰的查询反馈
- 支持快速重置和清空操作
- 保持查询条件的持久化（如果需要）

## 注意事项

1. **REST接口要求** - 目标表格组件必须配置为REST数据源
2. **参数命名** - 确保参数映射与后端API接口参数名一致
3. **数据格式** - 后端API返回的数据格式需要符合表格组件的要求
4. **错误处理** - 合理处理API调用失败的情况
5. **权限控制** - 确保用户有权限访问相关的REST接口

## 故障排除

### 查询无效果
1. 检查目标组件是否正确选择
2. 确认表格组件是否为REST数据源
3. 验证参数映射是否正确
4. 查看浏览器控制台的错误信息

### 参数传递问题
1. 检查字段名和参数映射配置
2. 确认日期范围的开始/结束参数名
3. 验证参数值的数据类型

### 性能问题
1. 调整自动查询的防抖延迟
2. 减少不必要的查询条件
3. 优化后端API的响应速度
