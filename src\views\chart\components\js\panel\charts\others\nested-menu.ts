import { AbstractChartView, ChartLibraryType, ChartRenderType } from '@/views/chart/components/js/panel/types'
import { useI18n } from '@/hooks/web/useI18n'

const { t } = useI18n()

/**
 * 嵌套菜单组件
 */
export class NestedMenuChartView extends AbstractChartView {
  properties: any[] = [
    'background-overall-component',
    'border-style',
    'function-cfg'
  ]
  propertyInner: any = {
    'background-overall-component': ['all'],
    'border-style': ['all'],
    'function-cfg': ['all']
  }
  axis: any[] = []
  axisConfig: any = {}
  selectorSpec: any = {}

  constructor() {
    super(ChartRenderType.CUSTOM, ChartLibraryType.NESTED_MENU, 'nested-menu', [])
    this.title = '嵌套菜单'
  }

  drawChart(): any {
    // 嵌套菜单组件不需要绘制图表，直接返回null
    return null
  }

  setupDefaultOptions(chart: any): any {
    // 设置默认配置
    return chart
  }
}
