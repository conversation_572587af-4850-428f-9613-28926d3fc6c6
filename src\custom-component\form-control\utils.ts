/**
 * 表单控件工具函数
 */

import type { FormField, FormData, FormValidationResult, SelectOption } from './types'
import { VALIDATION_PATTERNS } from './types'

/**
 * 验证单个字段
 * @param field 字段配置
 * @param value 字段值
 * @returns 验证结果
 */
export const validateField = (field: FormField, value: any): { valid: boolean; message?: string } => {
  // 必填验证
  if (field.required) {
    if (value === null || value === undefined || value === '') {
      return { valid: false, message: `请输入${field.label}` }
    }
    
    // 数组类型（复选框）的必填验证
    if (Array.isArray(value) && value.length === 0) {
      return { valid: false, message: `请选择${field.label}` }
    }
  }
  
  // 如果值为空且非必填，则跳过其他验证
  if (!value && !field.required) {
    return { valid: true }
  }
  
  // 长度验证
  if (field.validation) {
    const validation = field.validation
    const valueStr = String(value)
    
    if (validation.min !== undefined && valueStr.length < validation.min) {
      return { 
        valid: false, 
        message: validation.message || `${field.label}最少需要${validation.min}个字符` 
      }
    }
    
    if (validation.max !== undefined && valueStr.length > validation.max) {
      return { 
        valid: false, 
        message: validation.message || `${field.label}最多允许${validation.max}个字符` 
      }
    }
    
    // 自定义正则验证
    if (validation.pattern) {
      const regex = new RegExp(validation.pattern)
      if (!regex.test(valueStr)) {
        return { 
          valid: false, 
          message: validation.message || `${field.label}格式不正确` 
        }
      }
    }
  }
  
  // 内置类型验证
  if (field.type === 'email') {
    if (!VALIDATION_PATTERNS.email.test(String(value))) {
      return { valid: false, message: '请输入正确的邮箱地址' }
    }
  }
  
  if (field.type === 'number') {
    const numValue = Number(value)
    if (isNaN(numValue)) {
      return { valid: false, message: `${field.label}必须是数字` }
    }
    
    if (field.validation?.min !== undefined && numValue < field.validation.min) {
      return { valid: false, message: `${field.label}不能小于${field.validation.min}` }
    }
    
    if (field.validation?.max !== undefined && numValue > field.validation.max) {
      return { valid: false, message: `${field.label}不能大于${field.validation.max}` }
    }
  }
  
  return { valid: true }
}

/**
 * 验证整个表单
 * @param fields 字段配置数组
 * @param formData 表单数据
 * @returns 验证结果
 */
export const validateForm = (fields: FormField[], formData: FormData): FormValidationResult => {
  const errors: Record<string, string> = {}
  let valid = true
  
  fields.forEach(field => {
    if (!field.visible) return // 跳过隐藏字段
    
    const value = formData[field.name]
    const fieldResult = validateField(field, value)
    
    if (!fieldResult.valid) {
      valid = false
      errors[field.name] = fieldResult.message || '验证失败'
    }
  })
  
  return { valid, errors }
}

/**
 * 格式化表单数据用于提交
 * @param fields 字段配置数组
 * @param formData 表单数据
 * @returns 格式化后的数据
 */
export const formatFormDataForSubmit = (fields: FormField[], formData: FormData): Record<string, any> => {
  const result: Record<string, any> = {}
  
  fields.forEach(field => {
    if (!field.visible) return // 跳过隐藏字段
    
    const value = formData[field.name]
    
    // 根据字段类型格式化数据
    switch (field.type) {
      case 'number':
        result[field.name] = value !== null && value !== undefined ? Number(value) : null
        break
      case 'checkbox':
        result[field.name] = Array.isArray(value) ? value : []
        break
      case 'date':
        if (value instanceof Date) {
          result[field.name] = value.toISOString().split('T')[0] // YYYY-MM-DD格式
        } else {
          result[field.name] = value
        }
        break
      default:
        result[field.name] = value
    }
  })
  
  return result
}

/**
 * 生成字段的默认值
 * @param field 字段配置
 * @returns 默认值
 */
export const getFieldDefaultValue = (field: FormField): any => {
  if (field.defaultValue !== undefined) {
    return field.defaultValue
  }
  
  switch (field.type) {
    case 'checkbox':
      return []
    case 'number':
      return null
    case 'date':
      return null
    default:
      return ''
  }
}

/**
 * 初始化表单数据
 * @param fields 字段配置数组
 * @returns 初始化的表单数据
 */
export const initializeFormData = (fields: FormField[]): FormData => {
  const formData: FormData = {}
  
  fields.forEach(field => {
    formData[field.name] = getFieldDefaultValue(field)
  })
  
  return formData
}

/**
 * 重置表单数据
 * @param fields 字段配置数组
 * @param formData 表单数据对象（会被修改）
 */
export const resetFormData = (fields: FormField[], formData: FormData): void => {
  fields.forEach(field => {
    formData[field.name] = getFieldDefaultValue(field)
  })
}

/**
 * 检查字段是否需要选项配置
 * @param fieldType 字段类型
 * @returns 是否需要选项
 */
export const fieldNeedsOptions = (fieldType: string): boolean => {
  return ['select', 'radio', 'checkbox'].includes(fieldType)
}

/**
 * 生成字段选项的默认配置
 * @param count 选项数量
 * @returns 选项数组
 */
export const generateDefaultOptions = (count = 2): SelectOption[] => {
  const options: SelectOption[] = []
  
  for (let i = 1; i <= count; i++) {
    options.push({
      label: `选项${i}`,
      value: `option${i}`
    })
  }
  
  return options
}

/**
 * 验证字段名称是否唯一
 * @param fields 字段配置数组
 * @param fieldName 要检查的字段名称
 * @param excludeIndex 排除的字段索引（用于编辑时）
 * @returns 是否唯一
 */
export const isFieldNameUnique = (fields: FormField[], fieldName: string, excludeIndex?: number): boolean => {
  return !fields.some((field, index) => {
    if (excludeIndex !== undefined && index === excludeIndex) {
      return false
    }
    return field.name === fieldName
  })
}

/**
 * 生成唯一的字段名称
 * @param fields 现有字段配置数组
 * @param baseName 基础名称
 * @returns 唯一的字段名称
 */
export const generateUniqueFieldName = (fields: FormField[], baseName: string = 'field'): string => {
  let counter = 1
  let fieldName = `${baseName}_${counter}`
  
  while (!isFieldNameUnique(fields, fieldName)) {
    counter++
    fieldName = `${baseName}_${counter}`
  }
  
  return fieldName
}

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 * @returns 拷贝后的对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as unknown as T
  }
  
  const cloned = {} as T
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key])
    }
  }
  
  return cloned
}

/**
 * 检查表单配置是否有效
 * @param config 表单配置
 * @returns 验证结果
 */
export const validateFormConfig = (config: any): { valid: boolean; errors: string[] } => {
  const errors: string[] = []
  
  if (!config) {
    errors.push('表单配置不能为空')
    return { valid: false, errors }
  }
  
  if (!config.fields || !Array.isArray(config.fields)) {
    errors.push('字段配置必须是数组')
  } else if (config.fields.length === 0) {
    errors.push('至少需要一个字段')
  } else {
    // 检查字段名称唯一性
    const fieldNames = config.fields.map((field: any) => field.name)
    const uniqueNames = new Set(fieldNames)
    if (fieldNames.length !== uniqueNames.size) {
      errors.push('字段名称必须唯一')
    }
    
    // 检查必填字段
    config.fields.forEach((field: any, index: number) => {
      if (!field.name) {
        errors.push(`字段${index + 1}缺少名称`)
      }
      if (!field.label) {
        errors.push(`字段${index + 1}缺少标签`)
      }
      if (!field.type) {
        errors.push(`字段${index + 1}缺少类型`)
      }
    })
  }
  
  if (config.submitConfig?.enabled && !config.submitConfig.url) {
    errors.push('启用提交功能时必须配置提交URL')
  }
  
  return { valid: errors.length === 0, errors }
}
