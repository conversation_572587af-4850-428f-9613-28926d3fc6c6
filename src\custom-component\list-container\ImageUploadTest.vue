<template>
  <div class="image-upload-test">
    <h2>图片上传测试</h2>
    
    <div class="test-container">
      <h3>配置面板测试</h3>
      <div class="attr-panel">
        <Attr :themes="'light'" />
      </div>
    </div>
    
    <div class="test-container">
      <h3>列表组件预览</h3>
      <div class="component-preview">
        <ListContainer
          :prop-value="testConfig"
          :is-edit="false"
          show-position="preview"
        />
      </div>
    </div>
    
    <div class="test-container">
      <h3>调试信息</h3>
      <div class="debug-panel">
        <pre>{{ JSON.stringify(testConfig, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ListContainer from './Component.vue'
import Attr from './Attr.vue'

const testConfig = ref({
  listStyle: 'notice',
  dataSource: 'static',
  title: '图片上传测试',
  showHeader: true,
  showIcon: true,
  showDescription: true,
  showTime: true,
  showAction: true,
  actionType: 'icon',
  actionText: '查看',
  actionIcon: 'ArrowRight',
  iconSize: 24,
  emptyText: '暂无数据',
  items: [
    {
      title: '测试项目1',
      description: '这是第一个测试项目',
      time: '2024-12-07',
      icon: '',
      actionText: '查看'
    },
    {
      title: '测试项目2',
      description: '这是第二个测试项目',
      time: '2024-12-06',
      icon: '',
      actionText: '查看'
    }
  ],
  borderConfig: {
    showContainerBorder: true,
    containerBorderColor: '#e8e8e8',
    containerBorderWidth: '1px',
    containerBorderStyle: 'solid',
    showItemBorder: true,
    itemBorderColor: '#f0f0f0',
    itemBorderWidth: '1px',
    itemBorderStyle: 'solid',
    showHeaderBorder: true,
    headerBorderColor: '#e8e8e8',
    headerBorderWidth: '1px',
    headerBorderStyle: 'solid'
  }
})
</script>

<style lang="less" scoped>
.image-upload-test {
  padding: 20px;
  
  h2 {
    margin-bottom: 20px;
    color: #333;
  }
  
  h3 {
    margin-bottom: 16px;
    color: #666;
  }
  
  .test-container {
    margin-bottom: 30px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 20px;
  }
  
  .attr-panel {
    max-width: 400px;
    border: 1px dashed #ccc;
    padding: 16px;
    background-color: #f9f9f9;
  }
  
  .component-preview {
    max-width: 600px;
    border: 1px dashed #ccc;
    padding: 16px;
    background-color: #fff;
  }
  
  .debug-panel {
    background-color: #f5f5f5;
    padding: 16px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 400px;
    overflow-y: auto;
    
    pre {
      margin: 0;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}
</style>
