import { defineStore } from 'pinia'
import { store } from '@/store'

interface ProjectInfo {
  id: string | number
  name: string
  type: string
  categoryId?: string | number
  workspaceId?: string | number
  config?: string
  createTime?: string
  updateTime?: string
  status?: number
  [key: string]: any
}

interface ProjectState {
  currentProject: ProjectInfo | null
  projectList: ProjectInfo[]
}

export const useProjectStore = defineStore('project', {
  state: (): ProjectState => ({
    currentProject: null,
    projectList: []
  }),

  getters: {
    getCurrentProject: state => state.currentProject,
    getProjectList: state => state.projectList,
    getProjectById: state => (id: string | number) => {
      return state.projectList.find(project => project.id === id)
    }
  },

  actions: {
    setCurrentProject(project: ProjectInfo) {
      this.currentProject = project
      console.log('设置当前项目:', project)
    },

    updateCurrentProject(updates: Partial<ProjectInfo>) {
      if (this.currentProject) {
        this.currentProject = { ...this.currentProject, ...updates }
        console.log('更新当前项目:', this.currentProject)
      }
    },

    addProject(project: ProjectInfo) {
      const existingIndex = this.projectList.findIndex(p => p.id === project.id)
      if (existingIndex >= 0) {
        this.projectList[existingIndex] = project
      } else {
        this.projectList.push(project)
      }
    },

    removeProject(id: string | number) {
      const index = this.projectList.findIndex(p => p.id === id)
      if (index >= 0) {
        this.projectList.splice(index, 1)
      }
      if (this.currentProject?.id === id) {
        this.currentProject = null
      }
    },

    clearProjects() {
      this.projectList = []
      this.currentProject = null
    }
  }
})

export function useProjectStoreWithOut() {
  return useProjectStore(store)
}
