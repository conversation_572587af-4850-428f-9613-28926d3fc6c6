// 测试数据 - 用于验证4种列表样式的功能

// 通知公告测试数据
export const noticeTestData = [
  {
    title: '维护通知：2024-12-07 系统凌晨维护',
    description: '机房维护，请各单位做好工作安排，感谢支持！',
    time: '2024-12-07',
    icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMSA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDMgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjNDA5RUZGIi8+Cjwvc3ZnPgo=',
    actionText: '查看'
  },
  {
    title: '维护通知：2024-12-05 系统凌晨维护',
    description: '机房维护，请各单位做好工作安排，感谢支持！',
    time: '2024-12-05',
    icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMSA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDMgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjNDA5RUZGIi8+Cjwvc3ZnPgo=',
    actionText: '查看'
  },
  {
    title: '系统升级通知',
    description: '系统将于本周末进行升级，届时可能会有短暂的服务中断',
    time: '2024-12-03',
    icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIGZpbGw9IiNFNkEyM0MiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyUzYuNDggMjIgMTIgMjJTMjIgMTcuNTIgMjIgMTJTMTcuNTIgMiAxMiAyWk0xMyAxN0gxMVYxNUgxM1YxN1pNMTMgMTNIMTFWN0gxM1YxM1oiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=',
    actionText: '查看'
  }
]

// 时间轴测试数据
export const timelineTestData = [
  {
    title: '工作汇报',
    description: '完成本周工作总结和下周工作计划',
    time: '2024-06-21'
  },
  {
    title: '工作汇报',
    description: '参加项目评审会议，讨论技术方案',
    time: '2024-06-24'
  },
  {
    title: '工作汇报',
    description: '完成系统功能开发和测试',
    time: '2024-06-24'
  },
  {
    title: '工作汇报',
    description: '提交项目进度报告',
    time: '2024-06-24'
  }
]

// 任务列表测试数据
export const taskTestData = [
  {
    title: '完成用户管理模块开发',
    description: '包括用户注册、登录、权限管理等功能',
    time: '2024-12-10',
    status: 'processing',
    priority: 'high',
    actionText: '处理'
  },
  {
    title: '修复系统登录bug',
    description: '解决用户登录时偶发的超时问题',
    time: '2024-12-08',
    status: 'pending',
    priority: 'medium',
    actionText: '处理'
  },
  {
    title: '编写API文档',
    description: '为新开发的接口编写详细的API文档',
    time: '2024-12-05',
    status: 'completed',
    priority: 'low',
    actionText: '查看'
  },
  {
    title: '数据库优化',
    description: '优化查询性能，添加必要的索引',
    time: '2024-12-03',
    status: 'cancelled',
    priority: 'medium',
    actionText: '查看'
  }
]

// 预警列表测试数据
export const warningTestData = [
  {
    title: '【qhgxqshzlzx123001】的设备异常预警',
    description: '设备运行异常，请及时处理',
    time: '2024-06-15 09:38:17',
    priority: 'high',
    icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMSA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDMgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjRjU2QzZDIi8+Cjwvc3ZnPgo=',
    actionText: '处理'
  },
  {
    title: '【qr123001】的设备状态预警',
    description: '设备状态异常，需要检查',
    time: '2024-06-15 09:38:17',
    priority: 'medium',
    icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIGZpbGw9IiNFNkEyM0MiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyUzYuNDggMjIgMTIgMjJTMjIgMTcuNTIgMjIgMTJTMTcuNTIgMiAxMiAyWk0xMyAxN0gxMVYxNUgxM1YxN1pNMTMgMTNIMTFWN0gxM1YxM1oiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=',
    actionText: '查看'
  },
  {
    title: '【qhgxqshzlzx123001】的设备维护预警',
    description: '设备需要定期维护',
    time: '2024-06-15 09:38:17',
    priority: 'low',
    icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIGZpbGw9IiM0MDlFRkYiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTkgMTZIMTVWMTRIOVYxNlpNMTIgMkM2LjQ4IDIgMiA2LjQ4IDIgMTJTNi40OCAyMiAxMiAyMlMyMiAxNy41MiAyMiAxMlMxNy41MiAyIDEyIDJaTTEyIDIwQzcuNTkgMjAgNCAyMC40MSA0IDEyUzcuNTkgNCAxMiA0UzIwIDcuNTkgMjAgMTJTMTYuNDEgMjAgMTIgMjBaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K',
    actionText: '查看'
  },
  {
    title: '【qhgxqshzlzx123001】的设备监控预警',
    description: '设备监控数据异常',
    time: '2024-06-15 09:38:17',
    priority: 'high',
    icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMSA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDMgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjRjU2QzZDIi8+Cjwvc3ZnPgo=',
    actionText: '处理'
  }
]

// 默认配置
export const defaultConfigs = {
  notice: {
    listStyle: 'notice',
    dataSource: 'static',
    title: '通知公告',
    showHeader: true,
    showIcon: true,
    showDescription: true,
    showTime: true,
    showAction: true,
    actionType: 'icon',
    actionText: '查看',
    actionIcon: 'ArrowRight',
    items: noticeTestData,
    borderConfig: {
      showContainerBorder: true,
      containerBorderColor: '#e8e8e8',
      containerBorderWidth: '1px',
      containerBorderStyle: 'solid',
      showItemBorder: true,
      itemBorderColor: '#f0f0f0',
      itemBorderWidth: '1px',
      itemBorderStyle: 'solid',
      showHeaderBorder: true,
      headerBorderColor: '#e8e8e8',
      headerBorderWidth: '1px',
      headerBorderStyle: 'solid'
    }
  },
  
  timeline: {
    listStyle: 'timeline',
    dataSource: 'static',
    title: '工作概览',
    showHeader: true,
    showIcon: false,
    showDescription: true,
    showTime: true,
    showAction: false,
    items: timelineTestData,
    borderConfig: {
      showContainerBorder: true,
      containerBorderColor: '#e8e8e8',
      containerBorderWidth: '1px',
      containerBorderStyle: 'solid',
      showItemBorder: false,
      itemBorderColor: '#f0f0f0',
      itemBorderWidth: '1px',
      itemBorderStyle: 'solid',
      showHeaderBorder: true,
      headerBorderColor: '#e8e8e8',
      headerBorderWidth: '1px',
      headerBorderStyle: 'solid'
    }
  },

  task: {
    listStyle: 'task',
    dataSource: 'static',
    title: '待办事项',
    showHeader: true,
    showIcon: false,
    showDescription: true,
    showTime: true,
    showAction: true,
    actionType: 'button',
    actionText: '处理',
    items: taskTestData,
    borderConfig: {
      showContainerBorder: true,
      containerBorderColor: '#e8e8e8',
      containerBorderWidth: '1px',
      containerBorderStyle: 'solid',
      showItemBorder: true,
      itemBorderColor: '#f0f0f0',
      itemBorderWidth: '1px',
      itemBorderStyle: 'solid',
      showHeaderBorder: true,
      headerBorderColor: '#e8e8e8',
      headerBorderWidth: '1px',
      headerBorderStyle: 'solid'
    }
  },

  warning: {
    listStyle: 'warning',
    dataSource: 'static',
    title: '研判预警',
    showHeader: true,
    showIcon: false,
    showDescription: true,
    showTime: true,
    showAction: true,
    actionType: 'button',
    actionText: '查看',
    items: warningTestData,
    borderConfig: {
      showContainerBorder: true,
      containerBorderColor: '#e8e8e8',
      containerBorderWidth: '1px',
      containerBorderStyle: 'solid',
      showItemBorder: true,
      itemBorderColor: '#f0f0f0',
      itemBorderWidth: '1px',
      itemBorderStyle: 'solid',
      showHeaderBorder: true,
      headerBorderColor: '#e8e8e8',
      headerBorderWidth: '1px',
      headerBorderStyle: 'solid'
    }
  }
}

// REST接口配置示例
export const restConfigExamples = {
  notice: {
    url: '/api/notices',
    method: 'GET',
    headers: [
      { key: 'Content-Type', value: 'application/json' }
    ],
    params: [
      { key: 'page', value: '1' },
      { key: 'size', value: '10' }
    ]
  },
  
  task: {
    url: '/api/tasks',
    method: 'GET',
    headers: [
      { key: 'Content-Type', value: 'application/json' },
      { key: 'Authorization', value: 'Bearer ${token}' }
    ],
    params: [
      { key: 'status', value: 'pending' }
    ]
  },
  
  warning: {
    url: '/api/warnings',
    method: 'GET',
    headers: [
      { key: 'Content-Type', value: 'application/json' }
    ],
    params: [
      { key: 'priority', value: 'high' }
    ]
  }
}

// 字段映射配置示例
export const fieldMappingExamples = {
  notice: {
    title: 'title',
    description: 'content',
    time: 'publishTime',
    actionText: 'actionText'
  },
  
  task: {
    title: 'taskName',
    description: 'taskDesc',
    time: 'dueDate',
    status: 'taskStatus',
    priority: 'taskPriority',
    actionText: 'actionText'
  },
  
  warning: {
    title: 'warningTitle',
    description: 'warningDesc',
    time: 'warningTime',
    priority: 'warningLevel',
    actionText: 'actionText'
  }
}
