// import request from '@/config/axios'

// export const queryVisualizationBackground = () =>
//   request.get({ url: '/visualizationBackground/findAll' })

const data = {
  code: 0,
  msg: null,
  data: {
    default: [
      {
        id: 'board_1',
        name: '边框1',
        classification: 'default',
        content: '',
        remark: null,
        sort: null,
        uploadTime: null,
        baseUrl: 'img/board',
        url: 'board/board_1.svg'
      },
      {
        id: 'board_2',
        name: '边框2',
        classification: 'default',
        content: null,
        remark: null,
        sort: null,
        uploadTime: null,
        baseUrl: 'img/board',
        url: 'board/board_2.svg'
      },
      {
        id: 'board_3',
        name: '边框3',
        classification: 'default',
        content: null,
        remark: null,
        sort: null,
        uploadTime: null,
        baseUrl: 'img/board',
        url: 'board/board_3.svg'
      },
      {
        id: 'board_4',
        name: '边框4',
        classification: 'default',
        content: null,
        remark: null,
        sort: null,
        uploadTime: null,
        baseUrl: 'img/board',
        url: 'board/board_4.svg'
      },
      {
        id: 'board_5',
        name: '边框5',
        classification: 'default',
        content: null,
        remark: null,
        sort: null,
        uploadTime: null,
        baseUrl: 'img/board',
        url: 'board/board_5.svg'
      },
      {
        id: 'board_6',
        name: '边框6',
        classification: 'default',
        content: null,
        remark: null,
        sort: null,
        uploadTime: null,
        baseUrl: 'img/board',
        url: 'board/board_6.svg'
      },
      {
        id: 'board_7',
        name: '边框7',
        classification: 'default',
        content: null,
        remark: null,
        sort: null,
        uploadTime: null,
        baseUrl: 'img/board',
        url: 'board/board_7.svg'
      },
      {
        id: 'board_8',
        name: '边框8',
        classification: 'default',
        content: null,
        remark: null,
        sort: null,
        uploadTime: null,
        baseUrl: 'img/board',
        url: 'board/board_8.svg'
      },
      {
        id: 'board_9',
        name: '边框9',
        classification: 'default',
        content: null,
        remark: null,
        sort: null,
        uploadTime: null,
        baseUrl: 'img/board',
        url: 'board/board_9.svg'
      }
    ]
  }
}

export const queryVisualizationBackground = () => Promise.resolve(data)
