import { AbstractChartView, ChartRenderType, ChartLibraryType } from '../../types'
import type { AxisType, AxisConfig, EditorProperty, EditorPropertyInner, EditorSelectorSpec } from '../../types'
import { useI18n } from '@/hooks/web/useI18n'

const { t } = useI18n()

/**
 * 按钮组件
 */
export class ButtonChartView extends AbstractChartView {
  properties: EditorProperty[] = [
    'background-overall-component',
    'border-style'
  ]
  propertyInner: EditorPropertyInner = {
    'background-overall-component': ['all'],
    'border-style': ['all']
  }
  axis: AxisType[] = []
  axisConfig: AxisConfig = {}
  selectorSpec: EditorSelectorSpec = {}

  constructor() {
    super(ChartRenderType.CUSTOM, ChartLibraryType.BUTTON, 'button')
  }
}

export default ButtonChartView
