/**
 * 统一的图片处理服务
 * 根据配置自动选择base64或上传模式
 */

import { getImageConfig, getImageMode, ImageMode } from '@/config/imageConfig'
import { uploadFileResult } from '@/api/staticResource'
import { handleImageUpload } from '@/utils/imageBase64Utils'
import { ElMessage } from 'element-plus-secondary'

export interface ImageUploadResult {
  success: boolean
  data?: string
  message?: string
  mode?: ImageMode
}

/**
 * 统一的图片上传处理
 * @param file 文件对象
 * @param options 选项
 * @returns Promise<ImageUploadResult>
 */
export async function uploadImage(
  file: File,
  options: {
    compress?: boolean
    quality?: number
    maxWidth?: number
    maxHeight?: number
  } = {}
): Promise<ImageUploadResult> {
  const config = getImageConfig()
  const mode = getImageMode()

  try {
    if (mode === ImageMode.BASE64) {
      // Base64模式
      const result = await handleImageUpload(file, {
        compress: options.compress ?? config.compression.enabled,
        quality: options.quality ?? config.compression.quality,
        maxWidth: options.maxWidth ?? config.compression.maxWidth,
        maxHeight: options.maxHeight ?? config.compression.maxHeight,
        maxSize: config.maxFileSize
      })

      return {
        success: result.success,
        data: result.data,
        message: result.message,
        mode: ImageMode.BASE64
      }
    } else {
      // 上传模式
      return new Promise((resolve) => {
        uploadFileResult(file, (fileUrl) => {
          resolve({
            success: true,
            data: fileUrl,
            message: '图片上传成功',
            mode: ImageMode.UPLOAD
          })
        }).catch((error) => {
          console.error('图片上传失败:', error)
          resolve({
            success: false,
            message: '图片上传失败，请重试',
            mode: ImageMode.UPLOAD
          })
        })
      })
    }
  } catch (error) {
    console.error('图片处理失败:', error)
    return {
      success: false,
      message: '图片处理失败，请重试',
      mode
    }
  }
}

/**
 * 图片URL转换（兼容base64和传统URL）
 * @param url 图片URL或base64
 * @returns string 处理后的URL
 */
export function transformImageUrl(url: string): string {
  if (!url) return ''
  
  // 如果是base64图片，直接返回
  if (url.startsWith('data:image/')) {
    return url
  }
  
  // 传统URL处理逻辑
  // 这里可以调用原有的imgUrlTrans函数
  return url
}

/**
 * 批量上传图片
 * @param files 文件数组
 * @param options 选项
 * @returns Promise<ImageUploadResult[]>
 */
export async function uploadImages(
  files: File[],
  options: {
    compress?: boolean
    quality?: number
    maxWidth?: number
    maxHeight?: number
    onProgress?: (completed: number, total: number) => void
  } = {}
): Promise<ImageUploadResult[]> {
  const results: ImageUploadResult[] = []
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    const result = await uploadImage(file, options)
    results.push(result)
    
    // 进度回调
    if (options.onProgress) {
      options.onProgress(i + 1, files.length)
    }
  }
  
  return results
}

/**
 * 验证图片文件
 * @param file 文件对象
 * @returns {valid: boolean, message?: string}
 */
export function validateImage(file: File) {
  const config = getImageConfig()
  
  // 检查文件类型
  if (!config.supportedTypes.includes(file.type)) {
    return { 
      valid: false, 
      message: '仅支持 JPG、PNG、GIF、SVG 格式的图片' 
    }
  }

  // 检查文件大小
  if (file.size > config.maxFileSize) {
    const maxSizeMB = Math.round(config.maxFileSize / (1024 * 1024))
    return { 
      valid: false, 
      message: `图片大小不能超过${maxSizeMB}MB` 
    }
  }

  return { valid: true }
}

/**
 * 获取图片信息
 * @param url 图片URL或base64
 * @returns Promise<{width: number, height: number, size?: number}>
 */
export function getImageInfo(url: string): Promise<{width: number, height: number, size?: number}> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      const info = {
        width: img.width,
        height: img.height,
        size: url.startsWith('data:image/') ? Math.round((url.length * 3) / 4) : undefined
      }
      resolve(info)
    }
    
    img.onerror = reject
    img.src = url
  })
}

/**
 * 压缩图片
 * @param file 文件对象
 * @param options 压缩选项
 * @returns Promise<string> base64字符串
 */
export async function compressImage(
  file: File,
  options: {
    quality?: number
    maxWidth?: number
    maxHeight?: number
  } = {}
): Promise<string> {
  const config = getImageConfig()
  
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img
      const maxWidth = options.maxWidth ?? config.compression.maxWidth
      const maxHeight = options.maxHeight ?? config.compression.maxHeight
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height)
        width = width * ratio
        height = height * ratio
      }

      canvas.width = width
      canvas.height = height

      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, width, height)

      // 转换为base64
      const quality = options.quality ?? config.compression.quality
      const base64 = canvas.toDataURL('image/jpeg', quality)
      resolve(base64)
    }

    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}
