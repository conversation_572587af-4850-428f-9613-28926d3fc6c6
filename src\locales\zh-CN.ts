export default {
  common: {
    empty: '',
    first_item: '首项',
    cross_source: '跨源',
    single_source: '单源',
    source_tips: '数据集存在跨源情况，请检查其他 SQL 节点的语法，是否确认将类型改为单源?',
    other_levels: '除层级一外,其他层级无需进行查询条件配置',
    tree_structure: '下拉树的结构不能为空',
    associated_chart: '关联图表',
    associated_chart_first: '第一层级已关联图表',
    timeout_tips: '请求超时，请稍后再试',
    local_excel: '本地 Excel/CSV',
    remote_excel: '远程 Excel/CSV',
    watermarkManagement: '水印管理',
    list_selection: '列表选择',
    date_setting: '日期设置',
    changing_the_display: '更改展示类型将会清除下拉树相关设置,是否确定?',
    component: {
      input: '单行输入',
      textarea: '多行输入',
      select: '下拉框',
      radio: '单选',
      checkbox: '多选框',
      date: '日期',
      dateRange: '时间范围',
      add_component_hint: '点击或拖拽左侧组件添加字段'
    },
    openMobileTerminal: '开启移动端',
    inputText: '请输入',
    selectText: '请选择',
    add: '添加',
    account: '账号',
    email: '邮箱',
    phone: '手机',
    pwd: '密码',
    require: '必填',
    personal_info: '个人信息',
    about: '关于',
    exit_system: '退出系统',
    letter_start: '必须以字母开头',
    required: '必填',
    operate: '操作',
    create_time: '创建时间',
    edit: '编辑',
    delete: '删除',
    please_input: '请输入',
    please_select: '请选择',
    cancel: '取消',
    sure: '确定',
    save: '保存',
    input_limit: '长度在 {0} 到 {1} 个字符',
    save_success: '保存成功',
    roger_that: '知道了',
    delete_success: '删除成功',
    copy: '复制',
    operating: '操作',
    label: '备注',
    search_keywords: '输入关键字搜索',
    detail: '详情',
    prev: '上一步',
    description: '描述',
    next: '下一步',
    name: '名称',
    input_name: '请输入名称',
    yes: '是',
    no: '否',
    every: '每',
    minute: '分钟',
    second: '秒',
    hour: '时',
    day: '天',
    every_exec: '执行一次',
    cron_exp: 'cron表达式',
    copy_success: '复制成功',
    copy_unsupported: '您的浏览器不支持复制',
    filter: '筛选',
    filter_condition: '筛选条件',
    no_auth_tips: '缺少菜单权限，请联系管理员',
    no_menu_tips: '未找到资源 401错误'
  },
  toolbox: {
    name: '工具箱',
    template_center: '模板中心',
    org_center: '组织管理中心'
  },
  api_pagination: {
    help_documentation: '帮助文档',
    product_forum: '产品论坛',
    technical_blog: '技术博客',
    enterprise_edition_trial: '企业版试用',
    paging_ettings: '分页设置',
    parameter_name: '参数名',
    built_in_parameter_name: '内置参数名',
    request_parameter_name: '请求参数名',
    parameter_default_value: '参数默认值',
    parsing_path: '解析路径',
    total_number: '总数',
    total_number_de: '总数量',
    number_of_pages: '总页数',
    number__size: '页码+大小',
    cursor__size: '游标+大小',
    page_number: '页码',
    pagination_size: '分页大小',
    cursor: '游标',
    pagination_method: '分页方式',
    response: '响应',
    please_enter_jsonpath: '请输入 JsonPath',
    enter_parameter_name: '请输入参数名称',
    enter_default_value: '请输入默认值',
    enter_first_page: '请输入第一页的页码'
  },
  operate_log: {
    name: '操作日志',
    search_by_operate_info: '通过操作对象搜索',
    detail: '操作详情',
    type: '操作类型',
    status: '操作状态',
    success: '成功',
    failed: '失败',
    user: '操作用户',
    time: '操作时间',
    ip: 'IP地址',
    organization: '所属组织',
    error_msg: '错误信息',
    confirm_export: '确定导出日志吗',
    export_success: '导出成功',
    excel_file_name: 'DataEase操作日志',
    relevant_content_found: '没有找到相关内容',
    mobile: '移动端',
    client: '客户端'
  },
  data_set: {
    validation_succeeded: '字段表达式校验成功',
    to_nth_digits: '保留第M至N位',
    the_column_permissions: '确定删除列权限吗?',
    last_n_digits: '保留前M位,后N位',
    rule_preview: '规则预览',
    prohibit_viewing: '禁止查看',
    set_desensitization_rules: '设置脱敏规则',
    the_row_permissions: '确定删除行权限吗?',
    ten_wan: '10万',
    can_go_to: '后台导出中,可前往',
    progress_and_download: '查看进度，进行下载',
    this_data_set: '确定删除该数据集吗？',
    to_delete_them: '该数据集存在如下血缘关系，删除会造成相关仪表板的图表失效，确定删除？',
    check_blood_relationship: '查看血缘关系',
    dataset_export: '数据集导出',
    pieces_of_data: '提示：最多支持导出{limit}条数据',
    enter_parameter_name: '请输入参数名称',
    enter_1_50_characters: '请输入1-50个字符',
    parameter_default_value: '请输入参数默认值',
    edit_calculation_parameters: '编辑计算参数',
    add_calculation_parameters: '添加计算参数',
    parameter_is_supported: '仅支持添加一个计算参数。',
    enter_a_number: '请输入一个数字',
    parameter_name: '参数名称',
    parameter_default_value_de: '参数默认值',
    confirm_the_deletion: '未被勾选, 与其相关的计算字段将被删除，确认删除？',
    confirm_to_delete: '确定要删除{a}吗',
    also_be_deleted: '删除后，被关联的表或sql片段将被删除，与其相关的计算字段也将被删除。',
    deleted_confirm_deletion: '如果该字段被删除，与其相关的计算字段将被删除，确认删除？',
    delete_field_a: '确认删除字段{a}吗',
    field_name: '字段名称',
    field_type: '字段类型',
    field_notes: '字段备注',
    operate_with_caution: '删除后，此文件夹下的所有资源都会被删除，请谨慎操作。',
    delete_this_folder: '确定删除该文件夹吗',
    a_new_dataset: '新建数据集',
    structure_preview: '结构预览',
    data_set_yet: '暂无数据集',
    new_data_screen: '新建数据大屏',
    pieces_in_total: '显示 100 条数据，共 {msg} 条',
    no_data: '暂无数据',
    no_tasks_yet: '暂无任务',
    exporting: '导出中',
    success: '成功',
    fail: '失败',
    waiting: '等待中',
    all: '全部',
    successful_go_to: '导出成功，前往',
    failed_go_to: '导出失败，前往',
    data_set: '数据集',
    view: '图表',
    organization: '所属组织',
    download: '下载',
    re_export: '重新导出',
    delete: '删除',
    reason_for_failure: '失败原因',
    closure: '关闭',
    cannot_be_empty: 'SQL名字不能为空',
    cannot_be_empty_de: 'SQL不能为空',
    sure_to_exit: '你填写的信息未保存，确认退出吗？',
    copied_successfully: '复制成功',
    not_support_copying: '您的浏览器不支持复制：',
    parameters_set_successfully: '参数设置成功',
    run: '运行',
    parameter_settings: '参数设置',
    save: '保存',
    current_data_source: '当前数据源',
    relevant_content_found: '没有找到相关内容',
    physical_field_name: '物理字段名',
    click_above: '点击上方',
    see_the_results: '运行，即可查看运行结果',
    a_folder_name: '请输入文件夹名称',
    the_dataset_name: '请输入数据集名称',
    the_destination_folder: '请选择目标文件夹',
    moved_successfully: '移动成功',
    rename_successful: '重命名成功',
    field: '字段',
    want_to_continue: '未被选择，其相关的新建字段将被删除，是否继续？',
    field_selection: '字段选择',
    edit_sql: '编辑SQL',
    custom_sql_here: '拖拽表或自定义SQL至此处',
    on_the_left: '将左侧的数据表、自定义SQL',
    a_data_set: '拖拽到这里创建数据集',
    rename_table: '重命名表',
    table_name: '表名称',
    table_name_de: '表名',
    table_remarks: '表备注',
    customize: '自定义',
    change_field_type: '更换字段类型',
    text: '文本',
    time: '时间',
    geographical_location: '地理位置',
    numerical_value: '数值',
    numeric_value_decimal: '数值 (小数)',
    edit: '编辑',
    rename: '重命名',
    copy: '复制',
    unnamed_dataset: '未命名数据集',
    cannot_be_empty_time: '自定义时间格式不能为空',
    custom_sql: '自定义SQL',
    want_to_exit: '当前的更改尚未保存,确定退出吗?',
    saved_successfully: '保存成功',
    cannot_be_empty_de_: '表达式不能为空!',
    copy_a_dataset: '复制数据集',
    cannot_be_empty_de_field: '关联字段不能为空!',
    dataset_cannot_be: '数据集不能为空',
    save_and_return: '保存并返回',
    select_data_source: '选择数据源',
    invalid_data_source: '无效数据源',
    be_reported_incorrectly:
      '您正在进行跨数据源的表关联,请确保使用calcite的标准语法和函数,否则会导致数据集报错',
    refresh_data: '刷新数据',
    convert_to_indicator: '转换为指标',
    convert_to_dimension: '转换为维度',
    selected: '已选择',
    bar: '项',
    format_edit: '格式编辑',
    custom_time_format: '自定义时间格式',
    cannot_be_empty_: '过滤字段不能为空',
    cannot_be_empty_de_ruler: '规则条件不能为空'
  },
  data_source: {
    successfully_created: '创建成功',
    continue_to_create: '继续创建',
    data_source_list: '返回数据源列表',
    prompts_next_time: '下次不再提示',
    also_want_to: '您可能还想',
    or_large_screen: '为下一步的仪表板或大屏做准备',
    go_to_create: '去创建',
    verification_successful: '校验成功',
    verification_failed: '校验失败',
    create_successfully: '新建成功',
    operate_with_caution: '删除后，此文件夹下的所有资源都会被删除，请谨慎操作。',
    confirm_to_delete: '有数据集正在使用此数据源，删除后数据集不可用，确认删除？',
    view_blood_relationship: '查看血缘关系',
    no_data_source: '暂无数据源',
    replace_data: '替换数据',
    append_data: '追加数据',
    latest_update_status: '最近更新状态',
    latest_update_time: '最近更新时间',
    data_time: '数据时间：',
    update_all: '全部更新',
    on_the_left: '请在左侧选择数据源',
    update_result: '更新结果',
    failure_details: '失败详情',
    the_request_address: '请输入请求地址',
    name_already_exists: '已经存在同名参数：',
    name_already_exists_de: '已经存在同名的参数表',
    interface_parameters: '接口参数',
    extract_parameters: '提取参数',
    view_data_structure: '查看数据结构',
    the_data_structure: '暂无数据，请在数据结构勾选字段',
    parameter: '参数',
    page_parameter: '分页参数',
    fixed_value: '固定值',
    time_function: '时间函数',
    customize: '自定义',
    that_day: '当天',
    value: '值',
    name_use_parameters: "可用${'{'}参数名{'}'}，使用参数",
    add_parameters: '添加参数',
    data_source_name: '数据源名称',
    data_source_name_de: '请输入数据源名称',
    a_folder_name: '请输入文件夹名称',
    data_source: '数据源',
    the_destination_folder: '请选择目标文件夹',
    relevant_content_found: '没有找到相关内容',
    cannot_be_empty: 'SSH主机不能为空',
    cannot_be_empty_de: 'SSH端口不能为空',
    cannot_be_empty_de_name: 'SSH用户名不能为空',
    cannot_be_empty_de_pwd: 'SSH密码不能为空',
    cannot_be_empty_de_key: 'SSH key不能为空',
    to_64_characters: '参数名称限制2～64字符',
    to_64_characters_de: '接口名称限制2～64字符',
    sure_to_delete: '确定删除吗?',
    delete: '删除',
    source_configuration_information: '数据源配置信息',
    data_update_settings: '数据更新设置',
    connection_method: '连接方式',
    hostname: '主机名',
    jdbc_connection: 'JDBC 连接',
    jdbc_connection_string: ' JDBC 连接字符串',
    ssh_settings: 'SSH 设置',
    enable_ssh: '启用SSH',
    host: '主机',
    please_enter_hostname: '请输入主机名',
    port: '端口',
    password: '密码',
    enter_ssh_key: '请输入ssh key',
    ssh_key_password: 'ssh key 密码',
    update_now: '立即更新',
    update_once: '更新一次',
    edit_parameters: '编辑参数',
    enter_parameter_name: '请输入参数名称',
    text: '文本',
    numerical_value: '数值',
    numeric_value_decimal: '数值(小数)',
    rename: '重命名',
    interface_name: '接口名称',
    the_interface_name: '请输入接口名称',
    to_replace_it: '替换可能会影响自定义数据集、关联数据集、仪表板等，是否替换？',
    document: '文件',
    reupload: '重新上传',
    and_csv_formats: '仅支持xlsx、xls、csv格式的文件',
    please_upload_files: '请上传文件',
    cannot_be_empty_table: '数据表不能为空',
    the_previous_step: '填写的信息将会清空，确定返回上一步吗？',
    add_data_table: '需要添加数据表',
    source_saved_successfully: '保存数据源成功',
    copy_data_source: '复制数据源',
    create_data_source: '创建数据源',
    want_to_exit: '当前的更改尚未保存,确定退出吗?',
    configuration_information: '配置信息',
    recently_created: '最近创建',
    all: '全部',
    api_data: 'API数据'
  },
  dynamic_time: {
    set_default: '设置默认值',
    fix: '固定时间',
    dynamic: '动态时间',
    relative: '相对当前',
    today: '今天',
    yesterday: '昨天',
    firstOfMonth: '月初',
    endOfMonth: '月底',
    firstOfYear: '年初',
    custom: '自定义',
    date: '日',
    week: '周',
    month: '月',
    year: '年',
    before: '前',
    after: '后',
    preview: '预览',
    set: '设置',

    cweek: '本周',
    lweek: '上周',
    cmonth: '本月',
    cquarter: '本季',
    tquarter: '本季度',
    lquarter: '上季',
    cyear: '本年'
  },
  dynamic_year: {
    fix: '固定年份',
    dynamic: '动态年份',
    current: '今年',
    last: '去年'
  },
  dynamic_month: {
    fix: '固定年月',
    dynamic: '动态年月',
    current: '当月',
    last: '上月',
    firstOfYear: '当年首月',
    sameMonthLastYear: '去年同月'
  },
  data_export: {
    export_center: '数据导出中心',
    export_info: '查看进度，进行下载',
    exporting: '后台导出中,可前往',
    del_all: '全部删除',
    export_failed: '导出失败',
    export_from: '导出来源',
    export_obj: '导出对象',
    export_time: '导出时间',
    sure_del_all: '确定删除全部导出记录吗？',
    sure_del: '确定删除该导出记录吗？',
    no_failed_file: '暂无失败文件',
    no_file: '暂无文件',
    no_task: '暂无任务',
    download_all: '全部下载',
    download: '下载'
  },
  driver: {
    driver: '驱动',
    please_choose_driver: '请选择驱动',
    mgm: '驱动管理',
    exit_mgm: '退出驱动管理',
    add: '添加驱动',
    modify: '修改',
    show_info: '驱动信息',
    file_name: '文件名',
    version: '版本',
    please_set_driverClass: '请指定驱动类',
    please_set_supportVersions: '请输入支持的数据库大版本',
    supportVersions: '支持版本'
  },
  login: {
    welcome: '欢迎使用',
    btn: '登录',
    username_format: "1-25位字母数字或者._-:{'@'}且以字母或数字开头",
    pwd_format: '密码长度在5-15',
    default_login: '默认',
    ldap_login: 'LDAP 登录',
    account_login: '账号登录',
    other_login: '其他登录方式',
    pwd_invalid_error: '密码已过期请联系管理员修改或重置',
    pwd_exp_tips: '密码在 {0} 天后过期，请尽快修改密码',
    qr_code: '二维码',
    platform_disable: '{0}设置未开启！',
    input_account: '请输入账号',
    redirect_2_auth: '正在跳转至 {0} 认证，{1} 秒...'
  },
  component: {
    columnList: '列表项',
    selectInfo: '请选择列表中要展示的信息',
    allSelect: '全选'
  },
  system: {
    user: '用户',
    role: '角色',
    addUser: '@:common.add@:system.user',
    click_to_show: '点击显示',
    click_to_hide: '点击隐藏',
    basic_settings: '基础设置',
    login_settings: '登录设置',
    and_0_seconds: '0分0秒',
    time_0_seconds: '分钟（执行时间：0秒）',
    and_0_seconds_de: '小时（执行时间：0分0秒）',
    fonts_before_deleting: '请先将其他字体设置为默认字体，再进行删除。',
    sure_to_delete: '当前字体被删除后，使用该字体的组件将会使用默认字体，确定删除?',
    setting_successful: '设置成功',
    font_management: '字体管理',
    search_font_name: '搜索字体名称',
    a_new_font: '新建字体',
    add_font: '添加字体',
    default_font: '默认字体',
    system_built_in: '系统内置',
    update_time: '更新时间：',
    font_file: '字库文件：',
    upload_font_file: '上传字库文件',
    replace_font_file: '替换字库文件',
    as_default_font: '设为默认字体',
    the_font_name: '请输入字体名称',
    in_ttf_format: '只支持上传ttf格式的字体文件!',
    character_length_1_50: '字符长度1-50',
    upload_font_file_de: '请上传字库文件',
    font_name: '字体名称',
    font_file_de: '字库文件',
    be_the_same: '新旧密码不能相同',
    twice_are_inconsistent: '两次输入的密码不一致',
    log_in_again: '修改成功，请重新登录',
    original_password: '原始密码',
    the_original_password: '请输入原始密码',
    new_password: '新密码',
    the_new_password: '请输入新密码',
    confirm_password: '确认密码',
    the_confirmation_password: '请输入确认密码',
    map_settings: '地图设置',
    engine_settings: '引擎设置',
    normal_login: '普通登录',
    to_take_effect: '请求超时时间(单位：秒，注意：保存后刷新浏览器生效)',
    and_platform_docking: '作用域包括认证设置和平台对接',
    not_enabled: '未开启',
    default_organization: '默认组织',
    normal_role: '普通角色',
    engine_type: '引擎类型',
    on_the_left: '请在左侧选择区域',
    region_code: '区域代码',
    superior_region: '上级区域',
    coordinate_file: '坐标文件',
    delete_this_node: '确定删除此节点吗',
    at_the_end:
      '国家代码由三位数字组成，省、市、区县、乡镇代码由两位数字组成；非国家区域需要再后面补0',
    non_zero_three_digit_number: '请输入非0的三位数字',
    or_11_digits: '请输入9或11位数字',
    contact_the_administrator: '执行失败请联系管理员',
    upload_json_files: '只能上传json文件',
    maximum_upload_200m: '最大上传200M',
    geographic_information: '地理信息',
    superior_region_first: '请先选择上级区域',
    region_name: '区域名称',
    appearance_configuration: '外观配置',
    platform_display_theme: '平台显示主题',
    navigation_background_color: '顶部导航背景色',
    dark_color: '暗色',
    light_color: '浅色',
    theme_color: '主题色',
    default_blue: '默认 (蓝色) ',
    custom_color_value: '自定义色值',
    platform_login_settings: '平台登录设置',
    page_preview: '页面预览',
    restore_default: '恢复默认',
    platform: '默认为 {msg} 平台界面，支持自定义设置',
    supports_custom_settings: '默认为 {msg} 登录界面，支持自定义设置',
    replace_image: '替换图片',
    website_name: '网站名称',
    web_page_tab: '显示在网页 Tab 的平台名称',
    under_product_logo: '产品 Logo 下的 Slogan',
    footer: '页脚',
    footer_content: '页脚内容',
    platform_settings: '平台设置',
    top_navigation_logo: '顶部导航 Logo',
    not_exceeding_200kb:
      '顶部导航菜单显示的 Logo；建议尺寸 134 x 34，支持JPG、PNG，大小不超过 200KB',
    help_document: '帮助文档',
    ai_assistant_button: 'AI 助手按钮',
    copilot_button: 'Copilot 按钮',
    document_button: '文档按钮',
    about_button: '关于按钮',
    mobile_login_settings: '移动端登录设置',
    user_login: '用户登录',
    in_user_name: '请填写用户名',
    fill_in_password: '请填写密码',
    supports_custom_settings_de: '默认为 {msg} 移动端登录界面，支持自定义设置',
    login_logo: '登录 Logo',
    not_exceeding_200kb_de:
      '登录页面右侧 Logo，建议尺寸 120*30，支持 JPG、PNG、SVG，大小不超过 200KB',
    login_background_image: '登录背景图',
    not_exceeding_5m:
      '左侧背景图，矢量图建议尺寸375*480，位图建议尺寸1125*1440；支持 JPG、PNG、SVG，大小不超过5M',
    hidden_in_iframe: 'Iframe中隐藏',
    available_to_everyone: '人人可用的开源 BI 工具',
    the_website_name: '请输入网站名称',
    enter_the_slogan: '请输入Slogan',
    the_help_document: '请输入帮助文档',
    assistant: '请选择是否展示AI助手',
    to_display_copilot: '请选择是否展示Copilot',
    display_the_document: '请选择是否展示文档',
    display_the_about: '请选择是否展示关于',
    website_logo: '网站 Logo',
    not_exceeding_200kb_de_:
      '顶部网站显示的 Logo，建议尺寸 48 x 48，支持 JPG、PNG、SVG，大小不超过 200KB',
    not_exceeding_200kb_de_right:
      '登录页面右侧 Logo，建议尺寸 204 x 52，支持 JPG、PNG、SVG，大小不超过 200KB',
    not_exceeding_5m_de:
      '左侧背景图，矢量图建议尺寸 640 x 900，位图建议尺寸 1280 x 1800；支持 JPG、PNG、SVG，大小不超过 5M',
    tab: '页签',
    incorrect_please_re_enter: '回调域名格式错误，请重新输入',
    cas_settings: 'CAS设置',
    callback_domain_name: '回调域名',
    authentication_settings: '认证设置',
    be_turned_on: '测试连接有效后，可开启',
    platform_information_first: '请先保存平台信息',
    for_example: '如：{\'{\'}"account":"uid","name":"cn","email":"mail"{\'}\'}',
    in_json_format: '请输入json格式',
    ldap_settings: 'LDAP设置',
    ldap_address: 'LDAP地址',
    such_as_ldap: 'LDAP地址（如 ldap://127.0.0.1:389）',
    bind_dn: '绑定DN',
    user_ou: '用户OU',
    separate_each_ou: 'OU（使用|分割各OU）',
    user_filter: '用户过滤器',
    such_as_uid: "过滤器 [可能的选项是cn或uid或sAMAccountName={'{'}0{'}'}, 如：(uid={'{'}0{'}'})]",
    ldap_attribute_mapping: 'LDAP属性映射',
    incorrect_please_re_enter_de: 'url格式错误，请重新输入',
    oauth2_settings: 'OAuth2设置',
    authorization_end_address: '授权端地址',
    token_end_address: 'Token 端地址',
    information_end_address: '用户信息端地址',
    connection_range: '连接范围',
    client_id: '客户端 ID',
    client_key: '客户端密钥',
    callback_address: '回调地址',
    field_mapping: '字段映射',
    oauth2name:
      '例如：{\'{\'}"account": "oauth2Account", "name": "oauth2Name", "email": "email"{\'}\'}',
    oidc_settings: 'OIDC设置',
    test_mail_recipient: '仅用来作为测试邮件收件人',
    to_enable_ssl: '如果SMTP端口是 465 ，通常需要启用SSL',
    to_enable_tsl: '如果SMTP端口是 587 ，通常需要启用TSL',
    wrong_please_re_enter: '地址格式错误，请重新输入',
    create_embedded_application: '创建嵌入式应用',
    edit_embedded_application: '编辑嵌入式应用',
    application_name: '应用名称',
    cross_domain_settings: '跨域设置',
    embedded_secret_len_change: '密钥长度变化，密钥即将重置，是否确认',
    embedded_management: '嵌入式管理',
    embedded_del_confirm: '确定删除 {0} 个应用吗？',
    embedded_search_placeholder: '通过应用名称、APP ID、跨域设置搜索',
    to_5_applications: '最多支持创建 {0} 个应用',
    update_app_secret: '确定更新APP Secret吗？',
    operate_with_caution: '重置后现有的APP Secret将会失效，请谨慎操作。',
    no_application: '暂无应用',
    delete_this_application: '确定删除此应用吗',
    platform_connection: '平台对接',
    dingtalk_settings: '钉钉设置',
    enabled: '已开启',
    close: '已关闭',
    can_enable_it: '测试连接有效后，可开启',
    access: '接入',
    feishu_settings: '飞书设置',
    international_feishu_settings: '国际飞书设置',
    international_feishu: '国际飞书',
    enterprise_wechat_settings: '企业微信设置',
    enterprise_wechat: '企业微信',
    plugin_management: '插件管理',
    search_plugin_name: '搜索插件名称',
    local_installation: '本地安装',
    relevant_content_found: '没有找到相关内容',
    no_plugins_yet: '暂无插件',
    installation_time: '安装时间：',
    developer: '开发者：',
    update_the_plugin: '确认更新该插件吗？',
    to_take_effect_update: '更新并重启服务器之后才能生效',
    uninstall_the_plugin: '确认卸载该插件吗？',
    to_take_effect_de: '卸载并重启服务器之后才能生效',
    uninstall_successful: '卸载成功',
    update_successful: '更新成功',
    installation_successful: '安装成功',
    can_be_uploaded: '只能上传jar文件',
    to_change_it: '变量类型修改后，变量值将清空，确认要修改吗？',
    add_variable: '添加变量',
    edit_variable: '编辑变量',
    variable_name: '变量名称',
    variable_type: '变量类型',
    system_built_in_variable: '系统内置变量',
    custom_variable: '自定义变量',
    account: '账户',
    delete_this_variable: '确定删除该变量吗？',
    this_variable_value: '确定删除该变量值吗？',
    variable_list: '变量列表',
    add_variable_value: '添加变量值',
    search_variable_value: '搜索变量值',
    variable_value: '变量值',
    set_variable_value: '设置变量值',
    the_minimum_value: '请输入最小值',
    the_maximum_value: '请输入最大值',
    the_minimum_date: '请选择最小日期',
    the_maximum_date: '请选择最大日期',
    on_the_left_p: '请选择左侧变量',
    edit_variable_value: '编辑变量值',
    secret_length: '密钥长度',
    custom_area: '自定义区域',
    custom_area_tip: '仅对中国的省份、直辖市，支持自定义地理区域',
    add_area: '添加区域',
    area_name: '区域名称',
    area_scope: '区域范围',
    operation: '操作',
    sub_area_tip: '请选择省份或直辖市',
    delete_custom_area_tip: '该操作会导致使用了自定义区域的地图无法正常展示，确定删除？',
    please_select_area: '请选择区域',
    delete_custom_sub_area_tip: '确定删除该自定义区域？'
  },
  components: {
    dashboard_style: '仪表板风格',
    overall_configuration: '整体配置',
    dashboard_background: '仪表板背景',
    chart_color: '图表配色',
    advanced_style_settings: '高级样式设置',
    length_1_64_characters: '名称字段长度1-64个字符',
    current_page_first: '请先保存当前页面',
    from_other_organizations: '已切换至新组织，无权保存其他组织的资源',
    close_the_page: '关闭页面',
    sure_to_exit: '当前的更改尚未保存，确定退出吗？',
    add_components_first: '当前仪表板为空，请先添加组件',
    rich_text: '富文本',
    media: '媒体',
    dashboard_configuration: '仪表板配置',
    to_mobile_layout: '切换至移动端布局',
    complete: '完成',
    pager_color: '分页器配色',
    title_horizontal_position: '标题水平位置',
    title_display_position: '标题显示位置',
    title_color: '标题颜色',
    label_color: '标签颜色',
    input_box_style: '输入框样式',
    overall_refresh: '整体刷新',
    previews_take_effect: '仅公共链接生效',
    jump_icon_color: '联动、钻取、跳转的图标颜色',
    level_display_color: '钻取层级展示颜色',
    a_new_theme: '新建主题',
    upload_a_cover: '请上传封面',
    edit_theme: '编辑主题',
    cover: '封面',
    to_delete_: '确定删除[{0}]吗?',
    to_delete_variable: '确定删除 {0} 吗?'
  },
  user: {
    change_password: '修改密码',
    select_users: '请选择用户',
    account: '账号',
    name: '姓名',
    role: '角色',
    state: '用户状态',
    default_pwd: '默认密码',
    confirm_delete: '确定删除该用户吗？',
    add_title: '添加用户',
    edit_title: '编辑用户',
    user_id: '用户',
    user_id_empty: '请输入精准用户ID/账号',
    search_placeholder: '搜索姓名、账号、邮箱',
    batch_del: '批量删除',
    selection_info: '已选 {0} 项',
    clear_button: '清空',
    confirm_batch_delete: '确定删除{0}个用户吗？',
    reset_pwd: '重置密码',
    reset_confirm: '是否恢复为初始密码？',
    reset_success: '重置成功',
    modify_cur_pwd: '修改当前用户密码后需要重新登录',
    switch_success: '切换成功',
    user_name_pattern_error: "只允许数字字母以及{'@'}._-且必须数字或字母开头",
    pwd_pattern_error: '8-20位且至少一位大写字母、小写字母、数字、特殊字符',
    special_characters_are_not_supported: '不允许特殊字符',
    phone_format: '请填写正确格式手机号',
    email_format_is_incorrect: '请填写正确格式邮箱',
    enable_success: '已启用',
    disable_success: '已禁用',
    feishu: '飞书',
    dingtalk: '钉钉',
    wechat_for_business: '企业微信',
    international_feishu: '国际飞书',
    user_management: '用户管理',
    cannot_be_modified: '系统管理员状态不可修改',
    cannot_be_modified_de: '不能修改当前用户状态',
    has_been_disabled: '用户已被禁用',
    selected_user: '已选: {msg} 个用户',
    cannot_be_empty: '变量不能为空！',
    set_variable_value: '请设置变量值：',
    be_an_integer: '变量值必须为整数：',
    be_less_than: '不能小于：',
    be_greater_than: '不能大于：',
    than_start_time: '，不能小于开始时间：',
    than_end_time: '，不能大于结束时间：',
    variable: '变量',
    variable_value: '变量值',
    enter_a_value: '请输入数值',
    contact_the_administrator: '执行失败请联系管理员',
    data_import_successful: '导入数据成功',
    imported_1_data: '成功导入数据 {msg} 条',
    import_1_data: '，导入失败 {msg} 条 ',
    can: '可',
    download_error_report: '下载错误报告',
    modify_and_re_import: '，修改后重新导入',
    return_to_view: '返回查看',
    continue_importing: '继续导入',
    data_import_failed: '部分数据导入失败',
    data_import_failed_de: '数据导入失败'
  },
  userimport: {
    buttonText: '批量导入',
    dialogTitle: '批量上传',
    success: '导入成功',
    placeholder: '点击选择文件',
    defaultTip: '仅支持xlsx、xls格式的文件',
    errorTip: '上传失败：文件中存在不合规数据，如需查看详情，',
    downTip: '下载模板',
    uploadAgain: '再次上传',
    backUserGrid: '返回用户列表',
    sure: '确定',
    cancel: '取消',
    repeatDown: '请勿重复下载',
    limitMsg: '文件最大10M',
    suffixMsg: '只支持.xlsx|.xls结尾的文件',
    exceedMsg: '只能上传一个文件',
    templateError: '用户模板错误',
    first_please: '请先',
    fill_and_upload: '，按要求填写后上传',
    import: '导入'
  },
  role: {
    add_title: '添加角色',
    edit_title: '编辑角色',
    role_title: '角色列表',
    name: '角色名称',
    type: '角色类型',
    desc: '角色描述',
    average_role: '普通用户',
    org_admin: '组织管理员',
    confirm_delete: '确认删除该角色吗？',
    delete_tips:
      '<div id="u7755_text" class="text" style="font-size: 12px;"><p><span style="color:#F59A23;">友情提示，角色被删除后，归属于角色的用户将做如下处理：</span></p><p><span style="color:#7F7F7F;">1、用户拥有当前组织的其他角色，那么角色被删除后，将用户从该角色中移除。</span></p><p><span style="color:#7F7F7F;">2、该角色是用户在当前组织下拥有的唯一角色，但用户拥有其他组织下的角色，那么角色被删除后，用户也将从当前组织中移除。</span></p><p><span style="color:#7F7F7F;">3、该角色是用户在当前组织下拥有的唯一角色，用户在系统的其他组织下也没有任何角色，那么角色被删除后，用户也将从当前系统中删除。</span></p><p><span style="color:#7F7F7F;"><br></span></p></div>',
    confirm_unbind_user: '确定将该用户从角色中移除吗？',
    clear_in_system:
      '友情提示，从当前角色移除后，该用户已没有任何组织的任何角色，用户将从系统中删除。',
    clear_in_org:
      '友情提示，从当前角色移除后，该用户已没有当前组织的任何角色，将从当前组织中移除。',
    add_user: '为角色添加用户({0})',
    unbind_success: '移除成功',
    bind_success: '绑定成功',
    bound_user: '已添加用户',
    option_user: '可添加用户',
    org_user_title: '添加组织用户',
    out_user_title: '添加外部用户',
    search_user: '查找用户',
    search_one: '查找到一个用户',
    search_no: '暂无结果，用户可能不存在！',
    system_role: '系统内置角色',
    custom_role: '自定义角色',
    system: '系统',
    user_search_placeholder: '搜索姓名、账号',
    manager: '管理员',
    staff: '用户',
    system_role_edit_tips: '系统角色无法编辑',
    system_role_del_tips: '系统角色无法删除',
    empty_description: '请先选择左侧角色'
  },
  org: {
    resource_migration: '资源迁移',
    migration_type: '迁移类型',
    migrate_resources_only: '仅迁移资源',
    and_authorization_related: '迁移资源及授权相关',
    target_organization: '目标组织',
    target_directory: '目标目录',
    resource_type: '资源类型',
    user_dimension: '按用户配置',
    resource_dimension: '按资源配置',
    org_title: '组织管理',
    org_move: '组织迁移',
    add: '添加组织',
    name: '组织名称',
    sub_count: '下属组织数',
    search_placeholder: '请输入名称搜索',
    add_sub: '添加子组织',
    edit: '编辑组织',
    parent: '上级组织',
    default_cannot_move: '默认组织不能删除',
    cannot_delete: '无法删除',
    confirm_delete: '确定删除该组织吗？',
    delete_children_first: '请先删除子组织后，再删除当前组织',
    confirm_content: '友情提示，组织被删除后，组织下的资源将作为游离资源处理',
    give_up_resource: '放弃资源，直接删除',
    move_resource_first: '先迁移资源',
    default_parent_tips: '(默认当前组织)',
    admin_parent_tips: '(默认根组织)',
    please_login_per_changed: '当前用户权限已变更，请重新登录'
  },
  auth: {
    permission_configuration: '权限配置',
    was_not_obtained: '未获取到资源节点',
    search_name: '搜索名称',
    loading: '加载中···',
    on_the_left: '请选择左侧节点',
    sysParams_type: {
      user_id: '账号',
      user_name: '姓名',
      user_source: '用户来源',
      user_label: '用户标签',
      user_email: '邮箱',
      dept: '组织',
      role: '角色'
    },
    user: '用户',
    role: '角色',
    resource: '资源权限',
    menu: '菜单权限',
    panel: '仪表板',
    screen: '数据大屏',
    dataset: '数据集',
    datasource: '数据源',
    all_types: '全部类型',
    empty_desc: '请选择用户/角色以及资源类型',
    row_column: '行列权限设置',
    row_permission: '行权限规则',
    enable_row: '启用行权限',
    white_list: '白名单',
    white_user_not: '以上权限规则对白名单用户不生效',
    organization_or_role: '请选择组织或角色',
    column_permission: '列权限规则',
    enable_column: '启用列权限',
    search_by_field: '通过字段名称搜索',
    add_condition: '添加条件',
    add_relationship: '添加关系',
    filter_fields: '筛选字段',
    select_filter_fields: '请选择筛选字段',
    enter_keywords: '请输关键字',
    screen_method: '筛选方式',
    select: '请选择',
    fixed_value: '固定值',
    default_method: '默认条件',
    select_all: '全 选',
    added: '已添加',
    manual_input: '手工输入',
    please_fill: '请一行填一个，最多添加500个,识别录入时会自动过滤重复的选项和已经添加过的选项',
    close: '关 闭',
    add: '添 加',
    sure: '确 定',
    uncommitted_tips: '有未提交的权限变更，是否提交？',
    use: '使用',
    check: '查看',
    export: '导出',
    manage: '管理',
    auth: '授权',
    resource_name: '资源名称',
    menu_name: '菜单名称',
    from_role: '继承自以下角色：',
    auth_alone: '单独授权',
    org_role_empty: '组织管理员已拥有所有资源的权限，无需再授权',
    user_role_empty: '该用户是组织管理员，已拥有所有资源的权限，无需再授权',
    sysParams: '系统变量',
    set_rules: '设置规则',
    inner_role_tips: '系统内置角色，权限无法编辑'
  },
  datasource: {
    datasource: '数据源',
    create: '新建数据源',
    config: '数据源配置',
    table: '数据源表',
    table_name: '表名',
    remark: '备注',
    column_name: '字段名',
    field_type: '字段类型',
    field_description: '字段备注',
    dl: '数据湖',
    other: '其他',
    local_file: '文件',
    select_ds_type: '选择数据源类型',
    select_ds: '选择数据源',
    ds_info: '录入数据源信息',
    sync_info: '数据同步设置',
    input_name: '请输入名称',
    input_limit_2_25: '{0}-{1}字符',
    input_limit_2_50: '2-50字符',
    input_limit_2_64: '2-64字符',
    input_limit_1_64: '1-64字符',
    data_source_configuration: '数据源配置',
    data_source_table: '数据源表',
    auth_method: '认证方式',
    passwd: '用户名密码',
    kerbers_info: '请确保 krb5.Conf、Keytab Key，已经添加到路径：/opt/dataease2.0/conf',
    client_principal: 'Client Principal',
    keytab_Key_path: 'Keytab Key Path',
    please_select_left: '请从左侧选择',
    show_info: '数据源信息',
    type: '类型',
    please_choose_type: '请选择数据源类型',
    please_choose_data_type: '请选择计算模式',
    data_base: '数据库名称',
    user_name: '用户名',
    password: '密码',
    host: '主机名/IP地址',
    doris_host: 'Doris 地址',
    query_port: 'Query Port',
    http_port: 'Http Port',
    port: '端口',
    datasource_url: '地址',
    please_input_datasource_url: '请输入 Elasticsearch 地址，如: http://es_host:es_port',
    please_input_data_base: '请输入数据库名称',
    please_input_jdbc_url: '请输入 JDBC 连接',
    please_select_oracle_type: '选择连接类型',
    please_input_user_name: '请输入用户名',
    please_input_password: '请输入密码',
    please_input_host: '请输入主机',
    please_input_url: '请输入URL地址',
    please_input_port: '请输入端口',
    please_input_be_port: '请输入BE端口',
    modify: '编辑数据源',
    copy: '复制数据源',
    validate_success: '校验成功',
    validate_failed: '校验失败',
    validate: '校验',
    search_by_name: '根据名称搜索',
    delete_warning: '确定要删除吗?',
    input_limit: '{num}字符',
    oracle_connection_type: '服务名/SID',
    oracle_sid: 'SID',
    oracle_service_name: '服务名',
    get_schema: '获取 Schema',
    get_tables: '获取数据表',
    get_views: '获取视图',
    view: '视图',
    schema: 'Schema',
    charset: '字符集',
    targetCharset: '目标字符集',
    please_choose_schema: '请选择数据库 Schema',
    please_choose_charset: '请选择数据库字符集',
    please_choose_targetCharset: '请选择目标字符集',
    edit_datasource_msg: '修改数据源信息，可能会导致该数据源下的数据集不可用，确认修改？',
    repeat_datasource_msg: '已经存在相同配置的数据源信息, ',
    confirm_save: '确认保存?',
    in_valid: '无效数据源',
    initial_pool_size: '初始连接数',
    min_pool_size: '最小连接数',
    max_pool_size: '最大连接数',
    max_idle_time: '最大空闲(秒)',
    bucket_num: 'Bucket 数量',
    replication_num: '副本数量',
    please_input_bucket_num: '请输入 Bucket 数量',
    please_input_replication_num: '请输入副本数量',
    acquire_increment: '增长数',
    connect_timeout: '连接超时(秒)',
    please_input_initial_pool_size: '请输入初始连接数',
    please_input_min_pool_size: '请输入最小连接数',
    please_input_max_pool_size: '请输入最大连接数',
    please_input_max_idle_time: '请输入最大空闲(秒)',
    please_input_acquire_increment: '请输入增长数',
    please_input_query_timeout: '请输入查询超时',
    please_input_connect_timeout: '请输入连接超时(秒)',
    no_less_then_0: '高级设置中的参数不能小于零',
    port_no_less_then_0: '端口不能小于零',
    priority: '高级设置',
    data_mode: '数据模式',
    direct: '直连模式',
    extract: '抽取模式',
    all_compute_mode: '直连、抽取模式',
    extra_params: '额外的 JDBC 连接字符串',
    jdbcUrl: 'JDBC 连接',
    please_input_dataPath: '请输入 JsonPath 数据路径',
    show_api_data: '查看API数据结构',
    warning: '包含无效数据表',
    data_table: '数据表',
    data_table_name: '数据表名称',
    method: '请求方式',
    url: 'URL',
    add_api_table: '添加API数据表',
    edit_api_table: '编辑API数据表',
    base_info: '基础信息',
    column_info: '数据结构',
    request: '请求',
    isUseJsonPath: '是否指定JsonPath',
    path_all_info: '请填入完整地址',
    jsonpath_info: '请填入JsonPath',
    req_param: '请求参数',
    headers: '请求头',
    query_param: 'QUERY参数',
    query_info: '地址栏中跟在？后面的参数,如: updateapi?id=112',
    key: '键',
    value: '值',
    data_path: '提取数据',
    data_path_desc: '请用JsonPath填写数据路径',
    body_form_data: 'form-data',
    body_x_www_from_urlencoded: 'x-www-form-urlencoded',
    body_json: 'json',
    body_xml: 'xml',
    body_raw: 'raw',
    request_body: '请求体',
    auth_config: '认证配置',
    auth_config_info: '请求需要进行权限校验',
    verified: '认证',
    verification_method: '认证方式',
    username: '用户名',
    api_table_not_empty: 'API 数据表不能为空',
    has_repeat_name: 'API 数据表名称重复',
    has_repeat_field_name: '字段名重复，请修改后再选择',
    primary_key_change: '主键不能改变:',
    primary_key_length: '主键必须设置长度: ',
    api_field_not_empty: '字段不能为空',
    file_not_empty: '文件不能为空',
    success_copy: '复制成功',
    valid: '有效',
    invalid: '无效',
    api_step_1: '连接API',
    api_step_2: '提取数据',
    _ip_address: '请输入主机名/IP地址',
    display_name: '显示名称',
    connection_mode: '连接方式',
    driver_file: '驱动文件',
    edit_driver: '编辑驱动',
    driver_name: '驱动名称',
    drive_type: '驱动类型',
    add_driver: '添加驱动',
    diver_on_the_left: '请在左侧选择驱动',
    no_data_table: '暂无数据表',
    on_the_left: '请在左侧选择数据源',
    create_dataset: '创建数据集',
    table_description: '表备注',
    relational_database: '关系型数据库',
    data_warehouse_lake: '数仓/数据湖',
    non_relational_database: '非关系型数据库',
    all: '所有',
    this_data_source: '确定删除该数据源吗？',
    delete_this_dataset: '确定删除该数据集吗？',
    edit_folder: '编辑文件夹',
    click_to_check: '点击去查看血缘关系',
    please_select: '请选择',
    delete_this_item: '是否要删除此项？',
    can_be_uploaded: '仅支持上传JAR格式的文件',
    query_timeout: '查询超时',
    add_data_source: '添加数据源',
    delete_this_driver: '确定删除该驱动吗？',
    basic_info: '基本信息',
    data_preview: '预览数据',
    update_type: '更新方式',
    all_scope: '全量更新',
    add_scope: '增量更新',
    select_data_time: '选择日期时间',
    execute_rate: '执行频率',
    execute_once: '立即执行',
    simple_cron: '简单重复',
    manual: '手动更新',
    cron_config: '表达式设定',
    no_limit: '无限制',
    set_end_time: '设定结束时间',
    exec_time: '执行时间',
    start_time: '开始时间',
    end_time: '结束时间',
    parse_filed: '解析字段',
    set_key: '设为主键',
    length: '字段长度',
    field_rename: '重命名',
    select_type: '选择数据源类型',
    sync_table: '同步指定表',
    req_completed: '请求成功',
    sync_rate: '更新频率',
    has_same_ds: '存在相同配置数据源，确认保存？',
    app_token: 'app_token',
    input_app_token: '请输入app_token',
    table_id: 'table_id',
    input_table_id: '请选择数据表',
    view_id: 'view_id',
    input_view_id: '请选择视图',
    remote_excel_url: '远程 Excel/CSV 地址',
    remote_excel_url_placeholder:
      '请输入远程 Excel/CSV 地址，例如 ftp://*************/files/data.xlsx',
    remote_excel_url_empty: '请输入远程 Excel/CSV 地址',
    load_data: '加载数据'
  },
  chart: {
    align: '对齐方式',
    reset: '重置',
    chart_refresh_tips: '图表刷新设置优先于仪表板刷新设置',
    '1-trend': '趋势',
    '2-state': '状态',
    '3-rank': '排名',
    '4-location': '位置',
    '5-weather': '天气',
    chinese: '中文',
    mark_field: '字段',
    mark_value: '值',
    function_style: '功能型样式',
    condition_style: '标记样式',
    longitude: '经度',
    latitude: '纬度',
    longitude_and_latitude: '经度纬度',
    gradient: '渐变',
    layer_controller: '指标切换',
    show_zoom: '显示缩放按钮',
    button_color: '按钮颜色',
    button_background_color: '按钮背景色',
    chart_background: '组件背景',
    date_format: '请选择日期解析格式',
    solid_color: '纯色',
    split_gradient: '分离渐变',
    continuous_gradient: '连续渐变',
    map_center_lost: '图形缺失中心点centroid或center属性，请补全后再试',
    margin_model: '模式',
    margin_model_auto: '自动',
    margin_model_absolute: '绝对',
    margin_model_relative: '相对',
    margin_placeholder: '请输入0-100数字',
    margin_absolute_placeholder: '请输入0-40数字',
    rich_text_view_result_tips: '富文本只选取第一条结果',
    rich_text_view: '富文本图表',
    view_reset: '图表重置',
    view_reset_tips: '放弃对图表的修改？',
    export_img: '导出图片',
    title_repeat: '当前标题已存在',
    save_snapshot: '保存缩略图',
    datalist: '图表',
    add_group: '添加分组',
    add_scene: '添加场景',
    group: '分组',
    scene: '场景',
    delete: '删除',
    move_to: '移动到',
    rename: '重命名',
    tips: '提示',
    confirm_delete: '确认删除',
    delete_success: '删除成功',
    confirm: '确认',
    cancel: '取消',
    search: '搜索',
    back: '返回',
    add_table: '添加数据集',
    process: '进度',
    add_chart: '添加图表',
    db_data: '数据库数据集',
    sql_data: 'SQL数据集',
    excel_data: 'Excel数据集',
    custom_data: '自定义数据集',
    pls_slc_tbl_left: '请从左侧选图表',
    add_db_table: '添加数据库数据集',
    add_api_table: '添加API数据集',
    pls_slc_data_source: '请选择数据源',
    table: '表',
    edit: '编辑',
    create_view: '创建图表',
    data_preview: '数据预览',
    dimension: '维度',
    quota: '指标',
    dimension_abb: '维度',
    quota_abb: '指标',
    column_quota: '柱指标',
    line_quota: '线指标',
    time_dimension_or_quota: '时间维度或指标',
    aggregate_time: '聚合时间纬度',
    title: '标题',
    show: '显示',
    chart_type: '图表类型',
    shape_attr: '图形属性',
    module_style: '组件样式',
    result_filter: '过滤器',
    chart_mix_label_only_left: '仅针对柱状图设置有效',
    x_axis: '横轴',
    y_axis: '纵轴',
    chart: '图表',
    close: '关闭',
    summary: '汇总方式',
    fast_calc: '快速计算',
    sum: '求和',
    count: '计数',
    avg: '平均',
    max: '最大值',
    min: '最小值',
    stddev_pop: '标准差',
    var_pop: '方差',
    quick_calc: '快速计算',
    show_name_set: '编辑显示名称',
    show_name: '显示名称',
    backdrop_blur: '背景模糊',
    color: '颜色',
    color_case: '配色方案',
    pls_slc_color_case: '请选择配色方案',
    color_default: '默认',
    color_retro: '复古',
    color_future: '未来',
    color_gradual: '渐变',
    color_business: '商务',
    color_gentle: '柔和',
    color_elegant: '淡雅',
    color_technology: '科技',
    color_simple: '简洁',
    not_alpha: '不透明度',
    column_width_ratio: '柱宽比例',
    area_border_color: '地图边线',
    area_base_color: '地图区块填充',
    size: '大小',
    bar_width: '柱宽',
    bar_gap: '柱间隔',
    adapt: '自适应',
    line_width: '线宽',
    line_type: '线型',
    line_symbol: '折点',
    line_symbol_size: '折点大小',
    line_type_solid: '实线',
    line_type_dashed: '虚线',
    line_symbol_circle: '圆形',
    line_symbol_emptyCircle: '空心圆',
    line_symbol_rect: '矩形',
    line_symbol_roundRect: '圆角矩形',
    line_symbol_triangle: '三角形',
    line_symbol_diamond: '菱形',
    line_symbol_pin: '钉子',
    line_symbol_arrow: '箭头',
    line_symbol_none: '无',
    line_area: '面积',
    pie_inner_radius: '内径',
    pie_outer_radius: '外径',
    funnel_width: '宽度',
    line_smooth: '平滑折线',
    title_style: '标题样式',
    text_fontsize: '字体大小',
    text_color: '字体颜色',
    text_h_position: '水平位置',
    text_v_position: '垂直位置',
    text_position: '位置',
    text_pos_left: '左',
    text_pos_center: '中',
    text_pos_right: '右',
    text_pos_top: '上',
    text_pos_bottom: '下',
    text_italic: '字体倾斜',
    italic: '倾斜',
    orient: '方向',
    horizontal: '水平',
    vertical: '垂直',
    legend: '图例',
    legend_num: '图例数',
    shape: '形状',
    polygon: '多边形',
    circle: '圆形',
    label: '标签',
    label_position: '标签位置',
    label_bg: '标签背景',
    label_shadow: '标签阴影',
    label_shadow_color: '阴影颜色',
    label_reserve_decimal_count: '保留小数',
    content_formatter: '内容格式',
    inside: '内',
    tooltip: '提示',
    tooltip_item: '数据项',
    tooltip_axis: '坐标轴',
    formatter_plc: '内容格式为空时，显示默认格式',
    xAxis: '横轴',
    yAxis: '纵轴',
    yAxisLeft: '左纵轴',
    yAxisRight: '右纵轴',
    position: '位置',
    rotate: '角度',
    name: '名称',
    icon: '图标',
    trigger_position: '触发位置',
    asc: '升序',
    desc: '降序',
    sort: '排序',
    default: '默认',
    filter: '过滤',
    none: '无',
    background: '背景',
    border: '边角',
    border_width: '边框宽度',
    border_radius: '圆角',
    alpha: '透明度',
    add_filter: '添加过滤',
    no_limit: '无限制',
    filter_eq: '等于',
    filter_not_eq: '不等于',
    filter_lt: '小于',
    filter_le: '小于等于',
    filter_gt: '大于',
    filter_ge: '大于等于',
    filter_null: '为空',
    filter_not_null: '不为空',
    filter_empty: '空字符串',
    filter_not_empty: '非空字符串',
    filter_include: '包含',
    filter_not_include: '不包含',
    rose_type: '玫瑰图模式',
    radius_mode: '半径',
    area_mode: '面积',
    rose_radius: '圆角',
    view_name: '图表标题',
    belong_group: '所属分组',
    select_group: '选择分组',
    name_can_not_empty: '名称不能为空',
    template_can_not_empty: '请导入模板',
    custom_count: '记录数',
    table_title_fontsize: '表头字体大小',
    table_item_fontsize: '表格字体大小',
    table_header_bg: '表头背景',
    table_header_row_bg: '表头/行背景',
    table_item_bg: '表格背景',
    table_header_font_color: '表头字体',
    table_item_font_color: '表格字体',
    table_show_index: '显示序号',
    table_header_sort: '开启表头排序',
    table_show_row_tooltip: '开启行头提示',
    table_show_col_tooltip: '开启列头提示',
    table_show_cell_tooltip: '开启单元格提示',
    table_show_header_tooltip: '开启表头提示',
    table_summary: '总计',
    table_show_summary: '显示总计',
    table_summary_label: '总计标签',
    table_header_show_horizon_border: '表头横边框线',
    table_header_show_vertical_border: '表头纵边框线',
    table_cell_show_horizon_border: '单元格横边框线',
    table_cell_show_vertical_border: '单元格纵边框线',
    table_col_freeze_tip: '冻结前 n 列',
    table_row_freeze_tip: '冻结前 n 行',
    table_freeze: '冻结',
    stripe: '斑马纹',
    start_angle: '起始角度',
    end_angle: '结束角度',
    style_priority: '样式优先级',
    dashboard: '仪表板',
    dimension_color: '名称颜色',
    quota_color: '值颜色',
    dimension_font_size: '名称字体大小',
    quota_font_size: '值字体大小',
    space_split: '名称/值间隔',
    only_one_quota: '仅支持1个指标',
    only_one_result: '仅显示第1个计算结果',
    dimension_show: '名称显示',
    quota_show: '值显示',
    title_limit: '标题不能大于50个字符',
    filter_condition: '过滤条件',
    filter_field_can_null: '过滤字段必填',
    preview_100_data: '预览前100条记录',
    chart_table_normal: '汇总表',
    chart_table_info: '明细表',
    chart_card: '指标卡',
    chart_bar: '基础柱状图',
    chart_bar_stack: '堆叠柱状图',
    chart_percentage_bar_stack: '百分比柱状图',
    chart_bar_horizontal: '基础条形图',
    chart_bar_stack_horizontal: '堆叠条形图',
    chart_percentage_bar_stack_horizontal: '百分比条形图',
    chart_bar_range: '区间条形图',
    chart_bidirectional_bar: '对称条形图',
    chart_progress_bar: '进度条',
    chart_line: '基础折线图',
    chart_area_stack: '堆叠折线图',
    chart_pie: '饼图',
    chart_pie_donut: '环形图',
    chart_pie_rose: '玫瑰图',
    chart_pie_donut_rose: '玫瑰环形图',
    chart_funnel: '漏斗图',
    chart_sankey: '桑基图',
    chart_radar: '雷达图',
    chart_gauge: '仪表盘',
    chart_map: '地图',
    chart_bubble_map: '气泡地图',
    dateStyle: '日期显示',
    datePattern: '日期格式',
    y: '年',
    y_M: '年月',
    y_Q: '年季度',
    y_W: '年周',
    y_M_d: '年月日',
    M_d: '月日',
    M: '月',
    d: '日',
    H: '时',
    H_m: '时分',
    H_m_s: '时分秒',
    y_M_d_H: '年月日时',
    y_M_d_H_m: '年月日时分',
    y_M_d_H_m_s: '年月日时分秒',
    date_sub: 'yyyy-MM-dd',
    date_split: 'yyyy/MM/dd',
    chartName: '新建图表',
    chart_show_error: '无法正常显示',
    chart_error_tips: '获取数据异常，如有疑问请联系管理员，',
    chart_show_error_info: '查看异常原因',
    title_cannot_empty: '标题不能为空',
    table_title_height: '表头行高',
    table_item_height: '表格行高',
    text: '文本',
    axis_show: '轴线显示',
    axis_nameShow: '轴名称显示',
    axis_color: '轴线颜色',
    axis_width: '轴线宽度',
    axis_type: '轴线类型',
    grid_show: '网格线显示',
    grid_color: '网格线颜色',
    grid_width: '网格线宽度',
    grid_type: '网格线类型',
    axis_type_solid: '实线',
    axis_type_dashed: '虚线',
    axis_type_dotted: '点',
    axis_label_show: '标签显示',
    axis_label_color: '标签颜色',
    axis_label_fontsize: '标签大小',
    text_style: '字体样式',
    bolder: '加粗',
    change_ds: '更换数据集',
    change_ds_tip: '提示：更换数据集将导致字段发生变化，需重新制作图表',
    axis_name_color: '名称颜色',
    axis_name_fontsize: '名称字体',
    pie_label_line_show: '引导线',
    outside: '外',
    center: '中心',
    split: '轴线',
    axis_line: '轴线',
    axis_label: '轴标签',
    label_fontsize: '标签大小',
    split_line: '分割线',
    split_color: '分割颜色',
    shadow: '阴影',
    condition: '过滤值',
    filter_value_can_null: '过滤值不能为空',
    filter_like: '包含',
    filter_not_like: '不包含',
    filter_in: '属于',
    filter_not_in: '不属于',
    color_light: '明亮',
    color_classical: '经典',
    color_fresh: '清新',
    color_energy: '活力',
    color_red: '火红',
    color_fast: '轻快',
    color_spiritual: '灵动',
    chart_details: '图表明细',
    export: '导出',
    details: '明细',
    image: '图片',
    export_details: '导出明細',
    chart_data: '数据',
    chart_style: '样式',
    drag_block_type_axis: '类别轴',
    drag_block_type_axis_left: '左子类别',
    drag_block_type_axis_right: '右子类别',
    drag_block_type_axis_start: '源',
    drag_block_type_axis_end: '目的',
    drag_block_value_axis: '值轴',
    drag_block_value_start: '开始值',
    drag_block_value_end: '结束值',
    drag_block_value_axis_left: '左值轴',
    drag_block_value_axis_right: '右值轴',
    drag_block_table_data_column: '数据列',
    drag_block_pie_angle: '扇区角度',
    drag_block_pie_label: '扇区标签',
    drag_block_pie_radius: '扇区半径',
    drag_block_gauge_angel: '指针角度',
    drag_block_label_value: '值',
    drag_block_funnel_width: '漏斗层宽',
    drag_block_funnel_split: '漏斗分层',
    drag_block_radar_length: '分支长度',
    drag_block_radar_label: '分支标签',
    map_range: '地图范围',
    select_map_range: '请选择地图范围',
    area: '地区',
    stack_item: '堆叠项',
    placeholder_field: '拖动字段至此处',
    axis_label_rotate: '标签角度',
    chart_scatter_bubble: '气泡图',
    chart_scatter: '散点图',
    bubble_size: '气泡大小',
    chart_treemap: '矩形树图',
    drill: '钻取',
    drag_block_treemap_label: '色块标签',
    drag_block_treemap_size: '色块大小',
    bubble_symbol: '图形',
    gap_width: '间隔',
    width: '宽度',
    height: '高度',
    system_case: '系统方案',
    custom_case: '自定义',
    last_layer: '当前已经是最后一级',
    radar_size: '大小',
    chart_mix: '柱线组合图',
    chart_mix_group_column: '分组柱线组合图',
    chart_mix_stack_column: '堆叠柱线组合图',
    chart_mix_dual_line: '双线组合图',
    axis_value: '轴值',
    axis_value_min: '最小值',
    axis_value_max: '最大值',
    axis_value_split: '间隔',
    axis_auto: '自动',
    table_info_switch: '明细表切换将清空维度',
    drag_block_value_axis_main: '主轴值',
    drag_block_value_axis_ext: '副轴值',
    yAxis_main: '主纵轴',
    yAxis_ext: '副纵轴',
    total: '共',
    items: '条数据',
    chart_liquid: '水波图',
    chart_indicator: '指标卡',
    drag_block_progress: '进度指示',
    liquid_max: '目标值',
    liquid_outline_border: '边框粗细',
    liquid_outline_distance: '边框间隔',
    liquid_wave_length: '水波长度',
    liquid_wave_count: '水波数量',
    liquid_shape: '形状',
    liquid_shape_circle: '圆形',
    liquid_shape_diamond: '菱形',
    liquid_shape_triangle: '三角形',
    liquid_shape_pin: '气球',
    liquid_shape_rect: '矩形',
    dimension_or_quota: '维度或指标',
    axis_value_split_count: '刻度数',
    axis_value_split_space: '刻度间距',
    chart_waterfall: '瀑布图',
    pie_inner_radius_percent: '内径占比',
    pie_outer_radius_size: '外径大小',
    table_page_size: '分页',
    table_page_size_unit: '条/页',
    result_count: '结果展示',
    result_mode_all: '全部',
    result_mode_custom: '自定义',
    chart_word_cloud: '词云',
    drag_block_word_cloud_label: '词标签',
    drag_block_word_cloud_size: '词大小',
    splitCount_less_100: '刻度数范围0-100',
    change_chart_type: '更改类型',
    chart_type_table: '表格',
    chart_type_quota: '指标',
    chart_type_trend: '线/面图',
    chart_type_compare: '柱/条图',
    chart_type_distribute: '分布图',
    chart_type_relation: '关系图',
    chart_type_dual_axes: '双轴图',
    chart_type_space: '地图',
    preview: '上一步',
    next: '下一步',
    select_dataset: '选择数据集',
    select_chart_type: '选择图表类型',
    recover: '重置',
    yoy_label: '同比/环比',
    yoy_setting: '同环比设置',
    pls_select_field: '请选择字段',
    compare_date: '对比日期',
    compare_type: '对比类型',
    compare_data: '数据设置',
    year_yoy: '年同比',
    month_yoy: '月同比',
    quarter_yoy: '季同比',
    week_yoy: '周同比',
    day_yoy: '日同比',
    year_mom: '年环比',
    month_mom: '月环比',
    quarter_mom: '季环比',
    week_mom: '周环比',
    day_mom: '日环比',
    data_pre: '具体数值',
    data_sub: '对比差值',
    data_percent: '差值百分比',
    compare_calc_expression: '计算公式',
    compare_calc_day_pre: '昨日数据',
    compare_calc_day_sub: '当天数据 - 昨日数据',
    compare_calc_day_percent: '（当天数据 / 昨日数据 - 1）* 100%',
    compare_calc_month_pre: '上月同日数据',
    compare_calc_month_pre_m: '上月数据',
    compare_calc_month_sub: '当天数据 - 上月同日数据',
    compare_calc_month_sub_m: '当月数据 - 上月数据',
    compare_calc_month_percent: '（当天数据 / 上月同日数据 - 1）* 100%',
    compare_calc_month_percent_m: '（当月数据 / 上月数据 - 1）* 100%',
    compare_calc_year_pre: '去年同月同日数据',
    compare_calc_year_pre_m: '去年同月数据',
    compare_calc_year_pre_y: '去年数据',
    compare_calc_year_sub: '当天数据 - 去年同月同日数据',
    compare_calc_year_sub_m: '当月数据 - 去年同月数据',
    compare_calc_year_sub_y: '当年数据 - 去年数据',
    compare_calc_year_percent: '（当天数据 / 去年同月同日数据 - 1）* 100%',
    compare_calc_year_percent_m: '（当月数据 / 去年同月数据 - 1）* 100%',
    compare_calc_year_percent_y: '（当年数据 / 去年数据 - 1）* 100%',
    compare_calc_tip:
      '当对比日期需要过滤时，请使用过滤组件实现过滤；使用图表过滤器，仪表板下钻和联动等功能，会导致结果不一致',
    and: '与',
    or: '或',
    logic_exp: '逻辑条件',
    enum_exp: '字段枚举值',
    pls_slc: '请选择',
    filter_exp: '过滤值',
    filter_type: '过滤方式',
    filter_value_can_not_str: '数值类型字段过滤值不能包含文本',
    enum_value_can_not_null: '字段枚举值不能为空',
    table_config: '表格配置',
    table_column_width_config: '列宽调整',
    table_column_adapt: '自适应',
    table_column_fixed: '固定列宽',
    table_column_custom: '自定义',
    chart_table_pivot: '透视表',
    chart_table_heatmap: '热力图',
    table_pivot_row: '数据行',
    field_error_tips:
      '该字段所对应的数据集原始字段发生变更（包括维度、指标，字段类型，字段被删除等），建议重新编辑',
    mark_field_error: '数据集变更，当前字段不存在，请重新选择',
    table_border_color: '边框颜色',
    table_header_align: '表头对齐方式',
    table_item_align: '表格对齐方式',
    table_align_left: '左对齐',
    table_align_center: '居中',
    table_align_right: '右对齐',
    table_scroll_bar_color: '滚动条颜色',
    table_pager_style: '分页器风格',
    page_pager_simple: '精简',
    page_pager_general: '常规',
    draw_back: '收回',
    senior: '高级',
    senior_cfg: '高级设置',
    function_cfg: '功能设置',
    analyse_cfg: '分析预警',
    slider: '缩略轴',
    slider_range: '默认范围',
    slider_bg: '背景',
    slider_fill_bg: '选中背景',
    slider_text_color: '字体颜色',
    chart_no_senior: '当前图表类型暂无高级配置，敬请期待',
    chart_no_properties: '当前图表类型暂无样式配置',
    assist_line: '辅助线',
    field_fixed: '固定值',
    line_type_dotted: '点',
    value_can_not_empty: '值不能为空',
    value_error: '值必须为数值',
    threshold: '条件样式',
    threshold_range: '阈值区间',
    gauge_threshold_format_error: '格式错误',
    total_cfg: '总计配置',
    col_cfg: '列汇总',
    row_cfg: '行汇总',
    total_show: '总计',
    total_position: '位置',
    total_label: '别名',
    sub_total_show: '小计',
    total_pos_top: '顶部',
    total_pos_bottom: '底部',
    total_pos_left: '左侧',
    total_pos_right: '右侧',
    chart_label: '文本卡',
    drag_block_label: '标签',
    count_distinct: '去重计数',
    table_page_mode: '分页模式',
    page_mode_page: '翻页',
    page_mode_pull: '下拉',
    exp_can_not_empty: '条件不能为空',
    value_formatter: '数值格式',
    value_formatter_type: '格式类型',
    value_formatter_auto: '自动',
    value_formatter_value: '数值',
    value_formatter_percent: '百分比',
    value_formatter_unit: '数量单位',
    value_formatter_unit_language: '单位语言',
    value_formatter_unit_language_ch: '中文',
    value_formatter_unit_language_en: '英文',
    value_formatter_decimal_count: '小数位数',
    value_formatter_suffix: '单位后缀',
    show_gap: '显示间隔值',
    indicator_suffix_placeholder: '请输入1-10个字符',
    indicator_suffix: '后缀',
    indicator_value: '指标值',
    value_formatter_thousand_separator: '千分符',
    value_formatter_example: '示例',
    unit_none: '无',
    unit_thousand: '千',
    unit_ten_thousand: '万',
    unit_million: '百万',
    unit_hundred_million: '亿',
    formatter_decimal_count_error: '请输入0-10的整数',
    gauge_threshold_compare_error: '阈值范围需逐级递增',
    tick_count: '刻度间隔数',
    custom_sort: '自定义',
    custom_sort_tip: '自定义排序优先级最高，且仅支持单个字段自定义',
    clean_custom_sort: '清除自定义排序',
    ds_field_edit: '数据集字段管理',
    chart_field_edit: '图表字段管理',
    copy_field: '复制字段',
    calc_field: '计算字段',
    form_type: '类别',
    scroll_cfg: '滚动设置',
    scroll: '滚动',
    open: '开启',
    row: '行数',
    interval: '间隔',
    max_more_than_mix: '最大值必须大于最小值',
    field: '字段',
    textColor: '文字',
    backgroundColor: '背景',
    rowBackgroundColor: '行背景',
    colBackgroundColor: '列背景',
    cornerBackgroundColor: '角背景',
    field_can_not_empty: '字段不能为空',
    conditions_can_not_empty: '字段的条件不能为空，若无条件，请直接删除该字段',
    remark: '备注',
    remark_placeholder: '备注限制512个字符',
    remark_show: '显示备注',
    remark_edit: '编辑备注',
    remark_bg_color: '背景填充',
    quota_font_family: '值字体',
    quota_text_style: '值样式',
    quota_letter_space: '值字间距',
    dimension_font_family: '名称字体',
    dimension_text_style: '名称样式',
    dimension_letter_space: '名称字间距',
    name_value_spacing: '名称/值间距',
    font_family: '字体',
    letter_space: '字间距',
    font_shadow: '字体阴影',
    chart_area: '面积图',
    fix: '固定值',
    dynamic: '动态值',
    gauge_size_field_delete: '动态值中字段发生变更，请重新编辑',
    chart_group: '子类别',
    chart_bar_group: '分组柱状图',
    chart_bar_group_stack: '分组堆叠柱状图',
    field_dynamic: '动态值',
    aggregation: '聚合方式',
    filter_between: '介于',
    field_not_empty: '字段不能为空',
    summary_not_empty: '聚合方式不能为空',
    reserve_zero: '取整',
    reserve_one: '一位',
    reserve_two: '两位',
    proportion: '占比',
    label_content: '标签展示',
    percent: '占比',
    table_index_desc: '表头名称',
    total_sort: '总计排序',
    total_sort_none: '无',
    total_sort_asc: '升序',
    total_sort_desc: '降序',
    total_sort_field: '排序字段',
    empty_data_strategy: '空值处理',
    empty_data_field_ctrl: '字段设置',
    break_line: '保持为空',
    set_zero: '置为0',
    ignore_data: '隐藏空值',
    sub_dimension_tip:
      '该字段为必填项，且不应使用类别轴中的字段，若无需该字段，请选择基础图表进行展示，否则展示效果不理想。',
    drill_dimension_tip: '钻取字段仅支持数据集中的字段',
    table_scroll_tip: '明细表仅在分页模式为"下拉"时生效。',
    table_threshold_tip: '提示：请勿重复选择字段，若同一字段重复配置，则只有最后的字段配置生效',
    table_column_width_tip:
      '固定列宽并非任何时候都能生效。<br/>容器宽度优先级高于列宽，即(表格容器宽度 / 列数 > 指定列宽)，则列宽优先取(容器宽度 / 列数)。',
    reference_field_tip:
      '引用字段以 "[" 开始， "]" 结束。<br/>请勿修改引用内容，否则将引用失败。<br/>若输入与引用字段相同格式的内容，将被当作引用字段处理。',
    scatter_tip: '该指标生效时，样式大小中的气泡大小属性将失效',
    place_name_mapping: '地名映射',
    axis_tip:
      '最小值、最大值、间隔均为数值类型；若不填，则该项视为自动。<br/>请确保填写数值能正确计算，否则将无法正常显示轴值。',
    format_tip: `模板变量有 {a}, {b}，{c}，{d}，分别表示系列名，数据名，数据值等。<br>
                  在 触发位置 为 '坐标轴' 的时候，会有多个系列的数据，此时可以通过 {a0}, {a1}, {a2} 这种后面加索引的方式表示系列的索引。<br>
                  不同图表类型下的 {a}，{b}，{c}，{d} 含义不一样。 其中变量{a}, {b}, {c}, {d}在不同图表类型下代表数据含义为：<br><br>
                  折线（区域）图、柱状（条形）图、仪表盘 : {a}（系列名称），{b}（类目值），{c}（数值）<br>
                  饼图、漏斗图: {a}（系列名称），{b}（数据项名称），{c}（数值）, {d}（百分比）<br>
                  地图 : {a}（系列名称），{b}（区域名称），{c}（合并数值）, {d}（无）<br>
                  散点图（气泡）图 : {a}（系列名称），{b}（数据名称），{c}（数值数组）, {d}（无）`,
    h_position: '水平位置',
    v_position: '垂直位置',
    p_left: '左对齐',
    p_right: '右对齐',
    p_top: '上对齐',
    p_bottom: '下对齐',
    p_center: '居中',
    table_auto_break_line: '自动换行',
    table_break_line_tip: '开启自动换行，表头行高设置将失效',
    table_break_line_max_lines: '最大行数',
    step: '步长(px)',
    no_function: '函数尚未支持直接引用，请在字段表达式中手动输入。',
    chart_flow_map: '流向地图',
    chart_heat_map: '热力地图',
    start_point: '起点经纬度',
    end_point: '终点经纬度',
    line: '线条',
    map_style: '地图风格',
    map_style_url: '地图风格 URL',
    map_pitch: '倾角',
    map_rotation: '旋转',
    map_style_normal: '标准',
    map_style_light: '明亮',
    map_style_dark: '暗黑',
    map_style_fresh: '草色青',
    map_style_grey: '雅士灰',
    map_style_blue: '靛青蓝',
    map_style_darkblue: '极夜蓝',
    map_line_type: '类型',
    type: '类型',
    map_line_width: '线条宽度',
    map_line_height: '线条高度',
    map_line_linear: '渐变',
    map_line_animate: '动画',
    heatmap_classics: '经典热力图',
    heatmap3D: '3D热力图',
    heatMapIntensity: '热力强度',
    heatMapRadius: '热力点半径',
    map_line_animate_duration: '动画间隔',
    map_line_animate_interval: '轨迹间隔',
    map_line_animate_trail_length: '轨迹长度',
    map_line_type_line: '直线',
    map_line_type_arc: '弧线',
    map_line_type_arc_3d: '3D 弧线',
    map_line_type_great_circle: '大圆弧',
    map_line_color_source_color: '起始颜色',
    map_line_color_target_color: '结束颜色',
    map_line_theta_offset: '弧度',
    refresh_frequency: '刷新频率',
    enable_refresh_view: '开启刷新',
    enable_view_loading: '图表加载提示',
    minute: '分',
    switch_chart: '切换图表',
    update_chart_data: '更新图表数据',
    second: '秒',
    more_settings: '更多设置',
    basic_style: '基础样式',
    table_header: '表头',
    table_cell: '单元格',
    table_total: '汇总',
    slc_logic: '选择逻辑关系',
    add_addition: '添加条件',
    logic_and: '所有',
    logic_or: '任一',
    conform_below: '符合以下',
    addition: '条件',
    drill_field_error: '下钻起始字段需在维度中',
    error_not_number: '不支持拖拽非数值类型指标',
    error_q_2_d: '不支持拖拽指标至维度',
    error_d_2_q: '不支持拖拽维度至指标',
    error_d_not_coordinates: '不支持拖拽非地理位置类型的维度',
    error_d_not_time_2_q: '不支持拖拽非时间类型的维度',
    error_bar_range_axis_type_not_equal: '开始值与结束值需要设置相同类型',
    only_input_number: '请输入正确数值',
    value_min_max_invalid: '最小值必须小于最大值',
    add_assist_line: '添加辅助线',
    assist_line_tip: '辅助线数值格式跟随纵轴/横轴的标签格式化配置，请到样式中配置。',
    add_threshold: '添加阈值',
    add_condition: '添加条件',
    chart_quadrant: '象限图',
    quadrant: '象限',
    font_size: '字号',
    word_size_range: '字号区间',
    word_spacing: '文字间隔',
    radiusColumnBar: '柱形',
    rightAngle: '直角',
    roundAngle: '圆角',
    topRoundAngle: '顶部圆角',
    table_layout_mode: '展示形式',
    table_layout_grid: '平铺展示',
    table_layout_tree: '树形展示',
    top_n_desc: '合并数据',
    top_n_input_1: '显示 Top',
    top_n_input_2: ', 其余合并',
    top_n_label: '其他项名称',
    progress_target: '目标值',
    progress_current: '实际值',
    gauge_axis_label: '显示刻度',
    gauge_percentage_tick: '百分比刻度',
    add_style: '添加样式',
    map_symbol_marker: '标记',
    map_symbol_pentagon: '五角形',
    map_symbol_hexagon: '六角形',
    map_symbol_octagon: '八角形',
    map_symbol_hexagram: '菱形',
    tip: '提示',
    hide: '隐藏',
    show_label: '显示标签',
    security_code: '安全密钥',
    auto_fit: '自适应缩放',
    zoom_level: '缩放等级',
    central_point: '中心点',
    full_display: '全量显示',
    show_hover_style: '显示鼠标悬浮样式',
    table_header_group: '表头分组',
    table_header_group_config: '表头分组设置',
    cancel_group: '取消分组',
    cancel_all_group: '取消所有分组',
    group_name: '分组名称',
    merge_group: '合并分组',
    table_header_group_config_tip: '字段的增删，位置变动，显隐修改都会导致分组失效',
    group_name_edit_tip: '分组名称长度为 1-20 个字符',
    group_name_error_tip: '请输入正确的分组名称',
    merge_cells: '合并单元格',
    length_limit: '长度限制',
    radar_point: '开启辅助点',
    radar_point_size: '辅助点大小',
    radar_area_color: '开启面积',
    table_freeze_tip: '合并单元格后，不支持行列冻结',
    merge_cells_tips: '合并单元格后，行列冻结、自动换行和斑马纹会失效，当前页的序号会从 1 开始',
    merge_cells_break_line_tip: '合并单元格后，不支持自动换行',
    font_family_ya_hei: '微软雅黑',
    font_family_song_ti: '宋体',
    font_family_kai_ti: '楷体',
    font_family_hei_ti: '黑体',
    gauge_condition_style_tips: `条件样式设置，决定仪表盘区间颜色，为空则不开启阈值，范围(0-100)，逐级递增<br/>例如：输入 30,70；表示：分为3段，分别为[0,30],[30,70],[70,100]`,
    light_theme: '浅色主题',
    dark_theme: '深色主题',
    export_excel: '导出Excel',
    export_excel_formatter: '导出Excel(带格式)',
    export_raw_details: '导出原始明细',
    field_is_empty_export_error: '当前无字段，无法导出',
    chart_symbolic_map: '符号地图',
    symbolic: '符号',
    symbolic_shape: '符号形状',
    symbolic_upload_hint: '支持 1MB 以内的 SVG, JPG, JPEG, PNG 文件',
    symbolic_range: '区间',
    symbolic_error_icon: '请选择正确的图标文件!',
    symbolic_error_size: '文件大小不能超过 1MB!',
    symbolic_error_range: '第二个区间值必须大于第一个区间值',
    chart_stock_line: 'K 线图',
    liquid_condition_style_tips: `条件样式设置，决定水波图颜色，为空则不开启阈值，范围(0-100)，逐级递增<br/>例如：输入 30,70；表示：分为3段，分别为[0,30],[30,70],[70,100]`,
    conversion_rate: '转化率',
    show_extremum: '显示最值',
    left_axis: '左轴',
    right_axis: '右轴',
    no_other_configurable_properties: '无其他可配置属性',
    custom_label_content_tip: "可以${'{'}fieldName{'}'}的形式读取字段值（不支持换行）",
    number_of_scales_tip: '期望的坐标轴刻度数量，非最终结果',
    assist_line_settings: '辅助线设置',
    invalid_field: '无效字段',
    k_line_yaxis_tip: '开盘价-收盘价-最低价-最高价',
    carousel_enable: '开启轮播',
    carousel_stay_time: '停留时长（秒）',
    carousel_interval: '轮播间隔（秒）',
    custom_tooltip_content_tip: "可以${'{'}fieldName{'}'}的形式读取字段值（支持HTML）",
    legend_range_division: '图例区间划分',
    legend_equal_range: '等分区间',
    legend_custom_range: '自定义区间',
    start_coordinates: '起点经纬度',
    end_coordinates: '终点经纬度',
    start_name: '起点名称',
    end_name: '终点名称',
    flow_map_line_width: '线条粗细',
    flow_map_line_width_tip: '该指标生效时，样式中线条配置的线条宽度属性将失效',
    symbolic_map_coordinates: '经纬度',
    symbolic_map_bubble_size_tip:
      '该指标生效时，样式基础样式中的大小属性将失效，同时可在样式基础样式中的大小区间配置大小区间',
    point_text: '标注点文本',
    point_bubble_color: '标注点气泡颜色',
    point_bubble_size: '标注点气泡大小',
    animation_type: '动效类型',
    water_wave: '水波',
    animation_speed: '动效速率',
    wave_rings: '水波环数',
    symbolic_map_symbol_shape: '符号形状',
    symbolic_map_symbol_shape_tip: '自定义时, 支持 1MB 以内的 SVG, JPG, JPEG, PNG 文件',
    size_range: '大小区间',
    x_axis_constant_line: 'X 轴恒线',
    y_axis_constant_line: 'Y 轴恒线',
    sort_priority: '排序优先级设置',
    sort_priority_tip: '自上而下，排序优先级从高到低',
    chart_circle_packing: '圆形填充图',
    circle_packing_name: '圆形名称',
    circle_packing_value: '圆形大小',
    circle_packing_border_color: '边线颜色',
    circle_packing_border_width: '边线宽度',
    circle_packing_padding: '圆形间距',
    increase: '增加',
    decrease: '减少',
    waterfall_total: '合计',
    accumulate: '累加',
    table_cross_bg_tip: '合并单元格后，不支斑马纹',
    pivot_export_invalid_field: '行维度或指标维度为空不可导出!',
    pivot_export_invalid_col_exceed: '表格列数超过最大限制不可导出!',
    expand_all: '全展开',
    level_label: '第{num}层级',
    default_expand_level: '默认展开层级',
    no_data_or_not_positive: '暂无数据，或数据均不是正数，无法绘制',
    map_type: '地图提供商',
    map_type_gaode: '高德地图',
    map_type_tianditu: '天地图',
    map_type_baidu: '百度地图',
    map_type_tencent: '腾讯地图',
    bullet_chart: '子弹图',
    range_bg: '区间背景',
    legend_name: '图例名称',
    threshold_value: '分界值',
    range_num: '区间背景个数',
    show_range_bg: '显示区间背景',
    last_item: '最后一项',
    legend_sort: '图例排序',
    quota_position: '指标展示',
    quota_position_col: '列头展示',
    quota_position_row: '行头展示',
    quota_col_label: '指标列名',
    table_grand_total_label: '总计别名',
    table_field_total_label: '字段别名',
    table_row_header_freeze: '行头冻结'
  },
  dataset: {
    field_value: '字段值',
    scope_edit: '仅编辑时生效',
    scope_all: '数据集预览时全局生效',
    spend_time: '耗时',
    sql: 'SQL 语句',
    sql_result: '运行结果',
    parse_filed: '解析字段',
    field_rename: '字段重命名',
    params_work:
      '仅编辑时生效：参数值仅在数据集编辑时生效；全局生效：在数据集查看、预览、以及用到数据集的图表中均生效。',
    select_year: '选择年',
    sql_variable_limit_1: '1、SQL 变量只能在 WHERE 条件中使用',
    sql_variable_limit_2:
      "2、示例: select * from table where $DE_PARAM{'{'} name = substring('$[PARAM1]',1,5){'}'} and $DE_PARAM{'{'} name in ($[PARAM2]) {'}'}",
    select_month: '选择月',
    select_date: '选择日期',
    select_time: '选择时间',
    time_year: '日期-年',
    time_year_month: '日期-年月',
    time_year_month_day: '日期-年月日',
    time_all: '日期-年月日时分秒',
    dataset_sync: ' ( 数据同步中... )',
    sheet_warn: '有多个 Sheet 页，默认抽取第一个',
    datalist: '数据集',
    name: '数据集名称',
    add_group: '添加分组',
    add_scene: '添加场景',
    group: '分组',
    scene: '场景',
    delete: '删除',
    move_to: '移动到',
    rename: '重命名',
    tips: '提示',
    confirm_delete: '确认删除',
    confirm_delete_msg: '数据集删除，会影响与其相关的自定义数据集、关联数据集、仪表板，确认删除？',
    delete_success: '删除成功',
    confirm: '确认',
    cancel: '取消',
    search: '搜索',
    back: '返回',
    add_table: '添加数据集',
    process: '进度',
    update: '更新',
    db_data: '数据库数据集',
    sql_data: 'SQL 数据集',
    excel_data: 'Excel 数据集',
    custom_data: '自定义数据集',
    pls_slc_tbl_left: '请从左侧选择表',
    add_db_table: '添加数据库数据集',
    add_api_table: '添加API数据集',
    pls_slc_data_source: '请选择数据源',
    table: '表',
    edit: '编辑',
    create_view: '创建图表',
    data_preview: '数据预览',
    field_type: '字段类型',
    field_name: '字段名称',
    field_origin_name: '原始名称',
    field_check: '选中',
    update_info: '更新信息',
    update_records: '更新记录',
    join_view: '数据关联',
    text: '文本',
    time: '时间',
    value: '数值',
    mode: '模式',
    direct_connect: '直连',
    sync_data: '定时同步',
    update_setting: '更新设置',
    sync_now: '立即更新',
    add_task: '添加任务',
    task_name: '任务名称',
    task_id: '任务ID',
    start_time: '开始时间',
    end_time: '结束时间',
    status: '状态',
    error: '失败',
    completed: '成功',
    underway: '执行中',
    task_update: '更新设置',
    update_type: '更新方式',
    all_scope: '全量更新',
    add_scope: '增量更新',
    select_data_time: '选择日期时间',
    execute_rate: '执行频率',
    execute_once: '立即执行',
    simple_cron: '简单重复',
    cron_config: '表达式设定',
    no_limit: '无限制',
    set_end_time: '设定结束时间',
    operate: '操作',
    save_success: '保存成功',
    close: '关闭',
    required: '必填',
    input_content: '请输入内容',
    add_sql_table: '添加 SQL 数据集',
    preview: '预览',
    pls_input_name: '请输入名称',
    connect_mode: '连接模式',
    incremental_update_type: '增量更新方式',
    incremental_add: '增量添加',
    incremental_delete: '增量删除',
    last_update_time: '上次更新时间',
    current_update_time: '当前更新时间',
    param: '参数',
    edit_sql: '编辑 SQL 数据集',
    showRow: '显示行',
    add_excel_table: '添加Excel数据集',
    add_custom_table: '添加自定义数据集',
    upload_file: '上传文件',
    detail: '详情',
    type: '类型',
    create_by: '创建者',
    create_time: '创建时间',
    preview_show: '显示',
    preview_item: '条数据',
    preview_total: '共',
    pls_input_less_5: '请输入5位以内的正整数',
    field_edit: '编辑字段',
    table_already_add_to: '该表已添加至',
    uploading: '上传中...',
    add_union: '新建关联',
    union_setting: '关联设置',
    pls_slc_union_field: '请选择关联字段',
    pls_slc_union_table: '请选择关联表',
    source_table: '关联表',
    source_field: '关联字段',
    target_table: '被关联表',
    target_field: '被关联字段',
    union_relation: '关联关系',
    pls_setting_union_success: '请正确设置关联关系',
    invalid_dataset: 'Kettle未运行，无效数据集',
    check_all: '全选',
    can_not_union_self: '被关联表不能与关联表相同',
    float: '小数',
    edit_custom_table: '编辑自定义数据集',
    edit_field: '编辑字段',
    preview_100_data: '显示前100行数据',
    invalid_table_check: '非直连数据集请先完成数据同步',
    parse_error:
      'Excel解析失败，请检查格式、字段等信息。具体参考：https://dataease.io/docs/user_manual/dataset_configuration/dataset_Excel',
    origin_field_type: '字段原始类型',
    edit_excel_table: '编辑Excel数据集',
    edit_excel: '编辑Excel',
    excel_replace: '替换',
    excel_add: '追加',
    dataset_group: '数据集分组',
    m1: '将 ',
    m2: ' 移动到',
    char_can_not_more_50: '数据集名称不能超过50个字符',
    task_add_title: '添加任务',
    task_edit_title: '编辑任务',
    sync_latter: '稍后同步',
    task: {
      list: '任务列表',
      record: '执行记录',
      create: '新建任务',
      name: '任务名称',
      last_exec_time: '上次执行时间',
      next_exec_time: '下次执行时间',
      last_exec_status: '上次执行结果',
      task_status: '任务状态',
      dataset: '数据集',
      search_by_name: '根据名称搜索',
      underway: '等待执行',
      stopped: '执行结束',
      exec: '执行中',
      pending: '暂停',
      confirm_exec: '手动触发执行？',
      change_success: '状态切换成功',
      excel_replace_msg: '可能会影响自定义数据集、关联数据集、仪表板等，确认替换？',
      effect_ext_field: '会影响计算字段'
    },
    field_group_type: '分类',
    location: '地理位置',
    left_join: '左连接',
    right_join: '右连接',
    inner_join: '内连接',
    full_join: '全连接',
    can_not_union_diff_datasource: '被关联数据集必须与当前数据集的数据源一致',
    operator: '操作',
    d_q_trans: '维度/指标转换',
    add_calc_field: '新建计算字段',
    input_name: '请输入名称',
    field_exp: '字段表达式',
    data_type: '数据类型',
    click_ref_field: '点击引用字段',
    click_ref_function: '点击引用函数',
    field_manage: '字段管理',
    edit_calc_field: '编辑计算字段',
    calc_field: '计算字段',
    show_sql: '显示SQL',
    ple_select_excel: '请重新上传要导入的 Excel',
    merge: '合并',
    no_merge: '不合并',
    merge_msg: '数据表中存在字段一致的情况，是否合并到一个数据集中?',
    merge_title: '合并数据',
    field_name_less_50: '字段名不能超过50个字符',
    field_name_less_2_64: '2-64个字符',
    excel_info_1: '1、Excel 文件中不能存在合并单元格；',
    excel_info_2: '2、Excel 文件的第一行为标题行，不能为空，不能为日期型；',
    excel_info_3: '3、文件大小请确保在500M以内。',
    sync_field: '同步字段',
    confirm_sync_field: '确认同步',
    confirm_sync_field_tips: '同步字段可能会导致已编辑字段发生变更，请确认',
    sync_success: '同步成功',
    sync_success_1: '同步成功，请对当前数据集重新执行数据同步操作',
    row_permission: {
      type: '类型',
      name: '名称',
      condition: '条件',
      value: '值',
      add: '添加行权限',
      edit: '编辑行权限',
      please_select_field: '请选择字段',
      please_select_auth_type: '请选择授权类型',
      please_select_auth_id: '请选择授权目标',
      row_permission_not_empty: '行权限不能为空',
      search_by_filed_name: '根据字段名称搜索',
      auth_type: '授权类型',
      auth_obj: '授权对象'
    },
    column_permission: {
      add: '添加列权限',
      edit: '编辑列权限',
      please_select_field: '请选择字段',
      please_select_auth_type: '请选择授权类型',
      please_select_auth_id: '请选择授权目标',
      column_permission_not_empty: '列权限不能为空',
      auth_type: '授权类型',
      auth_obj: '授权对象',
      enable: '启用',
      disable: '禁用',
      prohibit: '禁用',
      desensitization: '脱敏',
      desensitization_rule: '脱敏规则',
      m: 'M等于',
      n: 'N等于',
      mgtn: 'M 不能大于 N'
    },
    row_permissions: '行权限',
    column_permissions: '列权限',
    row_column_permissions: '行列权限',
    union_data: '关联数据集',
    add_union_table: '添加关联数据集',
    edit_union: '编辑关联数据集',
    union: '关联',
    edit_union_relation: '编辑关联关系',
    add_union_relation: '新建关联关系',
    field_select: '字段选择',
    add_union_field: '添加关联字段',
    union_error: '关联关系与关联字段不能为空',
    union_repeat: '当前数据集已被关联，请勿重复关联',
    preview_result: '预览结果',
    sql_ds_union_error: '直连模式下SQL数据集，不支持关联',
    api_data: 'API 数据集',
    copy: '复制',
    sync_log: '同步日志',
    field_edit_name: '字段名称',
    input_edit_name: '请输入字段名称',
    edit_search: '通过名称搜索',
    na: '暂无',
    date_format: '时间格式，默认: 年-月-日 时:分:秒',
    export_dataset: '数据集导出',
    filename: '文件名称',
    export_filter: '筛选条件',
    pls_input_filename: '请输入文件名称',
    calc_tips: {
      tip1: '表达式语法请遵循calcite语法。',
      tip1_1: '表达式语法请遵循该数据源对应的数据库语法。',
      tip2: '聚合运算仅能在图表中生效。在预览时显示为"-"',
      tip3: '引用字段以 "[" 开始， "]" 结束',
      tip4: '请勿修改引用内容，否则将引用失败',
      tip5: '若输入与引用字段相同格式的内容，将被当作引用字段处理',
      tip6: '请使用Calcite支持的函数进行表达式编辑',
      tip7: '请使用当前数据源对应的数据库函数进行表达式编辑',
      tip8: 'Calcite函数请参考文档：'
    },
    batch_manage: '批量管理',
    origin_name: '物理字段名',
    origin_type: '物理字段类型',
    field_diff: '所选字段类型不一致，不支持转换',
    create_grouping_field: '新建分组字段',
    editing_grouping_field: '编辑分组字段',
    grouping_field: '分组字段',
    grouping_settings: '分组设置',
    ungrouped_value: '未分组的值',
    please_enter_number: '请输入数字'
  },
  deDataset: {
    search_by_name: '通过名称搜索',
    new_folder: '新建文件夹',
    search_fields: '搜索字段',
    show_rows: '显示行数',
    display: '显示',
    row: '行',
    restricted_objects: '受限对象',
    select_data_source: '选择数据源',
    by_table_name: '通过表名称搜索',
    run_a_query: '运行查询',
    running_results: '运行结果',
    parameter_type: '参数类型',
    run_failed: '运行失败',
    select_data_table: '选择数据表',
    in_the_file: '文件中不能存在合并单元格',
    or_date_type: '文件的第一行为标题行，不能为空，不能为日期型',
    is_within_500m: 'Excel文件大小请确保在500M以内',
    upload_data: '上传数据',
    excel_data_first: '请先上传Excel数据',
    is_currently_available: '当前无可用的数据表',
    sure_to_synchronize: '同步字段可能导致已编辑字段发生变更，确定同步？',
    folder_name: '文件夹名称',
    folder: '所属文件夹',
    edit_folder: '编辑文件夹',
    name_already_exists: '文件夹名称已存在',
    data_preview: '数据预览',
    original_name: '原始名称',
    database: '数据库',
    selected: '已选：',
    table: '张表',
    no_dataset_click: '暂无数据集，点击',
    create: '新建',
    new_folder_first: '请先新建文件夹',
    on_the_left: '请在左侧选择数据集',
    expression_syntax_error: '字段表达式语法错误',
    create_dashboard: '创建仪表板',
    cannot_be_empty: 'SQL不能为空',
    data_reference: '数据参考',
    want_to_replace: '替换可能会影响自定义数据集、关联数据集、仪表板等，是否替换？',
    replace_the_data: '确定替换数据吗？',
    append_successfully: '追加成功',
    already_exists: '数据集名称已存在',
    edit_dataset: '编辑数据集',
    convert_to_indicator: '转换为指标',
    convert_to_dimension: '转换为维度',
    left_to_edit: '选中左侧的数据表可进行编辑',
    cannot_be_duplicate: '数据集名称不允许重复',
    set_saved_successfully: '数据集保存成功',
    to_start_using: '浏览您的数据库，表和列的内容。 选择一个数据库即可开始使用。',
    to_run_query: '点击运行查询',
    the_running_results: '即可查看运行结果',
    item: '项',
    logic_filter: '条件筛选',
    enum_filter: '枚举筛选',
    description: '字段备注'
  },
  about: {
    auth_to: '授权给',
    invalid_license: 'License 无效',
    update_license: '更新 License',
    expiration_time: '过期时间',
    expirationed: '(已过期)',
    auth_num: '授权数量',
    version: '版本',
    version_num: '版本号',
    standard: '社区版',
    enterprise: '企业版',
    Professional: '专业版',
    Embedded: '嵌入式版',
    support: '获取技术支持',
    update_success: '更新成功，请重新登录',
    serial_no: '序列号',
    remark: '备注',
    back_community: '还原至社区版',
    confirm_tips: '确定还原至社区版？'
  },
  cron: {
    second: '秒',
    minute: '分',
    hour: '时',
    day: '日',
    minute_default: '分 (执行时间：0秒)',
    hour_default: '时 (执行时间：0分0秒)',
    day_default: '日 (执行时间：0时0分0秒)',
    month: '月',
    week: '周',
    year: '年',
    d_w_cant_not_set: '日期与星期不可以同时为“不指定”',
    d_w_must_one_set: '日期与星期必须有一个为“不指定”',
    every_day: '每日',
    cycle: '周期',
    not_set: '不指定',
    from: '从',
    to: '至',
    repeat: '循环',
    day_begin: '日开始，每',
    day_exec: '日执行一次',
    work_day: '工作日',
    this_month: '本月',
    day_near_work_day: '号，最近的工作日',
    this_week_last_day: '本月最后一天',
    set: '指定',
    every_hour: '每时',
    hour_begin: '时开始，每',
    hour_exec: '时执行一次',
    every_month: '每月',
    month_begin: '月开始，每',
    month_exec: '月执行一次',
    every: '每',
    every_begin: '开始，每',
    every_exec: '执行一次',
    every_week: '每周',
    week_start: '从星期',
    week_end: '至星期',
    every_year: '每年',
    week_tips: '说明：1-7 分别对应 周日-周六',
    minute_limit: '分钟不能小于1，大于59',
    hour_limit: '小时不能小于1，大于23',
    day_limit: '天不能小于1，大于31'
  },
  commons: {
    result_count: '个结果',
    clear_filter: '清空条件',
    language: '语言',
    help_center: '帮助中心',
    assistant: '小助手',
    test_connect: '测试连接',
    consanguinity: '血缘关系',
    collapse_navigation: '收起导航',
    operate_cancelled: '已取消操作',
    bind: '绑定',
    unbind: '解绑',
    unlock: '解锁',
    unlock_success: '解锁成功',
    uninstall: '卸载',
    parameter_effect: '参数值仅在数据集编辑时生效',
    no_result: '没有找到相关内容',
    manage_member: '管理成员',
    confirm_remove_cancel: '确定删除该角色吗?',
    user_confirm_remove_cancel: '确定将该用户从角色中移除吗?',
    default_value: '默认值',
    params_value: '参数值',
    input_role_name: '请输入角色名称',
    publish: '发布',
    unpublished: '取消发布',
    default_pwd: '初始密码',
    stop: '停止',
    first_login_tips: '您使用的是初始密码，记得修改密码哦',
    roger_that: '知道了',
    donot_noti: '不再提示',
    apply: '应用',
    search: '搜索',
    folder: '目录',
    no_target_permission: '没有权限',
    success: '成功',
    switch_lang: '切换语言成功',
    close: '关闭',
    icon: '图标',
    all: '全部',
    enable: '启用',
    disable: '禁用',
    yes: '是',
    no: '否',
    reset: '重置',
    catalogue: '目录',
    button: '按钮',
    gender: '性别',
    man: '男',
    woman: '女',
    keep_secret: '保密',
    nick_name: '姓名',
    confirmPassword: '确认密码',
    upload: '上传',
    cover: '覆盖',
    not_cover: '不覆盖',
    import_mode: '导入模式',
    import_module: '导入模块',
    please_fill_in_the_template: '请填写模板内容',
    cut_back_old_version: '切回旧版',
    cut_back_new_version: '切回新版',
    comment: '评论',
    examples: '示例',
    help_documentation: '帮助文档',
    api_help_documentation: 'API文档',
    delete_cancelled: '已取消删除',
    workspace: '工作空间',
    organization: '组织',
    menu: '菜单',
    setting: '设置',
    project: '项目',
    about_us: '关于',
    current_project: '当前项目',
    name: '名称',
    description: '描述',
    annotation: '注释',
    clear: '清空',
    save: '保存',
    otherSave: '另存为',
    update: '更新',
    save_success: '保存成功',
    delete_success: '删除成功',
    copy_success: '复制成功',
    modify_success: '修改成功',
    delete_cancel: '已取消删除',
    confirm: '确定',
    cancel: '取消',
    prompt: '提示',
    operating: '操作',
    input_limit: '长度在 {0} 到 {1} 个字符',
    login: '登录',
    welcome: '一站式开源数据分析平台',
    username: '姓名',
    password: '密码',
    input_username: '请输入用户姓名',
    input_password: '请输入密码',
    test: '测试',
    create_time: '创建时间',
    update_time: '更新时间',
    add: '添加',
    member: '成员',
    email: '邮箱',
    phone: '电话',
    mobile_phone: '请输入手机号',
    mobile_phone_number: '手机号码',
    role: '角色',
    personal_info: '个人信息',
    user_center: '用户中心',
    api_keys: 'API Keys',
    quota: '配额管理',
    status: '状态',
    show_all: '显示全部',
    show: '显示',
    report: '报告',
    user: '用户',
    system: '系统',
    personal_setting: '个人设置',
    test_resource_pool: '测试资源池',
    system_setting: '系统设置',
    input_content: '请输入内容',
    create: '新建',
    edit: '编辑',
    copy: '复制',
    refresh: '刷新',
    remark: '备注',
    delete: '删除',
    reduction: '恢复',
    not_filled: '未填写',
    please_select: '请选择',
    search_by_name: '根据名称搜索',
    personal_information: '个人信息',
    exit_system: '退出系统',
    verification: '验证',
    title: '标题',
    custom: '自定义',
    select_date: '选择日期',
    months_1: '一月',
    months_2: '二月',
    months_3: '三月',
    months_4: '四月',
    months_5: '五月',
    months_6: '六月',
    months_7: '七月',
    months_8: '八月',
    months_9: '九月',
    months_10: '十月',
    months_11: '十一月',
    months_12: '十二月',
    weeks_0: '周日',
    weeks_1: '周一',
    weeks_2: '周二',
    weeks_3: '周三',
    weeks_4: '周四',
    weeks_5: '周五',
    weeks_6: '周六',
    system_parameter_setting: '系统参数',
    connection_successful: '连接成功',
    connection_failed: '连接失败',
    save_failed: '保存失败',
    host_cannot_be_empty: '主机不能为空',
    port_cannot_be_empty: '端口号不能为空',
    account_cannot_be_empty: '帐户不能为空',
    remove: '移除',
    remove_cancel: '移除取消',
    remove_success: '移除成功',
    tips: '认证信息已过期，请重新登录',
    not_performed_yet: '尚未执行',
    incorrect_input: '输入内容不正确',
    delete_confirm: '请输入以下内容，确认删除：',
    login_username: 'ID 或 邮箱',
    input_login_username: '请输入用户 ID 或 邮箱',
    input_name: '请输入名称',
    please_upload: '请上传文件',
    please_fill_path: '请填写ur路径',
    formatErr: '格式错误',
    please_save: '请先保存',
    reference_documentation: '参考文档',
    id: 'ID',
    millisecond: '毫秒',
    cannot_be_null: '不能为空',
    required: '必填',
    already_exists: '名称不能重复',
    modifier: '修改人',
    validate: '校验',
    batch_add: '批量添加',
    tag_tip: '输入回车添加标签',
    search_keywords: '输入关键字搜索',
    table: {
      select_tip: '已选中 {0} 条数据'
    },
    date: {
      select_date: '选择日期',
      start_date: '开始日期',
      end_date: '结束日期',
      select_date_time: '选择日期时间',
      start_date_time: '开始日期时间',
      end_date_time: '结束日期时间',
      range_separator: '至',
      data_time_error: '开始日期不能大于结束日期',
      one_hour: '一小时',
      one_day: '一天',
      one_week: '一周',
      one_month: '一个月',
      permanent: '永久',
      one_year: '一年',
      six_months: '半年',
      three_months: '三个月',
      of_range_1_59: '分钟超出范围【1-59】',
      of_range_1_23: '小时超出范围【1-23】'
    },
    adv_search: {
      title: '高级搜索',
      combine: '组合查询',
      test: '所属测试',
      project: '所属项目',
      search: '查询',
      reset: '重置',
      and: '所有',
      or: '任意一个',
      operators: {
        is_empty: '空',
        is_not_empty: '非空',
        like: '包含',
        not_like: '不包含',
        in: '属于',
        not_in: '不属于',
        gt: '大于',
        ge: '大于等于',
        lt: '小于',
        le: '小于等于',
        equals: '等于',
        not_equals: '不等于',
        between: '之间',
        current_user: '是当前用户'
      },
      message_box: {
        alert: '警告',
        confirm: '确认'
      }
    },
    monitor: '监控',
    image: '镜像',
    tag: '标签',
    module: {
      select_module: '选择模块',
      default_module: '默认模块'
    },
    datasource: '数据源',
    char_can_not_more_50: '不能超过50字符',
    char_2_64: '2-64个字符',
    char_1_64: '1-64个字符',
    share_success: '分享成功',
    input_id: '请输入ID',
    input_pwd: '请输入密码',
    message_box: {
      alert: '警告',
      confirm: '确认',
      ok: '确认',
      cancel: '取消'
    },
    ukey_title: 'API Keys',
    thumbnail: '缩略图',
    confirm_delete: '确认删除',
    delete_this_dashboard: '确认删除该仪表板吗?',
    delete_this_folder: '确认删除该目录吗?',
    confirm_stop: '确认停止',
    stop_success: '停止成功',
    treeselect: {
      no_children_text: '没有子节点',
      no_options_text: '没有可用选项',
      no_results_text: '没有匹配的结果'
    },
    char_count_limit: '不能超过{count}字符'
  },
  sql_variable: {
    variable_mgm: '参数设置'
  },
  v_query: {
    display_sort: '显示字段和排序字段不一致，无法进行自定义排序',
    custom_sort: '自定义排序',
    msg_center: '消息中心',
    to_be_filled: '待填报',
    the_minimum_value: '数值区间最大值必须大于等于最小值',
    before_querying: '查询条件是必填项，请设置选项值后，再进行查询！',
    here_or_click: '将右侧的字段拖拽到这里 或 点击',
    add_query_condition: '添加查询条件',
    set_filter_condition: '设置过滤条件',
    delete_condition: '删除条件',
    last_3_months: '最近 3 个 月',
    last_6_months: '最近 6 个 月',
    last_12_months: '最近 12 个 月',
    last_3_days: '最近 3 天',
    month_to_date: '月初至今',
    year_to_date: '年初至今',
    exact_match: '精确匹配',
    fuzzy_match: '模糊匹配',
    option_type: '选项类型',
    time_filter_range: '设置时间筛选范围',
    configured: '已配置',
    is_not_supported: '绑定参数后，不支持传空数据',
    contains_empty_data: '选项值包含空数据',
    unnamed: '未命名',
    cannot_be_empty: '查询条件或字段不能为空',
    the_first_level: '第一级无需配置被级联字段',
    configure_cascaded_fields: '与上一级使用同一个数据集,无需配置被级联字段',
    condition_cascade_configuration: '查询条件级联配置',
    not_reverse_cascade: '(仅上级能级联下级,不可反向级联)',
    must_be_met: '基于当前查询组件的查询条件，如果需要进行级联配置，需要满足以下条件：',
    select_data_set: '1. 展示类型：文本下拉组件和数字下拉组件；2. 选项值来源：选择数据集',
    add_cascade_configuration: '添加级联配置',
    add_cascade_condition: '添加级联条件',
    query_condition_level: '查询条件层级',
    select_query_condition: '请选择查询条件',
    select_cascaded_field: '请选择被级联字段',
    level_1: '第{msg}级',
    to_modify_it: '数据集的修改，会导致级联配置失效，因此对应的级联关系将被清除，确定修改吗？',
    be_linked_first: '请先勾选需要联动的图表及字段',
    cannot_be_performed: '所选字段类型不一致，无法进行查询配置',
    numerical_parameter_configuration: '数值参数配置必须配置最大值和最小值',
    format_is_inconsistent: '时间格式不一致',
    cannot_be_empty_de: '查询条件为必填项,默认值不能为空',
    the_start_time: '结束时间必须大于开始时间!',
    and_end_time: '时间参数配置必须配置开始时间和结束时间',
    cannot_be_empty_time: '默认时间不能为空!',
    range_please_reset: '默认值超出日期筛选范围内，请重新设置！',
    cannot_be_empty_input: '手工输入-选项值不能为空',
    option_value_field: '请选择数据集及选项值字段',
    the_data_set: '请选择数据集的选项值字段',
    cannot_be_empty_name: '字段名称不能为空',
    query_condition_setting: '查询条件设置',
    query_condition: '查询条件',
    chart_and_field: '选择关联图表及字段',
    be_switched_to: '注意:自动模式支持同数据集自动关联字段，可切换到',
    to_automatic_again: '自定义模式。切换到自定义模式后无法再切换为自动！',
    as_query_conditions: '脱敏字段，不能被设置为查询条件',
    query_condition_configuration: '查询条件配置',
    required_items: '必填项',
    display_type: '展示类型',
    text_drop_down: '文本下拉',
    text_search: '文本搜索',
    drop_down_tree: '下拉树',
    number_drop_down: '数字下拉',
    number_range: '数值区间',
    of_option_values: '选项值数量',
    tree_structure_design: '下拉树结构设计',
    the_tree_structure: '点击进行树结构设计',
    time_granularity: '时间粒度',
    the_time_granularity: '请选择时间粒度',
    option_value_source: '选项值来源',
    manual_input: '手动输入',
    query_field: '查询字段',
    display_field: '显示字段',
    the_sorting_field: '请选择排序字段',
    condition_type: '条件类型',
    single_condition: '单条件',
    with_condition: '与条件',
    or_condition: '或条件',
    hide_condition_switch: '隐藏条件切换',
    cannot_be_displayed: '图表所使用的数据集不同, 无法展示配置项',
    component_cascade_configuration: '查询组件级联配置',
    time_type: '时间类型',
    start_at: '开始于',
    end_at: '结束于',
    time_interval: '时间区间',
    interval_type: '区间类型',
    query_time_window: '动态查询时间窗口',
    maximum_single_query: '单次查询最多',
    empty_data: '空数据',
    time_selection: '时间选择',
    select_a_field: '层级字段不能为空,请选择字段!',
    add_level: '添加层级',
    tree_query_field: '下拉树查询字段',
    query_condition_width: '查询条件宽度',
    custom_condition_style: '自定义条件样式'
  },
  panel: {
    column_name: '字段名称'
  },
  visualization: {
    support_query: '仅支持添加查询组件',
    publish_update_tips: '有更新',
    filter_freeze_tips: '已存在置顶查询组件,确定切换该组件？',
    query_position: '查询组件位置',
    default: '默认',
    to_top: '置顶',
    publish_recover: '恢复到发布版本',
    publish_tips1: '发布后可查看',
    publish_tips2: '发布后可{0}',
    cancel_publish_tips: '取消发布成功',
    resource_not_published: '资源未发布',
    re_publish: '重新发布',
    published_success: '发布成功',
    cancel_publish: '取消发布',
    publish: '发布',
    freeze_top: '位置冻结在顶部',
    indicator_linkage: '指标卡联动仅携带图表过滤参数',
    gap_size: '间隙大小',
    small: '小',
    middle: '中',
    large: '大',
    custom: '自定义',
    no_details: '无明细数据',
    sync_pc_design: '同步PC设计',
    title_background: '标题背景',
    active_title_background: '激活标题背景',
    reuse_active_title_background: '复用激活标题背景',
    inactive_title_background: '非激活标题背景',
    no_hidden_components: '当前无隐藏组件',
    hidden_components: '已隐藏的组件',
    dashboard_adaptor: '缩放模式',
    scale_keep_height_and_width: '按画布比例缩放',
    scale_with_width: '按组件比例缩放',
    multi_selected: '已选{0}项',
    number1: '一',
    number2: '二',
    number3: '三',
    number4: '四',
    number5: '五',
    number6: '六',
    number7: '七',
    jump_null_tips: '字段【{0}】存在空配置，请先完善配置！',
    jump_no_banding_tips: '当前图表无绑定的查询条件',
    set_as_tips: '置为',
    rich_text_tips: '双击输入文字',
    save_conflict_tips: '已被他人更新，是否覆盖保存？',
    text_decoration: '下滑线',
    select_target_resource: '请选择目标资源',
    target_dashboard_dataV: '目标仪表板/数据大屏',
    dashboard_dataV: '仪表板/数据大屏',
    effective_during_link: '公共链接生效',
    condition_style_set: '条件样式设置',
    cell_merge_tips: '合并单元格后，行列冻结、自动换行会失效。',
    image: '图片',
    drill_set_tips: '已设置下钻',
    input_calc_data: '输入计算数据',
    excel_with_format: 'Excel(带格式)',
    show_data_info: '查看数据',
    sort: '排序',
    add_query_filter: '添加查询条件',
    edit_query_filter: '编辑查询条件',
    jump_set_tips: '已设置跳转',
    tips_world: '提示词',
    query_name_space2: '名称与选框间距',
    button_color: '按钮颜色',
    button_text: '按钮文字',
    show_button: '展示按钮',
    query_tips:
      '如果展示查询按钮，需要点击该按钮后才能触发图表查询；如果不展示查询按钮，选择完查询条件后立即触发图表查询',
    custom_query_bg_color: '自定义查询条件背景',
    query_condition_space: '查询条件间距',
    query_condition_height: '查询条件高度',
    query_condition_name: '查询条件名称',
    condition_left: '左侧',
    condition_top: '上侧',
    custom_bg_color: '自定义组件背景',
    background_img: '背景图片',
    back_parent: '返回上一级',
    ext_fullscreen: '退出全屏',
    no_edit_auth: '目标资源没有编辑权限，请联系管理员！',
    select_target_dashboard_tips: '请选择目标仪表板',
    select_target_screen_tips: '请选择目标数据大屏',
    cur_dashboard: '当前仪表板',
    cur_screen: '当前数据大屏',
    target_dashboard: '目标仪表板',
    target_screen: '目标数据大屏',
    component_id: '组件ID',
    view_id: '图表ID',
    resource_create_tips: '从顶部工具栏中选择组件，添加到这里创建',
    component_select_tips: '请选择组件...',
    border_style_dotted: '点线',
    app_export: '应用导出',
    app_name: '应用名称',
    app_version: '应用版本号',
    app_required_version: 'DataEase最低版本号',
    description: '描述',
    new_dataset: '新建数据集',
    new_datasource: '新建数据源',
    select_dataset: '选择数据集',
    select_datasource: '选择数据源',
    picture_group: '图片组',
    new: '新建',
    new_folder: '新建文件夹',
    new_screen: '新建数据大屏',
    new_dashboard: '新建仪表板',
    new_from_template: '从模板新建',
    folder: '文件夹',
    copy: '复制',
    move_to: '移动到',
    rename: '重命名',
    name: '名称',
    name_repeat: '名称重复',
    input_name_tips: '请输入{0}名称',
    select_target_folder: '请选择目标文件夹',
    select_target_tips: '不能选择自身，请选择其他文件夹',
    input_tips: '请输入名称',
    position: '所在位置',
    ds_group_name: '数据集分组名称',
    ds_group_position: '数据集分组位置',
    datasource_info: '数据源信息',
    app_datasource: '应用数据源',
    sys_datasource: '系统数据源',
    select_folder: '请选择所属文件夹',
    belong_folder: '所属文件夹',
    no_content: '没有找到相关内容',
    confirm: '确认',
    cancel: '取消',
    select_ds_group_folder: '请选择数据集分组所属文件夹',
    app_no_datasource_tips: '存在未配置的数据源',
    dataset: '数据集',
    delete: '删除',
    delete_success: '删除成功',
    save_success: '保存成功',
    change_save_tips: '当前的更改尚未保存，确定退出吗？',
    mobile_config: '移动端配置',
    visualization_component: '可视化组件',
    component_style: '组件样式',
    whole_style: '整体样式',
    time_asc: '按时间升序',
    time_desc: '按时间降序',
    name_asc: '按名称升序',
    name_desc: '按名称降序',
    delete_tips: '删除后，此文件夹下的所有资源都会被删除，请谨慎操作。',
    delete_warn: '确定删除该{0}吗？',
    save_app: '保存应用',
    base_info: '基本信息',
    close: '关闭',
    adapt_new_subject: '适应新主题',
    export_tips: '当前仪表板中{0}属于模版图表，无法导出，请先设置数据集！',
    preview_select_tips: '请在左侧选择仪表板',
    have_none_resource: '暂无仪表板',
    attribute: '属性',
    dashboard_configuration: '仪表板配置',
    batch_style_set: '批量设置样式',
    pic_import_tips: '支持JPG、PNG、GIF、SVG，大小不超过 {0}',
    pic_size_error: '图片大小不能超过15M',
    re_upload: '重新上传',
    screen_configuration: '仪表板配置',
    mobile_ios_tips: 'IOS可能无法显示',
    layer_management: '图层管理',
    hold_canvas_tips: '按住空格可拖动画布',
    keep_subject: '保持源样式',
    select_component: '选择组件',
    show_selected_only: '仅展示已选',
    no_available_component: '当前没有可用的组件',
    no_selected_component: '当前选择的组件',
    no_params_tips: '参数不能为空',
    cancel_store: '取消收藏',
    creator: '创建人',
    fullscreen: '全屏',
    edit: '编辑',
    refresh_data: '刷新数据',
    export_as: '导出为',
    select_screen_tips: '请在左侧选择数据大屏',
    no_screen: '暂无数据大屏',
    query: '查询',
    carousel: '轮播',
    carousel_time: '轮播时间（秒）',
    carousel_tips: '轮播退出编辑模式开始生效',
    carousel_tips2: '启用条件样式后，轮播失效',
    background: '背景',
    tab_title: 'Tab标签',
    style: '样式',
    event: '事件',
    board: '边框',
    color: '颜色',
    board_width: '线宽',
    board_radius: '圆角',
    enable_event_binding: '开启事件绑定',
    event_binding_tips: '事件绑定需退出编辑模式后生效,富文本开启绑定事件则内部点击事件失效',
    input_url_tips: '请输入跳转地址',
    edit_title: '编辑标题',
    custom_sort: '自定义排序',
    show_date: '显示日期',
    show_time: '显示时间',
    show_week: '显示星期',
    link_info: '链接信息',
    pic_upload_tips: '请上传图片...',
    pic_group: '图片组',
    pic_upload_tips2: '支持JPG、PNG、GIF、SVG',
    pic_adaptor_type: '图片适应方式',
    pop_area_tips: '可点击或拖拽查询组件到此位置，点击预览可查看弹窗区',
    view: '图表',
    query_component: '查询组件',
    media: '媒体',
    more: '更多',
    source_material: '素材',
    text_html: '文本',
    external_parameter_settings: '外部参数设置',
    screen_config: '大屏配置',
    screen_adaptor: '缩放方式',
    screen_adaptor_width_first: '宽度优先',
    screen_adaptor_height_first: '高度优先',
    screen_adaptor_full: '铺满全屏',
    screen_adaptor_keep: '不缩放',
    effective_during_preview: '预览时生效',
    base_config: '基础配置',
    color_config: '配色',
    refresh_config: '刷新配置',
    advanced_style_settings: '高级样式设置',
    size: '尺寸',
    font_family_select: '仪表板字体选择',
    screen_font_family_select: '数据大屏字体选择',
    button_tips: '显示放大、导出等悬浮按钮',
    display_auxiliary_grid: '显示辅助网格',
    show_pop_button: '显示弹窗区查询按钮',
    show_zoom_button: '显示放大、导出等悬浮按钮',
    keep_ratio: '保持宽高比',
    rotation_3d: '3D旋转',
    keep_size: '调整大小保持内部组件尺寸',
    no_save_tips: '存在未保存的{0}',
    no_save_tips2: '存在未保存的修改，立即恢复？',
    locate_tips: '定位到中心点',
    new_tab: '新建Tab',
    pop_area: '弹窗区域',
    refresh_view: '刷新图表',
    view_group: '组合',
    video: '视频',
    stream_media: '流媒体',
    web: '网页',
    time_component: '时间组件',
    picture: '图片',
    icon: '图标',
    rect_shape: '矩形',
    circle_shape: '圆形',
    triangle: '三角形',
    tabs: '选项卡',
    scroll_text: '跑马灯',
    component_input_tips: '双击编辑文字',
    screen_area: '大屏区域',
    rich_text: '富文本',
    date_time: '日期时间',
    board_name: '边框{0}',
    graphic: '图形',
    selected_tips: '已选 {0} 项',
    params_list: '参数列表',
    params: '参数',
    no_setting_params_name_tip: '未配置参数名',
    select_params_connect_component: '选择参数关联组件',
    connection_condition: '关联条件',
    connection_params_fields: '关联字段或参数',
    fields: '字段',
    select_all: '全选',
    select_params_connect_view: '选择关联的图表',
    setting_params_tips: '请配置参数',
    setting_params: '参数配置',
    required: '必填',
    default_value: '默认值',
    default_value_tips1: '请使用JSON数组格式 示例:',
    default_value_tips2: '单值 ["name1"], 多值 ["name1","name2"]',
    default_value_tips3: '请输入参数,如:["name1"]',
    time_year_widget: '年份过滤组件',
    time_month_widget: '年月过滤组件',
    time_date_widget: '日期过滤组件',
    time_date_range_widget: '日期范围过滤组件',
    text_select_widget: '文本下拉过滤组件',
    text_select_grid_widget: '文本列表过滤组件',
    text_input_widget: '文本搜索过滤组件',
    text_select_tree_widget: '下拉树过滤组件',
    number_select_widget: '数字下拉过滤组件',
    number_select_grid_widget: '数字列表过滤组件',
    number_range_widget: '数值区间过滤组件',
    format_error: '格式错误',
    params_setting_check_message: '参数{0}默认值格式不正确！',
    params_setting_check_message_tips: '存在未配置的参数名或者参数名称重复！',
    already_setting: '已设置',
    bubble_dynamic_effect: '气泡动效',
    save_page_tips: '请先保存当前页面',
    selected_view: '已选图表',
    used_dataset: '所用数据集',
    to_select_view: '选择图表',
    same_dataset: '同数据集',
    diff_dataset: '不同数据集',
    no_available_view: '暂无可用图表',
    linkage_setting_tips1: '配置图表间的字段关联关系',
    current_chart_source_field: '当前图表源字段',
    add_linkage_dependency_fields: '追加联动依赖字段',
    select_linkage_tips: '请先勾选需要联动的图表',
    linkage_option_tips1: '如果联动维度已配置钻取，点击维度将',
    linkage_option1: '弹出浮框，由用户选择联动或者下钻',
    linkage_option2: '同时触发联动和下钻',
    window_size: '窗口大小',
    window_size_large: '大',
    window_size_middle: '中',
    window_size_small: '小',
    target: '目标',
    linkage_view: '联动图表',
    with_filter_params: '携带查询条件',
    source_field: '源字段',
    source_filter: '源条件',
    link_target_tips1: '目标仪表板无外部参数，因此无法携带条件查询，如有需要，',
    link_target_tips2: '请前往设置外部参数',
    link_outer_params: '联动外部参数',
    indicator_name: '指标名称',
    component_size: '大小',
    component_annotation: '标注',
    alignment: '对齐',
    left_justifying: '左对齐',
    right_justifying: '右对齐',
    top_justifying: '上对齐',
    bottom_justifying: '下对齐',
    horizontally_centered: '水平居中',
    vertically_centered: '垂直居中',
    cancel_group: '取消组合',
    move_to_screen_show: '移动到大屏显示区',
    move_to_pop_area: '移动到大屏弹窗区',
    hidden: '隐藏',
    cancel_hidden: '取消隐藏',
    template_view_tips: '当前为模板图表，请更换数据集...',
    download: '下载',
    refresh: '刷新',
    head_font_color: '头部字体颜色',
    head_font_active_color: '激活字体颜色',
    head_border_color: '头部边框颜色',
    head_border_active_color: '激活边框颜色',
    underline_height: '下划线高度',
    active_font_size: '激活字体大小',
    scroll_speed: '滚动速度',
    out_params_no_select: '外部参数无需选择',
    filter_no_select: '过滤组件无需选择',
    forbidden_copy: '当前组件不允许复制',
    url_check_error: '跳转错误，URL不合法',
    view_style: '图表样式',
    view_color_setting: '图表配色',
    border_color_setting: '边框配色',
    unpublished_tips: '取消发布后，该仪表板不能被查看。确定要取消发布？',
    position_adjust_component: '位置调整',
    enable_carousel: '启用轮播',
    switch_time: '切换时间',
    position_adjust: '位置',
    space_top: '上',
    space_left: '左',
    space_width: '宽',
    space_height: '高',
    down: '下载',
    mobile_style_setting: '样式设置',
    mobile_style_setting_tips: '自定义移动端背景',
    text: '文字',
    board_background: '背景',
    title_color: '标题颜色',
    input_style: '输入框样式(颜色)',
    overall_setting: '整体配置',
    panel_background: '仪表板背景',
    component_color: '组件配色',
    chart_title: '图表标题',
    filter_component: '查询组件',
    enable_refresh_view: '开启刷新',
    enable_view_loading: '图表加载提示',
    image_size_tips: '图片请不要大于15M',
    image_add_tips: '只能插入图片',
    watermark: '水印',
    panel_get_data_error: '获取仪表板信息失败，仪表板可能已经被删除，请检查仪表板状态',
    panel_no_save_tips: '存在未保存的仪表板',
    panel_cache_use_tips: '检查到上次有仪表板未能正常保存，是否使用上次未保存的仪表板？',
    template_name_tips: '仪表板名称必填',
    panel_background_item: '自定义仪表板背景',
    panel_background_image_tips: '支持JPG、PNG、GIF、SVG',
    reUpload: '重新上传',
    create_by: '创建人',
    create_time: '创建时间',
    update_by: '最近修改人',
    update_time: '最近修改时间',
    target_url: '目标URL',
    target_url_tips: '可以点击字段用来拼接URL或者参数',
    select_world: '点击引用字段',
    template_market: '模板市场',
    template_preview: '预览模板',
    apply: '应用',
    apply_this_template: '应用此模板',
    market_network_tips: `查看模板市场模板需要服务器与模板市场({0})连通，请检查网络...`,
    enter_name_tips: '请输入仪表板名称',
    apply_template: '应用模板',
    style_template: '样式模板',
    all_type: '全部分类',
    enter_template_name_tips: '搜索模板名称',
    pic_adaptation: '适应组件',
    pic_equiratio: '等比适应',
    pic_original: '原始尺寸',
    pic_size: '图片尺寸',
    web_addr: '网页地址',
    stream_media_info: '流媒体信息',
    video_info: '视频信息',
    title_position: '标题位置',
    tab_inner_style: 'tab内部样式',
    data_format: '日期格式',
    border_color: '边框颜色',
    theme_change_warn: '主题更换',
    theme_change_tips: '更换主题将会覆盖图表相关主题属性建议提前备份,是否继续更换？',
    theme_color_change_warn: '主题色更换',
    theme_color_change_tips: '主题色变更将会覆盖原有图表属性',
    theme_color: '主题色',
    theme_color_dark: '深色',
    theme_color_light: '浅色',
    refresh_frequency: '刷新频率',
    card_color_matching: '卡片配色',
    table_color_matching: '表格配色',
    background_color: '背景颜色',
    level: '层级',
    enlarge: '放大',
    panel_style: '仪表板样式',
    multiplexing: '复用',
    panel_off: '仪表板已下架',
    batch_opt: '批量操作',
    cancel_batch_opt: '退出批量操作',
    edit_leave_tips: '是否放弃编辑离开当前界面？',
    hyperlinks: '超链接',
    is_live: '是否直播',
    yes: '是',
    no: '否',
    live_tips: '优先HTTPS链接',
    stream_media_add_tips: '请添加流媒体信息...',
    stream_mobile_tips: 'IOS终端可能无法显示',
    json_params_error: '第三方参数解析失败，请检查参数格式是否正确',
    inner_padding: '内边距',
    board_radio: '圆角',
    web_set_tips: '部分网站可能设置不允许嵌入而无法显示',
    repeat_params: '存在名称重复的参数',
    enable_outer_param_set: '启用外部参数设置',
    select_param: '请选择参数...',
    add_param_link_field: '添加参数联动字段',
    add_param: '添加参数',
    enable_param: '启用参数',
    param_name: '参数名称',
    outer_param_set: '外部参数设置',
    outer_param_decode_error: '外部参数解析错误未生效，请按规定编码方式传参',
    input_param_name: '请输入参数名称',
    params_setting: '外部参数设置',
    edit_web_tips: '编辑状态不可操作网页内部',
    no_auth_role: '未分享角色',
    auth_role: '已分享角色',
    picture_limit: '只能插入图片',
    drag_here: '请将左侧字段拖至此处',
    copy_link_passwd: '复制链接及密码',
    copy_link: '复制链接',
    copy_short_link: '复制短链接',
    copy_short_link_passwd: '复制短链接及密码',
    passwd_protect: '密码保护',
    auto_pwd: '自动生成密码',
    link: '链接',
    over_time: '有效期',
    link_expire: '链接已过期！',
    link_share: '链接分享',
    link_share_desc: '开启链接后，任何人可通过此链接访问仪表板。',
    share: '分享',
    remove_share_confirm: '确认取消当前仪表板所有分享？',
    share_in: '分享给我',
    share_out: '我的分享',
    who_share: '分享人',
    when_share: '分享时间',
    share_to: '分享对象',
    share_to_some: '把[{some}]分享给',
    org: '组织',
    role: '角色',
    user: '用户',
    datalist: '图表列表',
    group: '目录',
    panel: '仪表板',
    panel_list: '仪表板',
    groupAdd: '新建目录',
    panelAdd: '新建仪表板',
    import: '导入模板',
    tips: '提示',
    confirm_delete: '确认删除',
    search: '搜索',
    back: '返回',
    module: '组件',
    filter_module: '查询组件',
    select_by_module: '按组件选择',
    sys_template: '系统模板',
    user_template: '用户模板',
    add_category: '添加分类',
    add_app_category: '添加应用分类',
    filter_keywords: '输入关键字进行过滤',
    dashboard_theme: '仪表板主题',
    table: '表格',
    gap: '有间隙',
    no_gap: '无间隙',
    component_gap: '组件间隙',
    refresh_time: '刷新时间',
    minute: '分钟',
    second: '秒',
    photo: '图片',
    default_panel: '默认仪表板',
    create_public_links: '创建公共链接',
    to_default: '另存为默认',
    to_default_panel: '另存为默认仪表板',
    store: '收藏',
    save_to_panel: '保存为模板',
    export_to_panel: '导出为模板',
    export_to_pdf: '导出为PDF',
    export_to_img: '导出为图片',
    export_to_app: '导出为应用',
    preview: '预览',
    fullscreen_preview: '全屏预览',
    new_tab_preview: '新Tab页预览',
    select_panel_from_left: '请从左侧选择仪表板',
    template_name: '模板名称',
    template: '模板',
    category: '分类',
    all_org: '所有组织',
    import_template: '导入模板',
    copy_template: '复用模板',
    upload_template: '上传模板',
    belong_to_category: '所属类别',
    pls_select_belong_to_category: '请选择所属类别',
    template_name_cannot_be_empty: '模板名称不能为空',
    select_by_table: '按表选择',
    data_list: '数据列表',
    component_list: '组件列表',
    custom_scope: '控制范围',
    binding_parameters: '参数',
    multiple_choice: '多选',
    single_choice: '单选',
    field: '字段',
    unshared_people: '未分享人员',
    shared_people: '已分享人员',
    error_data: '获取数据出错，请联系管理员',
    canvas_size: '画布大小',
    canvas_scale: '画布比例',
    clean_canvas: '清空画布',
    insert_picture: '插入图片',
    redo: '重做',
    undo: '撤销',
    panelNull: '这是个空的仪表板，可以通过编辑来丰富内容',
    paste: '粘贴',
    cut: '剪切',
    lock: '锁定',
    unlock: '解锁',
    top_component: '置于顶层',
    bottom_component: '置于底层',
    up_component: '上移一层',
    down_component: '下移一层',
    linkage_setting: '联动设置',
    add_tab: '新增Tab',
    open_aided_design: '打开组件辅助设计',
    close_aided_design: '关闭组件辅助设计',
    open_style_design: '打开样式设计',
    close_style_design: '关闭样式设计',
    matrix_design: '矩阵设计',
    left: 'x 坐标',
    top: 'y 坐标',
    height: '高',
    width: '宽',
    backgroundColor: '背景色',
    borderStyle: '边框风格',
    borderWidth: '边框宽度',
    borderColor: '边框颜色',
    borderRadius: '圆角',
    font_size: '字体大小',
    fontWeight: '字体粗细',
    lineHeight: '行高',
    letter_spacing: '字间距',
    padding: '内间距',
    margin: '外间距',
    textAlign: '左右对齐',
    opacity: '不透明度',
    background_opacity: '背景模糊',
    verticalAlign: '上下对齐',
    text_align_left: '左对齐',
    text_align_center: '左右居中',
    text_align_right: '右对齐',
    vertical_align_top: '上对齐',
    vertical_align_middle: '居中对齐',
    vertical_align_bottom: '下对齐',
    border_style_solid: '实线',
    border_style_dashed: '虚线',
    other_module: '其他',
    content: '内容',
    default_panel_name: '默认仪表板名称',
    source_panel_name: '原仪表板名称',
    content_style: '内容样式',
    canvas_self_adaption: '自适应画布区域',
    panel_save_tips: '仪表板已变动，是否保存？',
    panel_save_warn_tips: '如果未保存，你对仪表板做的变更将会丢失！',
    do_not_save: '不保存',
    save: '保存',
    drill: '下钻',
    linkage: '联动',
    linkage_and_drill: '联动和下钻',
    jump: '跳转',
    cancel_linkage: '取消联动',
    switch_picture: '更换图片',
    select_field: '选择图表字段',
    remove_all_linkage: '清除所有联动',
    exit_un_march_linkage_field: '关联字段不能为空',
    details: '详情',
    setting: '设置',
    no_drill_field: '缺少关联字段',
    matrix: '矩阵',
    suspension: '悬浮',
    new_element_distribution: '元素移入分布方式',
    aided_grid: '辅助设计网格',
    aided_grid_open: '打开',
    aided_grid_close: '关闭',
    export_pdf_page: '分页线',
    export_pdf_page_remark: '仅对API导出仪表板PDF分页有效',
    subject_no_edit: '系统主题不能修改',
    subject_name_not_null: '主题名称需要1~20字符',
    is_enable: '是否启用',
    open_mode: '打开方式',
    new_window: '新开页面',
    now_window: '当前页面',
    pop_window: '弹窗页面',
    hyperLinks: '目标地址',
    link_open_tips: '仪表板非编辑状态可打开链接',
    data_loading: '数据准备中...',
    export_loading: '导出中...',
    export_pdf: '导出PDF',
    jump_set: '跳转设置',
    enable_jump: '启用跳转',
    column_name: '字段名称',
    enable_column: '启用字段',
    open_model: '打开方式',
    link_type: '跳转类型',
    link_outer: '外部链接',
    link_panel: '仪表板',
    select_jump_panel: '选择关联的仪表板',
    link_view: '联动图表',
    link_view_field: '联动图表字段',
    add_jump_field: '追加跳转联动依赖字段',
    input_jump_link: '请输入跳转连接',
    select_dimension: '请选择维度...',
    select_dimension_hint: '请先勾选需要跳转的字段',
    please_select: '请选择',
    video_type: '视频类型',
    online_video: '在线视频',
    streaming_media: '流媒体',
    auto_play: '自动播放',
    video_tips: '优先HTTPS链接；当前支持格式mp4,webm',
    play_frequency: '播放频率',
    play_once: '播放一次',
    play_circle: '循环播放',
    video_links: '视频链接',
    web_url: '网页地址',
    video_add_tips: '请配置视频信息...',
    link_add_tips_pre: '请配置网页信息..',
    web_add_tips_suf: '添加网页信息...',
    panel_view_result_show: '图表结果',
    panel_view_result_tips: '选择{0}会覆盖图表的结果展示数量，取值范围1~10000',
    timeout_refresh: '请求超时，稍后刷新...',
    mobile_layout: '移动端布局',
    component_hidden: '隐藏的组件',
    public_link_tips: '当前是公共链接模式，目标仪表板未设置公共链接，无法跳转',
    input_title: '请输入标题',
    show_title: '标题',
    default_settings: '默认值设置',
    choose_background: '选择组件背景',
    choose_background_tips: '组件自有背景会覆盖当前设置',
    setting_background: '设置背景',
    setting_jump: '跳转设置',
    select_view: '请选择图表',
    visual: '虚拟化',
    prohibit_multiple: '禁止同数据集多字段',
    be_empty_dir: '是空目录！',
    fold: '收起',
    expand: '展开',
    pdf_export: 'PDF 导出',
    switch_pdf_template: '切换 PDF 模板',
    pdf_template_with_params: '默认模板(加仪表板描述)',
    pdf_template_only_pic: '默认模板(只截图)',
    panel_name: '仪表板名称',
    export_user: '导出用户',
    export_time: '导出时间',
    you_can_type_here: '可以在这里输入其他内容'
  },
  template_manage: {
    name_already_exists_type: '分类名称已存在',
    the_same_category: '同一分类下，该模板名称已存在',
    name: '模板管理',
    rename: '重命名',
    edit_template: '编辑模板',
    import_template: '导入模板',
    template_name: '模板名称',
    enter_template_name_hint: '请输入模板名称',
    keywords: '搜索关键字',
    catalog_name: '分类名称',
    search_result: '的搜索结果',
    search_result_unit: '个',
    selected_count: '已选 {0} 项',
    select_all_count: '全选 {0} 项',
    add_catalog: '添加分类',
    edit_catalog: '修改分类',
    select_catalog: '选择分类',
    no_selectable_catalog: '暂无可选分类',
    please_select_catalog: '请选择分类',
    no_template: '暂无模板',
    not_found: '没有找到相关模板',
    delete_failed_hint: '无法删除分类',
    delete_failed_tip: '请先移除该分类下所有模板再进行删除分类操作',
    delete_failed_confirm: '知道了',
    delete_hint: '确定删除该模板吗?',
    delete_batch_hint: '确定删除{0}个模板吗？',
    add_success: '添加成功',
    edit_success: '修改成功',
    import_success: '导入成功',
    cover_success: '覆盖成功',
    cover_exists_hint: '当前分类存在相同模板名称，是否覆盖？',
    template_size_hint: '模板大小需小于35MB',
    hint: '提示',
    relevant_content_found: '没有找到相关内容',
    no_catalog: '当前无分类',
    delete_catalog_hint: '确定删除该分类吗？',
    delete_catalog_tip: '删除后不可恢复，是否继续？',
    illegal_name_hint: '不合法命名，请更换！',
    exists_name_hint: '当前名称已在模版管理中存在，请修改',
    get_download_link_hint: '未获取模板下载链接请联系模板市场官方',
    search_result_count: '的搜索结果是 {0} 个',
    template_center: '模版中心',
    preview: '预览'
  },
  work_branch: {
    new_empty: '空白新建',
    new_folder: '新建文件夹',
    back_to_work_branch: '返回工作台',
    recommended_dashboard: '推荐仪表板',
    template_market_official: '未获取模板下载链接请联系模板市场官方',
    create_quickly: '快速创建',
    permission_to_create: '缺少创建权限',
    new_using_template: '使用模板新建',
    template_center: '模板中心',
    view_all: '查看全部',
    relevant_templates_found: '没有找到相关模板',
    last_edited_by: '最近编辑人',
    last_edit_time: '最近编辑时间',
    big_data_screen: '数据大屏',
    big_screen: '大屏',
    dashboard: '仪表板',
    data_set: '数据集',
    data_source: '数据源',
    recently_used: '最近使用',
    my_collection: '我的收藏',
    relevant_content_found: '没有找到相关内容',
    no_content_yet: '暂无内容',
    no_favorites_yet: '暂无收藏',
    permission_denied: '没有权限',
    search_keyword: '搜索关键词',
    new_page_preview: '新页面预览',
    cancel_favorites: '取消收藏',
    open_dataset: '打开数据集',
    administrator_for_authorization: '没有任何业务菜单权限，请联系管理员授权',
    public_link_share: '公共链接分享',
    share_time_limit: '必须大于当前时间',
    ticket_setting: 'Ticket 设置',
    cannot_share_link: '已经开启全局禁用分享，分享功能暂不可用，请联系管理员！',
    open_link_hint: '开启后，用户可以通过该链接访问',
    uuid_checker: '仅支持8-16位(字母数字)，请重新输入！',
    error_password_hint: '密码格式错误，请重新填写！',
    error_link_hint: '链接格式错误，请重新填写！',
    password_null_hint: '密码不能为空，请重新输入！',
    password_hint: "密码必须是包含数字、字母、特殊字符[!{'@'}#$%^&*()_+]的4-10位字符串",
    max_ticket_count: '最多支持创建5个Ticket',
    last: '上一个',
    next: '下一个',
    recommend: '推荐',
    recent: '最近使用',
    all_types: '全部类型',
    all_source: '全部来源'
  },
  link_ticket: {
    require: '必选',
    back: '返回公共链接设置页面',
    refresh: '刷新',
    time_tips: '单位: 分钟，范围: [0-1440],0代表无期限，自首次使用ticket访问开始',
    arg_val_tips: '请输入参数值',
    arg_format_tips: '请使用JSON数组格式，示例单值[argVal]，多值[argVal1, argVal2]',
    param_error: 'Ticket 参数错误！',
    exp_error: 'Ticket 已过期！',
    disable_error: '已禁用分享功能，请联系管理员！',
    pe_require_error: '已设置有效期密码必填，当前链接无效！',
    iframe_error: '仅嵌入式版和企业版支持iframe方式内嵌公共链接！',
    link_error: '链接不存在！',
    link_exp_error: '链接已过期！'
  },
  pblink: {
    key_pwd: '请输入密码打开链接',
    input_placeholder: '请输入4～10位数字或字母',
    pwd_error: '密码错误',
    pwd_format_error: '请输入4～10位数字或字母',
    sure_bt: '确定',
    back_parent: '返回上一级'
  },
  plugin: {
    'flag-all': '全部',
    'flag-ds': '数据源插件',
    'flag-view': '图表插件',
    'flag-df': '数据填报插件'
  },
  online_map: {
    geometry: '地理信息',
    onlinemap: '在线地图',
    empty_desc: '请在左侧输入信息然后保存'
  },
  setting_basic: {
    default_open_tips: '涉及仪表板、数据大屏等资源的新建与编辑界面',
    third_platform_settings: '第三方平台设置',
    autoCreateUser: '第三方自动创建用户',
    dsIntervalTime: '数据源检测时间间隔',
    dsExecuteTime: '数据源检测频率',
    frontTimeOut: '请求超时时间 (秒)',
    logLiveTime: '操作日志保留时间 (天)',
    thresholdLogLiveTime: '阈值告警记录保留时间 (天)',
    exportFileLiveTime: '后台导出文件保留时间 (天)',
    platformOid: '第三方平台用户组织',
    platformRid: '第三方平台用户角色',
    pwdStrategy: '开启密码策略',
    dip: '禁用初始密码',
    pvp: '密码有效期',
    defaultLogin: '默认登录方式',
    shareDisable: '禁用分享',
    sharePeRequire: '分享有效期密码必填',
    defaultSort: '资源默认排序方式',
    defaultOpen: '页面打开方式',
    loginLimit: '限制登录',
    loginLimitRate: '限制登录失败次数 (次)',
    loginLimitTime: '限制登录失败时间 (分)',
    share_disable_tips: '开启后仪表板以及大屏分享无效'
  },
  resource_sort: {
    time_asc: '按创建时间升序',
    time_desc: '按创建时间降序',
    name_asc: '按名称升序',
    name_desc: '按名称降序'
  },
  open_opt: {
    new_page: '新页面打开',
    local_page: '当前页面打开'
  },
  setting_email: {
    title: '邮件设置',
    host: 'SMTP主机',
    port: 'SMTP端口',
    account: 'SMTP账号',
    pwd: 'SMTP密码',
    reci: '测试收件人',
    ssl: 'SSL',
    tsl: 'TSL'
  },
  sync_manage: {
    title: '同步管理',
    ds_search_placeholder: '搜索名称,描述'
  },
  sync_datasource: {
    title: '数据连接管理',
    source_ds: '源数据源',
    target_ds: '目标数据源',
    add_source_ds: '@:common.add@:sync_datasource.source_ds',
    add_target_ds: '@:common.add@:sync_datasource.target_ds',
    name: '名称',
    desc: '描述',
    type: '类型',
    status: '状态',
    create_time: '创建时间',
    update_time: '更新时间',
    operation: '操作',
    edit: '编辑',
    delete: '删除',
    confirm_batch_delete_target_ds: '确定删除{0}个目标数据源吗？',
    confirm_batch_delete_source_ds: '确定删除{0}个源数据源吗？',
    recently_created: '最近创建',
    has_running_task_msg:
      '执行中的任务完成后，将继续使用修改之前的配置进行同步，需手动对任务进行重新保存。',
    edit_datasource: '编辑数据源',
    add_datasource: '新建数据源',
    config_info: '配置信息',
    ds_type: '数据源类型',
    valid: '有效',
    invalid: '无效',
    start_time: '开始时间',
    end_time: '结束时间',
    ds_delete_confirm: '确定删除该数据源吗？',
    datasource: '数据源',
    select_folder: '请选择文件夹',
    sync_ds: '同步数据源',
    sync_to_datasource: '将同步至数据准备的数据源列表',
    input_ds_name: '请输入数据源名称',
    folder: '所属文件夹',
    cancel: '取消',
    save: '保存',
    next: '下一步',
    prev: '上一步',
    validate: '校验',
    validate_success: '校验成功',
    select_type: '请选择数据源类型',
    extra_params: '额外的 JDBC 连接字符串',
    remark: '备注',
    input_name: '请输入名称',
    input_limit_2_25: '{0}-{1}字符',
    input_limit_2_50: '2-50字符',
    input_limit_2_64: '2-64字符',
    input_limit_1_64: '1-64字符',
    data_source_configuration: '数据源配置',
    data_source_table: '数据源表',
    auth_method: '认证方式',
    passwd: '用户名密码',
    kerbers_info: '请确保 krb5.Conf、Keytab Key，已经添加到路径：/opt/dataease2.0/conf',
    client_principal: 'Client Principal',
    keytab_Key_path: 'Keytab Key Path',
    data_base: '数据库名称',
    user_name: '用户名',
    password: '密码',
    host: '主机名/IP地址',
    doris_host: 'Doris 地址',
    query_port: 'Query Port',
    http_port: 'HTTP端口',
    port: '端口',
    datasource_url: '地址',
    please_input_datasource_url: '请输入 Elasticsearch 地址，如: http://es_host:es_port',
    please_input_data_base: '请输入数据库名称',
    please_input_jdbc_url: '请输入 JDBC 连接',
    please_select_oracle_type: '选择连接类型',
    please_input_user_name: '请输入用户名',
    please_input_password: '请输入密码',
    please_input_host: '请输入主机',
    please_input_url: '请输入URL地址',
    please_input_port: '请输入端口',
    please_input_be_port: '请输入BE HTTP端口',
    please_input_be_ip: '请输入BE IP地址',
    please_input_fe_port: '请输入FE HTTP端口',
    modify: '编辑数据源',
    validate_failed: '校验失败',
    oracle_connection_type: '服务名/SID',
    oracle_sid: 'SID',
    oracle_service_name: '服务名',
    get_schema: '获取 Schema',
    schema: 'Schema',
    charset: '字符集',
    targetCharset: '目标字符集',
    please_choose_schema: '请选择数据库 Schema',
    please_choose_charset: '请选择数据库字符集',
    please_choose_targetCharset: '请选择目标字符集',
    edit_datasource_msg: '修改数据源信息，可能会导致该数据源下的数据集不可用，确认修改？',
    repeat_datasource_msg: '已经存在相同配置的数据源信息, ',
    in_valid: '无效数据源',
    initial_pool_size: '初始连接数',
    min_pool_size: '最小连接数',
    max_pool_size: '最大连接数',
    max_idle_time: '最大空闲(秒)',
    bucket_num: 'Bucket 数量',
    replication_num: '副本数量',
    please_input_bucket_num: '请输入 Bucket 数量',
    please_input_replication_num: '请输入副本数量',
    acquire_increment: '增长数',
    connect_timeout: '连接超时(秒)',
    please_input_initial_pool_size: '请输入初始连接数',
    please_input_min_pool_size: '请输入最小连接数',
    please_input_max_pool_size: '请输入最大连接数',
    please_input_max_idle_time: '请输入最大空闲(秒)',
    please_input_acquire_increment: '请输入增长数',
    please_input_query_timeout: '请输入查询超时',
    please_input_connect_timeout: '请输入连接超时(秒)',
    no_less_then_0: '高级设置中的参数不能小于零',
    port_no_less_then_0: '端口不能小于零',
    priority: '高级设置',
    jdbcUrl: 'JDBC 连接',
    _ip_address: '请输入主机名/IP地址',
    display_name: '显示名称',
    connection_mode: '连接方式',
    please_select: '请选择',
    query_timeout: '查询超时',
    description: '描述',
    tips: '提示',
    replication: 'BE 副本数',
    replication_tip: 'BE 节点数量'
  },
  sync_summary: {
    summary: '概览',
    data_source_number: '数据源数量',
    task_number: '任务数量',
    execution_count: '执行次数',
    execution_results_in_the_past_7_days: '过去7天执行情况',
    sync_status_distribution: '同步状态分布'
  },
  sync_task: {
    title: '任务管理',
    task_list: '任务列表',
    log_list: '任务日志',
    add_task: '添加任务',
    name: '名称',
    desc: '描述',
    status: '状态',
    create_time: '创建时间',
    update_time: '更新时间',
    operation: '操作',
    edit: '编辑',
    delete: '删除',
    start: '启用',
    stop: '停止',
    terminated: '终止同步',
    running_one: '执行一次',
    trigger_last_time: '上次执行时间',
    trigger_next_time: '下次执行时间',
    status_success: '成功',
    status_running: '同步中',
    status_failed: '失败',
    status_stopped: '任务停止',
    status_waiting: '等待同步',
    status_done: '任务结束',
    status_terminated: '终止',
    status_connection_lost: '连接丢失',
    log: '日志',
    show_log: '查看日志',
    last_execute_result: '上次执行结果',
    execute_result: '执行结果',
    task_status: '任务状态',
    sync: '同步',
    target_table: '目标表',
    batch_del: '批量删除',
    selection_info: '已选 {0} 项',
    clear_button: '清空',
    task_text: '任务',
    hour: '小时',
    day: '天',
    week: '周',
    month: '月',
    year: '年',
    minute: '分钟',
    second: '秒',
    hour_minute_second: '时:分:秒',
    please_enter_task_name: '请输入任务名称',
    input_limit_255: '长度不能超过255个字符',
    please_enter: '请输入',
    please_cron: '请输入可用的Cron表达式：',
    please_choose: '请选择',
    please_choose_start_time: '请选择开始时间',
    please_choose_end_time: '请选择结束时间',
    end_time_must_be_later_than_start_time: '结束时间必须大于开始时间！',
    please_choose_database_type: '请选择数据库类型',
    please_choose_database: '请选择数据库',
    please_choose_table: '请选择表',
    please_choose_incremental_field: '请选择增量字段',
    please_enter_table_name: '请输入表名',
    input_limit_64: '长度不能超过64个字符',
    must_be_met_the_table_name: '必须以字母开头，并且只能包含字母、数字、下划线',
    please_choose_partition_type: '请选择分区类型',
    please_enter_end_offset: '请输入结束偏移度',
    please_choose_partition_interval_unit: '请选择分区间隔的单位',
    please_enter_partition_column_value: '请输入分区列值',
    input_limit_4096: '长度不能超过4096个字符',
    please_enter_starting_value: '请输入起始数值',
    please_enter_end_value: '请输入结束数值',
    please_enter_numerical_range_interval: '请输入数值分区间隔',
    please_choose_time_range: '请选择时间范围',
    edit_success: '修改成功',
    add_success: '添加成功',
    target_database_status_is_abnormal: '目标数据库状态异常',
    edit_task: '编辑任务',
    basic_information: '基本信息',
    source_database: '源数据库',
    target_database: '目标数据库',
    task_time_out_time: '任务超时时间（秒）',
    effective_if_greater_than_0: '单位秒，大于0时生效',
    retry_attempts_on_failure: '失败重试次数',
    sync_frequency: '同步频率',
    sync_immediately: '立即同步',
    sync_cron: '表达式设定',
    sync_fixed_frequency: '固定频率',
    cron_expression: 'Cron表达式',
    each: '每',
    sync_once: '同步一次',
    confirm: '确认',
    msg_get_database_table_failed: '获取数据库表失败',
    msg_source_database_status_is_abnormal: '源数据库状态异常',
    database: '数据库',
    database_type: '类型',
    query_method: '查询方式',
    please_choose_data_extraction_method: '请选择数据抽取方式',
    table: '表',
    sql_tip_1:
      '该方式在获取列类型的长度或者精度时，并不总是返回用户设置的精确和长度，但它仍然可以作为一个参考值，用于确定结果集中每列的最大显示长度。',
    sql_tip_2:
      '如需获取更精确的列类型长度精度，请使用库表方式，或者在下一步中的字段映射中进行长度精度的设置。',
    please_enter_sql: '请输入查询SQL',
    msg_confirm_delete_field: '确定删除该字段？',
    source_field: '源字段',
    field_type: '类型',
    field_length: '长度',
    field_precision: '精度',
    field_key: '键',
    field_index: '索引',
    field_comment: '注释',
    confirm_delete_field: '确定删除 {0} 字段？',
    msg_field_list_empty_tip:
      '字段列表不能为空，不能存在名称为空或者字段类型为UNKNOWN的数据,请检查',
    next_week: '未来一周',
    next_month: '未来一个月',
    next_three_month: '未来三个月',
    must_be_start_less_end: '数值范围结束数值必须大于起始数值',
    must_be_partition_interval_greater_than_0: '分区间隔必须大于0',
    must_be_partition_interval_less_end_start_difference: '分区间隔必须小于结束数值与起始数值差值',
    date: '日期',
    list: '列',
    number: '数值',
    define_mapping_field: '定义映射字段',
    target_database_type: '目标数据库类型',
    delete_field: '删除字段',
    add_field: '添加字段',
    edit_field: '编辑字段',
    add_all_field: '添加所有字段',
    fault_tolerance_rate: '容错率',
    fault_tolerance_rate_tip:
      '0～1，默认为0，即表示同步批次数据有一条错误数据时，整个批次的导入任务将会失败。',
    incremental_sync: '增量同步',
    incremental_sync_tip_1: '全量：全量覆盖同步',
    incremental_sync_tip_2: '增量：根据增量字段增量同步，增量字段必须是整型或时间类型',
    incremental_field: '增量字段',
    enable_partition: '启用分区',
    enable_partition_tip: '启用分区，需要字段列表不能有空值',
    partition_type: '分区类型',
    partition_field: '分区字段',
    on: '启用',
    off: '关闭',
    picker_to: '至',
    picker_start: '开始',
    picker_end: '截止',
    end_offset: '结束偏移',
    number_range: '数值范围',
    partition_interval: '分区间隔',
    partition_column_value: '分区列值',
    partition_column_value_placeholder: '分区格式为:p1:"v1","v2","v3";p2:"v1","v2"',
    partition_interval_unit: '分区间隔单位',
    input_limit: '长度不能超过{0}个字符',
    cannot_begin_with_number: '字段名称不能以数字开头',
    duplicate_field_tip: '重复字段[{0}]，同一个源字段不能映射多次',
    duplicate_name_error: '名称重复[{0}]',
    confirm_batch_delete: '确定批量删除任务',
    op_success: '操作成功',
    search_input_name_desc_placeholder: '搜索名称、描述',
    confirm_delete_msg: '确定删除？',
    target_table_info: '目标表信息',
    confirm_clear_msg: '确定清理 {0} 吗?',
    clear: '清理',
    op_success_refresh: '执行成功,请稍后刷新',
    execute_time: '执行时间',
    clear_log: '清理日志',
    search_input_name_id_placeholder: '搜索名称、ID',
    log_id: '日志ID',
    op: '操作',
    view_execute_log: '查看执行日志',
    submit_true: '确定',
    please_choose_clear_method: '请选择清理方式',
    last_1_days_log: '1 天前的日志',
    last_1_weeks_log: '1 周前的日志',
    last_1_months_log: '1 个月前的日志',
    last_3_months_log: '3 个月前的日志',
    last_6_months_log: '6 个月前的日志',
    last_1_years_log: '1 年前的日志',
    execute_log: '执行日志',
    done: '完成',
    connection_lost: '连接断开',
    task_name: '任务名称',
    es_params_label: '查询参数',
    es_params_tip: '请遵循 Elasitcsearch 的查询语法',
    dynamic_partition_enable: '动态分区',
    time_end: '结束',
    es_query_param_formatter_error: '查询参数格式错误，请输入正确的JSON格式，请检查',
    show_task_id: '查看任务ID'
  },
  watermark: {
    support_params: '当前支持的参数：',
    enable: '启用水印',
    excel_enable: '导出数据文件开启水印',
    enable_panel_custom: '允许仪表板或数据大屏单独打开或者关闭水印',
    content: '水印内容',
    custom_content: '自定义公式',
    account: '账号',
    nick_name: '昵称',
    ip: 'IP',
    now: '当前时间',
    watermark_color: '水印颜色',
    watermark_font_size: '水印字号',
    watermark_space: '水印间距',
    horizontal: '横向间距',
    vertical: '纵向间距',
    reset: '重置',
    preview: '预览',
    save: '保存'
  },
  appearance: {
    give_up: '放弃更新',
    save_apply: '保存并应用'
  },
  report: {
    title: '定时报告',
    task_name: '任务名称',
    last_exec_time: '上次执行时间',
    last_exec_result: '上次执行结果',
    task_status: '任务状态',
    next_exec_time: '下次执行时间',
    creator: '创建人',
    create_time: '创建时间',
    status_wait: '等待发送',
    status_stop: '任务停止',
    status_finish: '任务结束',
    status_send: '发送中',
    search_tips: '通过任务名称搜索',
    report_title: '任务列表',
    instance_title: '任务日志',
    add_task: '添加任务',
    lark_groups: '飞书群',
    larksuite_groups: '国际飞书群',
    send_setting: '发送设置',
    retrying_settings: '发送失败重试设置',
    start_time: '开始时间',
    end_time: '结束时间',
    once_a_day: '每天',
    once_a_week: '每周',
    once_a_month: '每月',
    hour: '小时',
    day: '天',
    week: '周',
    month: '月',
    week_mon: '一',
    week_tue: '二',
    week_wed: '三',
    week_thu: '四',
    week_fri: '五',
    week_sat: '六',
    week_sun: '日',
    every_exec: '执行一次',
    date: '日',
    last_status_running: '运行中',
    last_status_fail: '失败',
    last_status_success: '成功',
    batch_confirm: '确定批量删除任务？',
    fire_now_tips: '手动执行任务后5s可以再次手动执行！',
    task_running_tips: '任务正在执行！',
    start_success: '发起成功，正在执行',
    form: {
      title: '报告主题',
      content: '报告正文',
      send_content: '发送内容',
      filter: '设置查询组件参数',
      water_mask: '水印设置',
      show_water_mask: '显示水印',
      format: '格式',
      view_data: '图表数据',
      pixel: '仪表板分辨率',
      reci_setting: '接收设置',
      retrying: '失败重试',
      retrying_rate: '重试间隔（分钟）',
      please_input_positive_int: '请输入 {0} 正整数',
      rate: '发送频率'
    },
    filter: {
      title: '设置查询组件默认值',
      reset: '还原默认值',
      reset_all: '还原所有查询条件默认值',
      empty_tips: '过滤组件 {0} 条件必填，请先填写条件!'
    }
  },
  variable: {
    give_up: 's',
    save_apply: '保存并应用'
  },
  data_fill: {
    data_fill: '数据填报',
    fill_in_the_task: '填报任务',
    data_fill_name: '数据填报名称',
    p_data_fill_name: '请输入数据填报名称',
    save_df_success: '保存数据填报成功',
    permission: '填报权限',
    enable: '开启',
    enable_hint: '数据填报开启后，可将表单数据存放至数据源中，一旦开启后，后期不允许关闭。',
    new_folder: '新建文件夹',
    form_manage: '表单管理',
    my_job: '我的填报',
    short_name: '填报',
    disable_data_fill_hint: '关闭数据填报后，表单数据将提交失败，确定关闭？',
    enable_data_fill_hint: '启用后，允许在数据源数据库中新建表，并将表单数据存放至表中',
    todo: '待填报',
    finished: '已填报',
    expired: '已过期',
    all: '全部',
    required_select: '必选',
    condition: '过滤值',
    add_condition: '添加条件',
    disable_edit: '禁止编辑',
    enable_edit: '允许编辑',
    select_component: '请选择组件',
    set_condition: '设置条件',
    move_to: '移动到',
    rename: '重命名',
    delete: '删除',
    move_success: '移动成功',
    rename_success: '重命名成功',
    create_success: '新建成功',
    create_form: '新建表单',
    create_folder: '新建文件夹',
    order_by_create_time_asc: '按创建时间升序',
    order_by_create_time_desc: '按创建时间降序',
    order_by_name_asc: '按照名称升序',
    order_by_name_desc: '按照名称降序',
    delete_folder_hint: '删除后，此文件夹下的所有资源都会被删除，请谨慎操作。',
    confirm_delete_folder: '确定删除该文件夹吗',
    confirm_delete_form: '确定删除该表单吗',
    confirm_delete_multiple_data: '确定删除 {0} 条数据吗？',
    confirm_delete_data: '确定删除数据？',
    no_form: '暂无表单',
    on_the_left: '请在左侧选择表单',
    exporting: '后台导出中,可前往',
    progress_to_download: '查看进度，进行下载',
    clear_selection: '取消选择',
    truncate_table: '清空数据',
    truncate: '清空',
    confirm_truncate_table: '确定要清空数据吗？',
    add_search_condition: '添加筛选条件',
    form: {
      set_enableDefaultTime: '默认值',
      currentTime: '当前时间',
      defaultTime: '固定时间',
      please_select_valid_column: '请选择符合要求的字段',
      create_type: '创建方式',
      create_new_table: '创建新表',
      bind_exists_table: '绑定已有表',
      create_new_column: '新建字段',
      select_exists_column: '绑定字段',
      table_primary_key_not_exists: '此表不存在主键，不支持关联',
      add_detail_columns: '添加字段描述',
      detail_columns: '字段描述',
      display_name: '显示名称',
      show_more_detail: '查看更多',
      confirm_to_mark_as_complete: '确认标记为完成?',
      mobile_number_format_is_incorrect: '手机号码格式不正确',
      email_format_is_incorrect: '邮箱格式不正确',
      name: '名称',
      rename: '重命名',
      untitled: '未命名表单',
      create_new_form: '新建表单',
      copy_new_form: '复制表单',
      edit_form: '编辑表单',
      title: '标题',
      no_form: '暂无表单，点击',
      form_list_name: '填报表单',
      create_form: '新建表单',
      please_select: '请选择',
      component: '组件',
      component_setting: '组件设置',
      hint: '提示词',
      input_limit_50: '不超过50个字符',
      input_limit_max: '不超过{0}个字符',
      input_limit_min: '不少于{0}个字符',
      option: '选项',
      form_setting: '表单设置',
      confirm_delete: '确认删除？(不会删除已创建的数据库表)',
      list: '表单数据',
      record: '提交记录',
      task_manage: '任务管理',
      form_name: '表单名称',
      commit_type: '表单提交方式',
      commit_type_append: '数据追加',
      commit_type_update: '数据更新',
      commit_rule: '更新条件',
      commit_rule_add: '添加更新规则',
      commit_rule_settings: '更新规则设置',
      commit_rule_set: '已设置',
      folder: '所属文件夹',
      datasource: '数据源',
      table: '数据库表',
      creator: '创建人',
      createTime: '创建时间',
      operation: '操作',
      operator: '操作人',
      operate_time: '操作时间',
      modify: '修改',
      show: '查看',
      delete: '删除',
      show_data: '查看数据',
      text: '普通文本',
      number: '数字',
      tel: '手机号',
      email: '邮箱',
      duplicate_error: '重复',
      value_not_exists: '值不存在',
      range_separator: '分割字符',
      start_hint_word: '开始提示词',
      end_hint_word: '结束提示词',
      input_type: '格式类型',
      date_type: '展示粒度',
      check: '校验',
      set_required: '设置为必填项',
      set_unique: '不允许重复值',
      set_multiple: '允许多选',
      use_datetime: '使用日期时间',
      custom: '自定义',
      use_datasource: '绑定数据源',
      bind_column: '绑定字段',
      bind_complete: '已绑定',
      option_value: '选项值',
      add_option: '添加选项值',
      form_name_cannot_none: '表单名称不能为空',
      form_update_rule_none: '请配置更新规则',
      form_components_cannot_null: '请添加表单组件',
      option_list_cannot_empty: '选项值不能为空',
      option_list_datasource_cannot_empty: '选项值绑定数据源配置不能为空',
      component_setting_error: '组件设置错误',
      table_name: '数据库表名',
      form_column: '表单字段',
      column_name: '数据库表字段名称',
      column_type: '数据库字段类型',
      create_index: '创建索引',
      add_index: '新增索引',
      index_name: '索引名称',
      create_index_hint: 'MySQL 8.0 或 MariaDB 10.8.0 以下版本不支持索引降序排序',
      index_column: '索引字段',
      order: '排序',
      order_asc: '升序',
      order_desc: '降序',
      order_none: '默认排序',
      add_column: '新增字段',
      please_insert_start: '请输入开始时间',
      please_insert_end: '请输入结束时间',
      save_form: '保存表单',
      default: '默认',
      default_built_in: '内建数据库',
      lt_check: '值需要小于{0}: {1}',
      gt_check: '值需要大于{0}: {1}',
      le_check: '值需要小于等于{0}: {1}',
      ge_check: '值需要大于等于{0}: {1}',
      status: '填报状态',
      status_0: '未填报',
      status_1: '已完成'
    },
    database: {
      nvarchar: '字符串',
      text: '长文本',
      number: '整型数字',
      decimal: '小数数字',
      datetime: '日期'
    },
    data: {
      data_not_exists: '数据不存在',
      cannot_select_all: '不能全选',
      commit_time: '提交时间',
      confirm_delete: '确认删除?',
      add_data: '添加数据',
      batch_upload: '批量上传',
      download: '下载',
      download_template: '下载模板',
      insert_data: '插入数据',
      batch_insert_data: '批量导入',
      batch_insert_data_with_count: '批量导入，共{0}条数据',
      update_data: '更新数据',
      delete_data: '删除数据',
      recent_committer: '最近提交人',
      recent_commit_time: '最近提交时间',
      start: '开始',
      end: '结束',
      id_is: 'ID为[',
      data_not_found: ']的数据不存在'
    },
    task: {
      commit_operate_type: '提交类型',
      committer: '提交人',
      send_status: '任务下发状态',
      df_task_status: '任务填报状态',
      time_check_5_minute_later_than_current: '不能小于当前时间5分钟后',
      time_check_later_than_current: '不能小于当前时间',
      time_check_earlier_than_end: '不能大于结束时间',
      time_check_later_than_start: '不能小于开始时间',
      confirm_exit_without_save: '当前的更改尚未保存,确定退出吗?',
      deliver_now: '立即下发',
      deliver_scheduled: '定时下发',
      logic_filter: '条件筛选',
      enum_filter: '枚举筛选',
      cannot_be_all_disabled: '所有组件不能全为禁止',
      template_hint_title: '设置说明如下',
      template_hint_1: '当组件被设置为禁止编辑时，用户填写表单时不允许修改',
      template_hint_2: '当组件被设置为允许编辑时，用户填写表单时允许修改',
      finish_rate_hint: '填报完成率=已填报数据条数/下发填报条数*100%',
      distribute_frequency: '发送频率',
      one_time: '仅下发一次',
      interval: '定期下发',
      execute_now: '立即执行',
      end_time: '任务结束时间',
      please_select_end_time: '请选择任务结束时间',
      end_time_error: '结束时间必须大于当前时间',
      distribute_setting: '下发设置',
      task_distribute_setting: '任务下发设置',
      receive_object: '接收对象',
      receive_fit_column: '接收对象匹配字段',
      form_template_setting: '表单模板设置',
      template_setting: '模板设置',
      form_filter_setting: '表单过滤设置',
      filter_setting: '过滤设置',
      component: '组件标题',
      receiver: '接收人',
      receiver_not_null: '接收人不能为空！',
      commit_type: '数据提交方式',
      person: '人',
      select_receiver: '选择接收人',
      exec_logs: '执行日志',
      assign_num: '下发人数',
      finished_user_num: '已完成人数',
      unfinished_user_num: '未完成人数',
      finished_rate: '完成率',
      confirm_batch_delete: '确定批量删除任务',
      name: '名称',
      creator: '创建人',
      create_time: '创建时间',
      rate_type: '任务下发模式',
      task_status: '任务状态',
      task_progress: '已填报数/总数',
      task_name: '任务名称',
      add_task: '添加任务',
      task_remain_time: '任务有效期',
      task_sender: '任务下发人',
      start_filling: '立即填报',
      task_distribute_time: '任务下发时间',
      task_expiration_time: '任务过期时间',
      task_finished_time: '任务完成时间',
      task_end_time: '任务截止时间',
      edit_data: '编辑数据',
      show_data: '查看数据',
      confirm_enable: '确认启动任务？（单次任务会新建下发任务）',
      confirm_disable: '确认停止任务？',
      edit_task: '编辑任务',
      create_task: '新建任务',
      edit: '编辑',
      stop: '停止',
      start: '启动',
      delete: '删除',
      no_time_limit: '不限时',
      todo: '待办项',
      finished: '已提交',
      expired: '已过期',
      running: '进行中',
      assigned_task: '已下发任务',
      task_finish_in: '在任务下发',
      task_finish_in_suffix: '内完成填报',
      open_sub_task: '查看已下发任务'
    },
    search_by_commit_name: '根据操作人名称搜索'
  },
  threshold: {
    drawer_title: '设置阈值告警',
    table_name: '阈值告警名称',
    status: '数据状态',
    base_setting: '基本设置',
    threshold_setting: '告警设置',
    name: '告警名称',
    grid_title: '告警管理',
    grid: '告警列表',
    record: '检测记录',
    module_name: '阈值告警',
    setting: '阈值告警设置',
    no_view_tip: '请在设置阈值告警前先保存',
    selected_view: '已选择图表：',
    please_enter_name: '请输入告警名称',
    detection_time: '检测时间',
    rules: '告警规则',
    rules_invalid: '告警规则无效',
    once_a_hour: '每小时',
    once_a_day: '每天',
    once_a_week: '每周',
    once_a_month: '每月',
    email: '邮件',
    wecom: '企业微信',
    dingtalk: '钉钉',
    lark: '飞书',
    larksuite: '国际飞书',
    notification_setting: '告警通知',
    notification_method: '通知方式',
    notification_user: '通知人',
    notification_email: '邮件通知',
    please_enter_email: '请输入邮箱，回车确认',
    please_choose_lark_group: '请选择飞书群',
    notification_content: '通知内容',
    default_msg: '默认消息',
    custom_msg: '自定义消息',
    msg_title: '消息标题',
    msg_content: '消息正文',
    repeat_send: '是否重复发送',
    recipient: '接收人',
    choose_recipient: '选择接收人',
    trigger_alarm: '触发告警',
    abnormal_alarm: '异常告警',
    choose_recipient_tip: '已选 {0} 人， {1} 角色',
    notification_methods_cannot_be_empty: '通知方式不能同时为空',
    recipient_setting: '设置接收人',
    attention_quota_tip: '您关注的指标',
    pay_attention_in_time: '。请及时关注。',
    msg_preview: '消息预览',
    average: '平均值',
    next_time: '下一',
    end_of_year: '年末',
    ago: '前',
    later: '后'
  },
  relation: {
    no_permission: '没有查看权限',
    datasource: '数据源',
    dataset: '数据集',
    dashboard: '仪表板',
    dataV: '数据大屏',
    analysis: '血缘分析',
    resource_type: '资源类型',
    pls_choose: '请选择',
    choose_resource: '选择资源',
    list_chart: '列表图表',
    mind_map: '脑图',
    index: '序号',
    datasource_name: '数据源名称',
    dataset_name: '数据源集名称',
    dashboard_name: '仪表板名称',
    dataV_name: '数据大屏名称',
    retract: '收起',
    expand: '展开',
    node_info: '节点详情',
    node_name: '节点名称',
    creator: '创建人',
    last_update_time: '最近更新时间',
    dependent: '资源依赖',
    new_page: '新页面打开'
  },
  copilot: {
    talking_analysis: 'Copilot 对话分析',
    hello: '你好，我是 Copilot 对话分析',
    click_talk: '点击一下，开启可视化图表解答模式～',
    know: '我知道了',
    ds_prefix: '当前数据集为【',
    ds_suffix: '】，切换数据集将清空当前会话。',
    confirm: '确定要切换数据集吗？',
    choose_dataset: '选择数据集',
    pls_choose_dataset: '请选择数据集',
    chart: '图表',
    line: '折线图',
    bar: '柱状图',
    pie: '饼图',
    sorry: '抱歉，根据已知信息无法回答这个问题，请重新描述你的问题或提供更多信息～',
    hello1: '您好，我是 Copilot，很高兴为你服务～',
    answer: '回答中',
    example: '您可以问我: 2020年各个销售部门销售额占比的饼图',
    switch_chart: '切换图表类型',
    switch_table: '切换至明细表',
    download: '下载'
  },
  userCenter: {
    enable: '启用',
    invalid: '失效',
    binding_settings: '绑定设置',
    wechat: '企业微信',
    wechat_desc: '绑定后，您可通过企业微信扫码进行登录',
    dingtalk: '钉钉',
    dingtalk_desc: '绑定后，您可通过钉钉扫码进行登录',
    lark: '飞书',
    lark_desc: '绑定后，您可通过飞书扫码进行登录',
    international_lark: '国际飞书',
    international_lark_desc: '绑定后，您可通过国际飞书扫码进行登录',
    bind: '绑定',
    unbind_success: '解绑成功',
    confirm_unbind_dingtalk: '确定解除{0}绑定吗？',
    pls_use: '请使用',
    bind_use_qr: '扫描二维码绑定',
    pls_use_dingtalk: '请使用钉钉扫描二维码登录',
    api_limit_5: '最多支持创建5个ApiKey',
    tips: '提示',
    create: '创建',
    click_to_hind: '点击隐藏',
    click_to_show: '点击显示',
    view_api: '查看API',
    enable_success: '启用成功',
    disabled_success: '禁用成功',
    delete_api_key: '确定删除该 API key 吗? ',
    api_key_desc:
      'API Key 是您访问 DataEase API 的密钥，具有账户的完全权限，请您务必妥善保管！不要以任何方式公开 API Key 到外部渠道，避免被他人利用造成安全威胁。'
  },
  free: {
    title: '游离资源管理',
    no_data: '暂无游离资源',
    sync: '迁移',
    quick: '一键',
    batch: '批量',
    resource: '资源',
    view_association: '查看血缘关系',
    quick_sync_tips: '所有仪表板、数据大屏、数据集、数据源，将全部迁移到【迁移资源】文件夹。',
    batch_sync_tips:
      '1. 与选中资源相关的仪表板、数据大屏、数据集、数据源，也将一并迁移到对应资源的【迁移资源】文件夹；',
    batch_sync_tips1: '2. 迁移文件夹将同时迁移该文件夹下的子文件夹和资源。',
    quick_del_confirm: '确定删除所有游离资源吗？',
    quick_del_tips: '资源删除后，不可撤销。',
    quick_sync_confirm: '确定迁移所有游离资源吗？',
    quick_sync_confirm_tips: '迁移资源后，不可撤销，请谨慎操作。',
    batch_sync_confirm: '确定迁移 {0} 项及其相关游离资源吗？',
    single_sync_confirm: '确定迁移该资源吗',
    batch_del_confirm: '确定删除 {0} 项资源吗？',
    batch_del_confirm_tips: '资源删除后，不可撤销，请谨慎操作。',
    del_tips_dataset: '删除数据集会造成相关数据集失效，确定删除？',
    del_tips_datasource: '有数据集正在使用这些数据源，删除后数据集不可用，确定删除？',
    single_del_confirm: '确定删除该{0}吗？',
    single_del_tips_dataset: '该数据集存在如下血缘关系，删除会造成相关图表失效，确定删除？',
    single_del_tips_datasource: '有 {0} 个数据集正在使用此数据源，删除后数据集不可用，确定删除？',
    folder: '文件夹',
    del_folder_tips: '删除后，此文件夹下的所有资源都会被删除，请谨慎操作。',
    sync_to_org: '迁移至目标组织',
    sync_org_placeholder: '请选择目标组织',
    relation_picture: '血缘关系图',
    save_error: '禁止操作【迁移资源】目录'
  },
  security: {
    title: '安全设置'
  },
  setting_mfa: {
    title: 'MFA 设置',
    status: '全局启用 MFA 认证',
    platformEnable: '第三方认证开启 MFA',
    exp: 'MFA 校验有效期',
    otpName: 'OPT 扫描后的名称',
    rate: 'OTP 延迟有效次数',
    status_0: '未启用',
    status_1: '所有用户',
    status_2: '仅系统管理员',
    platform_tips: '第三方登录方式包括：OIDC、CAS',
    exp_tips: '单位：秒，目前仅在查看账号密码校验 MFA 时生效',
    user_enable: 'MFA 多因子认证',
    code_input_msg: '请输入 {0} 位数字',
    bind_ready: '已绑定',
    bind_unready: '未绑定',
    bind_title: '绑定 MFA 多因子认证',
    enable_switch_tips: '管理员已设置所有用户开启 MFA 认证',
    reset_key_tips: '重置 MFA',
    step_1: '安装应用',
    step_2: '绑定 MFA 验证器',
    unbind_confirm: '确定解除 MFA 多因子认证绑定吗？',
    mfa_code: 'MFA 验证码',
    install_app: '安装应用',
    install_1: '1、请在手机端或微信小程序下载并安装 MFA 验证器应用',
    install_2: '2、安装完成后点击下一步进入绑定页面(如已安装，直接进入下一步)',
    phone_download: '手机下载',
    scan_qr_tips: '使用 MFA 验证器应用扫描以下二维码，获取6位验证码',
    code_miss_tips: '如果不能提供 MFA 验证码，请联系管理员!'
  },
  threshold_warn: {
    all: '全部',
    normal: '正常',
    abnormal: '异常',
    batch_del_confirm: '确定删除 {0} 个告警任务吗？',
    search_placeholder: '通过告警名称搜索',
    chart_name: '图表名称',
    warn_status: '告警状态'
  },
  webhook: {
    title: 'Webhook 管理',
    add: '添加 Webhook',
    search_placeholder: '通过名称搜索',
    content_type: '内容类型',
    del_confirm: '确定删除该 Webhook吗？',
    batch_del_confirm: '确定删除 {0} 个 Webhook吗'
  }
}
