<template>
  <div class="list-container" :class="[`list-style-${propValue.listStyle}`]" :style="containerStyle">
    <!-- 列表头部 -->
    <div v-if="propValue.showHeader && propValue.title" class="list-header" :style="headerStyle">
      <div class="list-title">{{ propValue.title }}</div>
    </div>

    <!-- 列表内容 -->
    <div class="list-content" :style="contentStyle">
      <div v-if="listItems.length === 0" class="list-empty" :style="emptyStyle">
        {{ propValue.emptyText || '暂无数据' }}
      </div>

      <!-- 通知公告样式 -->
      <template v-else-if="propValue.listStyle === 'notice'">
        <div
          v-for="(item, index) in listItems"
          :key="index"
          class="list-item notice-item"
          :style="getItemStyle(index)"
          @click="handleItemClick(item, index)"
        >
          <!-- 左侧图片 -->
          <div v-if="propValue.showIcon" class="item-icon" :style="iconStyle">
            <img
              v-if="getFieldValue(item, 'icon')"
              :src="getFieldValue(item, 'icon')"
              :style="imageIconStyle"
              @error="handleImageError"
              alt="图标"
            />
            <div v-else class="default-icon" :style="defaultIconStyle"></div>
          </div>

          <!-- 中间内容 -->
          <div class="item-content" :style="contentItemStyle">
            <div class="item-title" :style="titleStyle">
              {{ getFieldValue(item, 'title', index) }}
            </div>
            <div v-if="propValue.showDescription" class="item-description" :style="descriptionStyle">
              {{ getFieldValue(item, 'description') }}
            </div>
            <div v-if="propValue.showTime" class="item-time" :style="timeStyle">
              {{ getFieldValue(item, 'time') }}
            </div>
          </div>

          <!-- 右侧按钮/操作区 -->
          <div v-if="propValue.showAction" class="item-action" :style="actionStyle">
            <el-button
              v-if="propValue.actionType === 'button'"
              :type="propValue.buttonType || 'primary'"
              :size="propValue.buttonSize || 'small'"
              @click.stop="handleActionClick(item, index)"
            >
              {{ getFieldValue(item, 'actionText') || propValue.actionText || '操作' }}
            </el-button>

            <el-icon
              v-else-if="propValue.actionType === 'icon'"
              class="action-icon"
              :size="propValue.actionIconSize || 16"
              @click.stop="handleActionClick(item, index)"
            >
              <component :is="propValue.actionIcon || 'ArrowRight'" />
            </el-icon>

            <div v-else class="custom-action" @click.stop="handleActionClick(item, index)">
              {{ getFieldValue(item, 'actionText') || propValue.actionText || '>' }}
            </div>
          </div>
        </div>
      </template>

      <!-- 时间轴样式 -->
      <template v-else-if="propValue.listStyle === 'timeline'">
        <div class="timeline-container">
          <div
            v-for="(item, index) in listItems"
            :key="index"
            class="list-item timeline-item"
            :style="getItemStyle(index)"
            @click="handleItemClick(item, index)"
          >
            <!-- 时间轴节点 -->
            <div class="timeline-node">
              <div class="timeline-dot"></div>
              <div v-if="index < listItems.length - 1" class="timeline-line"></div>
            </div>

            <!-- 时间标签 -->
            <div class="timeline-time">
              {{ getFieldValue(item, 'time') }}
            </div>

            <!-- 内容区域 -->
            <div class="timeline-content">
              <div class="item-title" :style="titleStyle">
                {{ getFieldValue(item, 'title', index) }}
              </div>
              <div v-if="propValue.showDescription" class="item-description" :style="descriptionStyle">
                {{ getFieldValue(item, 'description') }}
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 任务列表样式 -->
      <template v-else-if="propValue.listStyle === 'task'">
        <div
          v-for="(item, index) in listItems"
          :key="index"
          class="list-item task-item"
          :class="[`task-${getFieldValue(item, 'status') || 'pending'}`]"
          :style="getItemStyle(index)"
          @click="handleItemClick(item, index)"
        >
          <!-- 状态指示器 -->
          <div class="task-status">
            <div class="status-dot" :class="[`status-${getFieldValue(item, 'status') || 'pending'}`]"></div>
          </div>

          <!-- 任务内容 -->
          <div class="item-content" :style="contentItemStyle">
            <div class="item-title" :style="titleStyle">
              {{ getFieldValue(item, 'title', index) }}
            </div>
            <div v-if="propValue.showDescription" class="item-description" :style="descriptionStyle">
              {{ getFieldValue(item, 'description') }}
            </div>
            <div class="task-meta">
              <span v-if="propValue.showTime" class="item-time" :style="timeStyle">
                {{ getFieldValue(item, 'time') }}
              </span>
              <span v-if="getFieldValue(item, 'priority')" class="task-priority" :class="[`priority-${getFieldValue(item, 'priority')}`]">
                {{ getPriorityText(getFieldValue(item, 'priority')) }}
              </span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div v-if="propValue.showAction" class="item-action task-action">
            <el-button
              type="primary"
              size="small"
              @click.stop="handleActionClick(item, index)"
            >
              {{ getFieldValue(item, 'actionText') || '处理' }}
            </el-button>
          </div>
        </div>
      </template>

      <!-- 预警列表样式 -->
      <template v-else-if="propValue.listStyle === 'warning'">
        <div
          v-for="(item, index) in listItems"
          :key="index"
          class="list-item warning-item"
          :class="[`warning-${getFieldValue(item, 'priority') || 'medium'}`]"
          :style="getItemStyle(index)"
          @click="handleItemClick(item, index)"
        >
          <!-- 预警图片 -->
          <div class="warning-icon">
            <img
              v-if="getFieldValue(item, 'icon')"
              :src="getFieldValue(item, 'icon')"
              :style="warningImageStyle"
              @error="handleImageError"
              alt="预警图标"
            />
            <img
              v-else
              :src="getDefaultWarningImage(getFieldValue(item, 'priority'))"
              :style="warningImageStyle"
              @error="handleImageError"
              alt="默认预警图标"
            />
          </div>

          <!-- 预警内容 -->
          <div class="item-content" :style="contentItemStyle">
            <div class="warning-header">
              <div class="item-title" :style="titleStyle">
                {{ getFieldValue(item, 'title', index) }}
              </div>
              <div class="warning-time" :style="timeStyle">
                {{ getFieldValue(item, 'time') }}
              </div>
            </div>
            <div v-if="propValue.showDescription" class="item-description" :style="descriptionStyle">
              {{ getFieldValue(item, 'description') }}
            </div>
          </div>

          <!-- 操作区域 -->
          <div v-if="propValue.showAction" class="item-action warning-action">
            <el-button
              type="text"
              size="small"
              @click.stop="handleActionClick(item, index)"
            >
              {{ getFieldValue(item, 'actionText') || '查看' }}
            </el-button>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs, onMounted, watch } from 'vue'

const props = withDefaults(
  defineProps<{
    element?: any
    propValue?: any
    showPosition?: string
    isEdit?: boolean
  }>(),
  {
    element: () => ({}),
    propValue: () => ({
      title: '通知公告',
      showHeader: true,
      showIcon: true,
      showDescription: true,
      showTime: true,
      showAction: true,
      actionType: 'icon', // button, icon, text
      actionText: '查看',
      actionIcon: 'ArrowRight',
      buttonType: 'primary',
      buttonSize: 'small',
      iconSize: 16,
      actionIconSize: 16,
      emptyText: '暂无数据',

      // 新增：列表样式类型配置
      listStyle: 'notice', // notice(公告), timeline(时间轴), task(任务), warning(预警)

      // 新增：数据源配置
      dataSource: 'static', // static(静态数据), rest(REST接口)
      restConfig: null, // REST接口配置
      restFields: [], // REST字段映射配置

      // 新增：字段映射配置
      fieldMapping: {
        title: 'title',
        description: 'description',
        time: 'time',
        icon: 'icon',
        status: 'status',
        priority: 'priority',
        actionText: 'actionText'
      },

      // 新增：边框配置
      borderConfig: {
        showContainerBorder: true,
        containerBorderColor: '#e8e8e8',
        containerBorderWidth: '1px',
        containerBorderStyle: 'solid',
        showItemBorder: true,
        itemBorderColor: '#f0f0f0',
        itemBorderWidth: '1px',
        itemBorderStyle: 'solid',
        showHeaderBorder: true,
        headerBorderColor: '#e8e8e8',
        headerBorderWidth: '1px',
        headerBorderStyle: 'solid'
      },

      items: [
        {
          title: '维护通知：2024-12-07 系统凌晨维护',
          description: '机房维护，请各单位做好工作安排，感谢支持！',
          time: '2024-12-07',
          icon: 'Bell',
          actionText: '查看'
        },
        {
          title: '维护通知：2024-12-05 系统凌晨维护',
          description: '机房维护，请各单位做好工作安排，感谢支持！',
          time: '2024-12-05',
          icon: 'Bell',
          actionText: '查看'
        }
      ],
      containerStyle: {
        backgroundColor: '#fff',
        border: '1px solid #e8e8e8',
        borderRadius: '6px',
        padding: '0'
      },
      headerStyle: {
        padding: '12px 16px',
        borderBottom: '1px solid #e8e8e8',
        backgroundColor: '#f5f5f5',
        fontSize: '16px',
        fontWeight: 'bold',
        color: '#333'
      },
      itemStyle: {
        padding: '12px 16px',
        borderBottom: '1px solid #f0f0f0',
        cursor: 'pointer',
        transition: 'background-color 0.2s'
      },
      itemHoverStyle: {
        backgroundColor: '#f5f5f5'
      },
      titleStyle: {
        fontSize: '14px',
        fontWeight: '500',
        color: '#333',
        marginBottom: '4px'
      },
      descriptionStyle: {
        fontSize: '12px',
        color: '#666',
        marginBottom: '4px'
      },
      timeStyle: {
        fontSize: '12px',
        color: '#999'
      },
      iconStyle: {
        marginRight: '12px',
        color: '#409eff'
      },
      actionStyle: {
        color: '#409eff',
        cursor: 'pointer'
      }
    }),
    showPosition: 'canvas',
    isEdit: false
  }
)

const { element, propValue, showPosition, isEdit } = toRefs(props)

// 列表数据
const listItems = computed(() => {
  if (propValue.value.dataSource === 'rest' && propValue.value.restData) {
    return propValue.value.restData
  }
  return propValue.value.items || []
})

// 获取字段值的方法
const getFieldValue = (item: any, fieldType: string, index?: number) => {
  const mapping = propValue.value.fieldMapping || {}
  const fieldName = mapping[fieldType] || fieldType

  // 如果是静态数据，直接返回字段值
  if (propValue.value.dataSource === 'static') {
    return item[fieldName] || item[fieldType]
  }

  // 如果是REST数据，根据字段映射获取值
  const value = item[fieldName] || item[fieldType]

  // 对于标题字段，如果没有值则提供默认值
  if (fieldType === 'title' && !value) {
    return `项目 ${(index || 0) + 1}`
  }

  return value
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const priorityMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return priorityMap[priority] || priority
}

// 获取默认预警图片
const getDefaultWarningImage = (priority: string) => {
  const imageMap = {
    high: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMSA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDMgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjRjU2QzZDIi8+Cjwvc3ZnPgo=',
    medium: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIGZpbGw9IiNFNkEyM0MiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyUzYuNDggMjIgMTIgMjJTMjIgMTcuNTIgMjIgMTJTMTcuNTIgMiAxMiAyWk0xMyAxN0gxMVYxNUgxM1YxN1pNMTMgMTNIMTFWN0gxM1YxM1oiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=',
    low: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIGZpbGw9IiM0MDlFRkYiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTkgMTZIMTVWMTRIOVYxNlpNMTIgMkM2LjQ4IDIgMiA2LjQ4IDIgMTJTNi40OCAyMiAxMiAyMlMyMiAxNy41MiAyMiAxMlMxNy41MiAyIDEyIDJaTTEyIDIwQzcuNTkgMjAgNCAyMC40MSA0IDEyUzcuNTkgNCAxMiA0UzIwIDcuNTkgMjAgMTJTMTYuNDEgMjAgMTIgMjBaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K'
  }
  return imageMap[priority] || imageMap.medium
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 设置默认图片
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMiA2QzEzLjEgNiAxNCA2LjkgMTQgOEM0IDkuMSAxMy4xIDEwIDEyIDEwUzEwIDkuMSAxMCA4UzEwLjkgNiAxMiA2Wk0xMiAxNkMxMy4xIDE2IDE0IDE1LjEgMTQgMTRTMTMuMSAxMiAxMiAxMlMxMCAxMi45IDEwIDE0UzEwLjkgMTYgMTIgMTZaIiBmaWxsPSIjQ0NDIi8+Cjwvc3ZnPgo='
}

// 容器样式
const containerStyle = computed(() => {
  const baseStyle = { ...propValue.value.containerStyle }
  const borderConfig = propValue.value.borderConfig || {}

  // 应用容器边框配置
  if (borderConfig.showContainerBorder) {
    baseStyle.border = `${borderConfig.containerBorderWidth || '1px'} ${borderConfig.containerBorderStyle || 'solid'} ${borderConfig.containerBorderColor || '#e8e8e8'}`
  } else {
    baseStyle.border = 'none'
  }

  return baseStyle
})

// 头部样式
const headerStyle = computed(() => {
  const baseStyle = { ...propValue.value.headerStyle }
  const borderConfig = propValue.value.borderConfig || {}

  // 应用头部边框配置
  if (borderConfig.showHeaderBorder) {
    baseStyle.borderBottom = `${borderConfig.headerBorderWidth || '1px'} ${borderConfig.headerBorderStyle || 'solid'} ${borderConfig.headerBorderColor || '#e8e8e8'}`
  } else {
    baseStyle.borderBottom = 'none'
  }

  return baseStyle
})

// 内容区域样式
const contentStyle = computed(() => {
  return {
    maxHeight: '400px',
    overflowY: 'auto'
  }
})

// 空状态样式
const emptyStyle = computed(() => {
  return {
    padding: '40px 16px',
    textAlign: 'center',
    color: '#999',
    fontSize: '14px'
  }
})

// 图标样式
const iconStyle = computed(() => {
  return propValue.value.iconStyle
})

// 默认图标样式
const defaultIconStyle = computed(() => {
  return {
    width: (propValue.value.iconSize || 16) + 'px',
    height: (propValue.value.iconSize || 16) + 'px',
    backgroundColor: '#e8e8e8',
    borderRadius: '50%'
  }
})

// 图片图标样式
const imageIconStyle = computed(() => {
  return {
    width: (propValue.value.iconSize || 16) + 'px',
    height: (propValue.value.iconSize || 16) + 'px',
    objectFit: 'cover',
    borderRadius: '4px'
  }
})

// 预警图片样式
const warningImageStyle = computed(() => {
  return {
    width: (propValue.value.iconSize || 20) + 'px',
    height: (propValue.value.iconSize || 20) + 'px',
    objectFit: 'cover',
    borderRadius: '4px'
  }
})

// 内容项样式
const contentItemStyle = computed(() => {
  return {
    flex: 1,
    minWidth: 0
  }
})

// 标题样式
const titleStyle = computed(() => {
  return propValue.value.titleStyle
})

// 描述样式
const descriptionStyle = computed(() => {
  return propValue.value.descriptionStyle
})

// 时间样式
const timeStyle = computed(() => {
  return propValue.value.timeStyle
})

// 操作区样式
const actionStyle = computed(() => {
  return propValue.value.actionStyle
})

// 获取列表项样式
const getItemStyle = (index: number) => {
  const baseStyle = { ...propValue.value.itemStyle }
  const borderConfig = propValue.value.borderConfig || {}

  // 应用列表项边框配置
  if (borderConfig.showItemBorder) {
    baseStyle.borderBottom = `${borderConfig.itemBorderWidth || '1px'} ${borderConfig.itemBorderStyle || 'solid'} ${borderConfig.itemBorderColor || '#f0f0f0'}`

    // 最后一项可选择是否显示底部边框
    if (index === listItems.value.length - 1) {
      baseStyle.borderBottom = 'none'
    }
  } else {
    baseStyle.borderBottom = 'none'
  }

  return {
    ...baseStyle,
    display: 'flex',
    alignItems: 'center'
  }
}

// 处理列表项点击
const handleItemClick = (item: any, index: number) => {
  if (!isEdit.value) {
    console.log('Item clicked:', item, index)
    // 这里可以添加自定义点击事件
  }
}

// 处理操作按钮点击
const handleActionClick = (item: any, index: number) => {
  if (!isEdit.value) {
    console.log('Action clicked:', item, index)
    // 这里可以添加自定义操作事件
  }
}

// REST数据获取
const fetchRestData = async () => {
  if (propValue.value.dataSource !== 'rest' || !propValue.value.restConfig) {
    return
  }

  try {
    const { callRestApi, preprocessRestData } = await import('@/utils/restApi')

    console.log('列表组件开始获取REST数据:', {
      url: propValue.value.restConfig.url,
      method: propValue.value.restConfig.method
    })

    // 调用REST API
    const restData = await callRestApi(propValue.value.restConfig, {
      enableMockData: false,
      logPrefix: '列表组件'
    })

    // 预处理数据
    const processedData = preprocessRestData(restData, propValue.value.restConfig)

    console.log('列表组件REST数据获取成功:', {
      originalDataType: typeof restData,
      processedDataLength: processedData.length,
      sampleData: processedData.slice(0, 2)
    })

    // 将数据存储到组件配置中
    propValue.value.restData = processedData

  } catch (error) {
    console.error('列表组件REST数据获取失败:', error)
    propValue.value.restData = []
  }
}

// 监听REST配置变化
watch(() => propValue.value.restConfig, () => {
  if (propValue.value.dataSource === 'rest') {
    fetchRestData()
  }
}, { deep: true })

// 组件挂载时获取数据
onMounted(() => {
  if (propValue.value.dataSource === 'rest' && propValue.value.restConfig) {
    fetchRestData()
  }
})
</script>

<style lang="less" scoped>
.list-container {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .list-header {
    .list-title {
      margin: 0;
    }
  }

  .list-content {
    .list-item {
      &:hover {
        background-color: v-bind('propValue.itemHoverStyle.backgroundColor') !important;
      }

      .item-icon {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        .default-icon {
          flex-shrink: 0;
        }
      }

      .item-content {
        .item-title {
          line-height: 1.4;
          word-break: break-all;
        }

        .item-description {
          line-height: 1.4;
          word-break: break-all;
        }

        .item-time {
          line-height: 1.4;
        }
      }

      .item-action {
        flex-shrink: 0;
        margin-left: 12px;

        .action-icon {
          cursor: pointer;

          &:hover {
            opacity: 0.7;
          }
        }

        .custom-action {
          font-size: 14px;

          &:hover {
            opacity: 0.7;
          }
        }
      }
    }
  }

  // 通知公告样式（默认样式）
  &.list-style-notice {
    .notice-item {
      display: flex;
      align-items: center;
    }
  }

  // 时间轴样式
  &.list-style-timeline {
    .timeline-container {
      position: relative;
      padding-left: 20px;
    }

    .timeline-item {
      position: relative;
      display: flex;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;

        .timeline-line {
          display: none;
        }
      }
    }

    .timeline-node {
      position: absolute;
      left: -20px;
      top: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      z-index: 2;
    }

    .timeline-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #409eff;
      border: 2px solid #fff;
      box-shadow: 0 0 0 2px #409eff;
    }

    .timeline-line {
      width: 2px;
      height: 40px;
      background-color: #e4e7ed;
      margin-top: 4px;
    }

    .timeline-time {
      min-width: 80px;
      font-size: 12px;
      color: #999;
      text-align: center;
      margin-right: 16px;
      margin-top: 2px;
    }

    .timeline-content {
      flex: 1;
      padding: 8px 12px;
      background-color: #f8f9fa;
      border-radius: 6px;
      border-left: 3px solid #409eff;
    }
  }

  // 任务列表样式
  &.list-style-task {
    .task-item {
      display: flex;
      align-items: flex-start;
      padding: 16px;
      border-left: 4px solid transparent;

      &.task-pending {
        border-left-color: #e6a23c;
      }

      &.task-processing {
        border-left-color: #409eff;
      }

      &.task-completed {
        border-left-color: #67c23a;
      }

      &.task-cancelled {
        border-left-color: #f56c6c;
      }
    }

    .task-status {
      margin-right: 12px;
      margin-top: 4px;
    }

    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;

      &.status-pending {
        background-color: #e6a23c;
      }

      &.status-processing {
        background-color: #409eff;
      }

      &.status-completed {
        background-color: #67c23a;
      }

      &.status-cancelled {
        background-color: #f56c6c;
      }
    }

    .task-meta {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-top: 8px;
    }

    .task-priority {
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;

      &.priority-high {
        background-color: #fef0f0;
        color: #f56c6c;
      }

      &.priority-medium {
        background-color: #fdf6ec;
        color: #e6a23c;
      }

      &.priority-low {
        background-color: #f0f9ff;
        color: #409eff;
      }
    }

    .task-action {
      margin-left: auto;
    }
  }

  // 预警列表样式
  &.list-style-warning {
    .warning-item {
      display: flex;
      align-items: flex-start;
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 8px;

      &.warning-high {
        background-color: #fef0f0;
        border: 1px solid #fbc4c4;

        .warning-icon {
          color: #f56c6c;
        }
      }

      &.warning-medium {
        background-color: #fdf6ec;
        border: 1px solid #f5dab1;

        .warning-icon {
          color: #e6a23c;
        }
      }

      &.warning-low {
        background-color: #f0f9ff;
        border: 1px solid #b3d8ff;

        .warning-icon {
          color: #409eff;
        }
      }
    }

    .warning-icon {
      margin-right: 12px;
      margin-top: 2px;
    }

    .warning-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 4px;
    }

    .warning-time {
      font-size: 12px;
      color: #999;
      white-space: nowrap;
      margin-left: 12px;
    }

    .warning-action {
      margin-left: auto;
      margin-top: 2px;
    }
  }
}
</style>
