<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus-secondary'
import { useI18n } from '@/hooks/web/useI18n'
import { Plus, Delete, Edit, View } from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'

const { t } = useI18n()

interface RestField {
  id: string
  name: string
  originalName: string
  type: 'string' | 'number' | 'date' | 'boolean'
  groupType: 'd' | 'q'
  path: string
  deType: number
  enabled: boolean
  summary?: string
  sort?: 'asc' | 'desc' | 'none'
  customName?: string
}

const props = defineProps<{
  fields: RestField[]
  themes?: string
  readonly?: boolean
}>()

const emits = defineEmits<{
  'update:fields': [fields: RestField[]]
  onFieldsChange: [fields: RestField[]]
}>()

const localFields = ref<RestField[]>([])
const editingField = ref<RestField | null>(null)
const showEditDialog = ref(false)
const showAddDialog = ref(false)

// 字段类型选项
const typeOptions = [
  { label: '文本', value: 'string' },
  { label: '数值', value: 'number' },
  { label: '日期', value: 'date' },
  { label: '布尔', value: 'boolean' }
]

// 分组类型选项
const groupTypeOptions = [
  { label: '维度', value: 'd' },
  { label: '指标', value: 'q' }
]

// 聚合方式选项（仅指标）
const summaryOptions = [
  { label: '求和', value: 'sum' },
  { label: '平均值', value: 'avg' },
  { label: '最大值', value: 'max' },
  { label: '最小值', value: 'min' },
  { label: '计数', value: 'count' },
  { label: '去重计数', value: 'count_distinct' }
]

// 排序选项
const sortOptions = [
  { label: '无排序', value: 'none' },
  { label: '升序', value: 'asc' },
  { label: '降序', value: 'desc' }
]

// 监听props变化
watch(
  () => props.fields,
  newFields => {
    // 避免循环更新，只在字段真正变化时更新
    if (JSON.stringify(localFields.value) !== JSON.stringify(newFields)) {
      localFields.value = [...newFields]
    }
  },
  { immediate: true, deep: true }
)

// 监听本地字段变化 - 使用防抖避免频繁更新
const debouncedEmit = debounce(newFields => {
  emits('update:fields', newFields)
  emits('onFieldsChange', newFields)
}, 300)

watch(
  localFields,
  newFields => {
    debouncedEmit(newFields)
  },
  { deep: true }
)

// 添加字段
const addField = () => {
  editingField.value = {
    id: generateId(),
    name: '',
    originalName: '',
    type: 'string',
    groupType: 'd',
    path: '',
    deType: 0,
    enabled: true,
    sort: 'none'
  }
  showAddDialog.value = true
}

// 编辑字段
const editField = (field: RestField) => {
  editingField.value = { ...field }
  showEditDialog.value = true
}

// 删除字段
const deleteField = (index: number) => {
  localFields.value.splice(index, 1)
}

// 保存字段
const saveField = () => {
  if (!editingField.value) return

  if (!editingField.value.name.trim()) {
    ElMessage.warning('请输入字段名称')
    return
  }

  if (!editingField.value.path.trim()) {
    ElMessage.warning('请输入JSON路径')
    return
  }

  // 更新deType
  editingField.value.deType = getDeTypeFromRestType(editingField.value.type)

  if (showAddDialog.value) {
    // 添加新字段
    localFields.value.push({ ...editingField.value })
    showAddDialog.value = false
  } else {
    // 更新现有字段
    const index = localFields.value.findIndex(f => f.id === editingField.value!.id)
    if (index !== -1) {
      localFields.value[index] = { ...editingField.value }
    }
    showEditDialog.value = false
  }

  editingField.value = null
}

// 取消编辑
const cancelEdit = () => {
  showEditDialog.value = false
  showAddDialog.value = false
  editingField.value = null
}

// 切换字段启用状态
const toggleFieldEnabled = (field: RestField) => {
  field.enabled = !field.enabled
}

// 生成唯一ID
const generateId = () => {
  return 'field_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
}

// 将REST字段类型转换为DataEase字段类型
const getDeTypeFromRestType = (restType: string): number => {
  switch (restType) {
    case 'string':
      return 0 // 文本
    case 'date':
      return 1 // 时间
    case 'number':
      return 2 // 整型数值
    case 'boolean':
      return 4 // 布尔
    default:
      return 0
  }
}

// 获取字段类型显示文本
const getTypeLabel = (type: string) => {
  const option = typeOptions.find(opt => opt.value === type)
  return option?.label || type
}

// 获取分组类型显示文本
const getGroupTypeLabel = (groupType: string) => {
  const option = groupTypeOptions.find(opt => opt.value === groupType)
  return option?.label || groupType
}

// 验证JSON路径格式
const validateJsonPath = (path: string) => {
  if (!path) return false
  // 简单验证：允许点号分隔的路径，如 data.items.name
  return /^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)*$/.test(path)
}

// 监听字段名称变化，自动设置JSON路径默认值
watch(
  () => editingField.value?.name,
  (newName, oldName) => {
    if (editingField.value && newName && newName !== oldName) {
      // 只有在路径为空或者路径等于旧字段名时才自动更新
      if (!editingField.value.path || editingField.value.path === oldName) {
        editingField.value.path = newName
      }
    }
  }
)

defineExpose({
  addField,
  getFields: () => localFields.value
})
</script>

<template>
  <div class="rest-field-editor" :class="{ dark: themes === 'dark' }">
    <div class="field-header">
      <h4>字段配置</h4>
      <el-button v-if="!readonly" type="primary" size="small" :icon="Plus" @click="addField">
        添加字段
      </el-button>
    </div>

    <!-- 字段列表 -->
    <el-table :data="localFields" size="small" border style="width: 100%">
      <el-table-column type="index" label="#" width="30" />

      <el-table-column prop="enabled" label="启用" width="60">
        <template #default="{ row }">
          <el-switch v-model="row.enabled" :disabled="readonly" @change="toggleFieldEnabled(row)" />
        </template>
      </el-table-column>

      <el-table-column prop="name" label="字段名称" min-width="120">
        <template #default="{ row }">
          <span :class="{ 'field-disabled': !row.enabled }">
            {{ row.customName || row.name }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="type" label="类型" width="80">
        <template #default="{ row }">
          {{ getTypeLabel(row.type) }}
        </template>
      </el-table-column>

      <el-table-column prop="groupType" label="分组" width="80">
        <template #default="{ row }">
          <el-tag :type="row.groupType === 'd' ? 'info' : 'success'">
            {{ getGroupTypeLabel(row.groupType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="path" label="JSON路径" min-width="120">
        <template #default="{ row }">
          <code class="json-path">{{ row.path }}</code>
        </template>
      </el-table-column>

      <el-table-column prop="summary" label="聚合方式" width="100">
        <template #default="{ row }">
          <span v-if="row.groupType === 'q'">
            {{ row.summary || 'sum' }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="120" v-if="!readonly">
        <template #default="{ row, $index }">
          <el-button
            style="min-width: 30px"
            type="primary"
            size="small"
            :icon="Edit"
            @click="editField(row)"
          />
          <el-button
            style="min-width: 30px"
            type="danger"
            size="small"
            :icon="Delete"
            @click="deleteField($index)"
          />
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑字段"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form v-if="editingField" :model="editingField" label-width="100px" size="small">
        <el-form-item label="字段名称" required>
          <el-input v-model="editingField.name" placeholder="请输入字段名称" />
        </el-form-item>

        <el-form-item label="自定义名称">
          <el-input v-model="editingField.customName" placeholder="可选，用于显示的自定义名称" />
        </el-form-item>

        <el-form-item label="JSON路径" required>
          <el-input v-model="editingField.path" placeholder="如: data.items.name" />
          <div class="form-tip">支持点号分隔的路径，如：data.items.name</div>
        </el-form-item>

        <el-form-item label="字段类型">
          <el-select v-model="editingField.type" style="width: 100%">
            <el-option
              v-for="option in typeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="分组类型">
          <el-select v-model="editingField.groupType" style="width: 100%">
            <el-option
              v-for="option in groupTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="editingField.groupType === 'q'" label="聚合方式">
          <el-select v-model="editingField.summary" style="width: 100%">
            <el-option
              v-for="option in summaryOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="排序方式">
          <el-select v-model="editingField.sort" style="width: 100%">
            <el-option
              v-for="option in sortOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="启用状态">
          <el-switch v-model="editingField.enabled" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="cancelEdit">取消</el-button>
        <el-button type="primary" @click="saveField">保存</el-button>
      </template>
    </el-dialog>

    <!-- 添加对话框 -->
    <el-dialog v-model="showAddDialog" title="添加字段" width="600px" :close-on-click-modal="false">
      <el-form v-if="editingField" :model="editingField" label-width="100px" size="small">
        <el-form-item label="字段名称" required>
          <el-input v-model="editingField.name" placeholder="请输入字段名称" />
        </el-form-item>

        <el-form-item label="自定义名称">
          <el-input v-model="editingField.customName" placeholder="可选，用于显示的自定义名称" />
        </el-form-item>

        <el-form-item label="JSON路径" required>
          <el-input v-model="editingField.path" placeholder="如: data.items.name" />
          <div class="form-tip">支持点号分隔的路径，如：data.items.name</div>
        </el-form-item>

        <el-form-item label="字段类型">
          <el-select v-model="editingField.type" style="width: 100%">
            <el-option
              v-for="option in typeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="分组类型">
          <el-select v-model="editingField.groupType" style="width: 100%">
            <el-option
              v-for="option in groupTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="editingField.groupType === 'q'" label="聚合方式">
          <el-select v-model="editingField.summary" style="width: 100%">
            <el-option
              v-for="option in summaryOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="排序方式">
          <el-select v-model="editingField.sort" style="width: 100%">
            <el-option
              v-for="option in sortOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="启用状态">
          <el-switch v-model="editingField.enabled" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="cancelEdit">取消</el-button>
        <el-button type="primary" @click="saveField">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="less">
.rest-field-editor {
  &.dark {
    background-color: #1a1a1a;
    color: #ebebeb;
  }

  .field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      margin: 0;
    }
  }

  .field-disabled {
    opacity: 0.5;
  }

  .json-path {
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
  }

  .form-tip {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }
}

.dark {
  .json-path {
    background-color: #2a2a2a;
    color: #ebebeb;
  }

  .form-tip {
    color: #ccc;
  }
}
</style>
