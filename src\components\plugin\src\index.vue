<script lang="ts" setup>
import noLic from './nolic.vue'
import { ref, useAttrs, onMounted } from 'vue'
import { execute, randomKey, formatArray } from './convert'
import { loadDistributed } from '@/api/plugin'
import configGlobal from '@/components/config-global/src/ConfigGlobal.vue'
import { useCache } from '@/hooks/web/useCache'
import { i18n } from '@/plugins/vue-i18n'
import * as Vue from 'vue'
import axios from 'axios'
import * as Pinia from 'pinia'
import * as echarts from 'echarts'
import router from '@/router'
import tinymce from 'tinymce/tinymce'
import { useEmitt } from '@/hooks/web/useEmitt'
import { isNull } from '@/utils/utils'

const { wsCache } = useCache()

const plugin = ref()

const loading = ref(false)

const attrs = useAttrs()

const showNolic = () => {
  plugin.value = noLic
  loading.value = false
}
const generateRamStr = (len: number) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let randomStr = ''
  for (var i = 0; i < len; i++) {
    randomStr += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return randomStr
}

const importProxy = (bytesArray: any[]) => {
  const promise = import(
    `../../../../../../${formatArray(bytesArray[6])}/${formatArray(bytesArray[7])}/${formatArray(
      bytesArray[8]
    )}/${formatArray(bytesArray[9])}/${formatArray(bytesArray[10])}.vue`
  )
  promise
    .then((res: any) => {
      plugin.value = res.default
    })
    .catch(e => {
      console.error(e)
      showNolic()
    })
}

const loadXpack = async () => {
  if (window['DEXPack']) {
    const xpack = await window['DEXPack'].mapping[attrs.jsname]
    plugin.value = xpack.default
  }
}

useEmitt({
  name: 'load-xpack',
  callback: loadXpack
})

const loadComponent = () => {
  loading.value = true
  // 直接显示无许可证组件，不再调用远程接口
  setTimeout(() => {
    emits('loadFail')
    showNolic()
    loading.value = false
  }, 100)
}
const storeCacheProxy = byteArray => {
  const result = []
  byteArray.forEach(item => {
    result.push([...item])
  })
  wsCache.set(`de-plugin-proxy`, JSON.stringify(result))
}
const pluginProxy = ref(null)
const invokeMethod = param => {
  if (pluginProxy.value && pluginProxy.value['invokeMethod']) {
    pluginProxy.value['invokeMethod'](param)
  } else if (param.methodName && pluginProxy.value[param.methodName]) {
    pluginProxy.value[param.methodName](param.args)
  }
}
const emits = defineEmits(['loadFail'])
defineExpose({
  invokeMethod
})
onMounted(async () => {
  // 直接使用本地组件加载方式，不再调用 xpackModelApi
  loadComponent()
})
</script>

<template>
  <configGlobal>
    <component
      :key="attrs.jsname"
      ref="pluginProxy"
      :is="plugin"
      v-loading="loading"
      v-bind="attrs"
    ></component>
  </configGlobal>
</template>
