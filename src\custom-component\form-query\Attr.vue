<template>
  <div class="form-query-attr">
    <el-collapse v-model="activeNames" accordion>
      <!-- 基础配置 -->
      <el-collapse-item title="基础配置" name="basic">
        <el-form label-width="80px" size="small">
          <el-form-item label="显示标题">
            <el-switch v-model="config.showTitle" @change="handleConfigChange" />
          </el-form-item>
          
          <el-form-item v-if="config.showTitle" label="标题文本">
            <el-input v-model="config.title" @change="handleConfigChange" />
          </el-form-item>
          
          <el-form-item label="目标组件">
            <el-select
              v-model="config.targetComponents"
              multiple
              placeholder="选择要联动的表格组件"
              style="width: 100%"
              @change="handleConfigChange"
            >
              <el-option
                v-for="component in availableComponents"
                :key="component.id"
                :label="component.label"
                :value="component.id"
              />
            </el-select>
            <div class="form-tip">选择需要联动查询的表格组件</div>
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 字段配置 -->
      <el-collapse-item title="字段配置" name="fields">
        <div class="fields-config">
          <div class="fields-header">
            <el-button type="primary" size="small" @click="addField">
              添加字段
            </el-button>
          </div>
          
          <div class="fields-list">
            <div
              v-for="(field, index) in config.fields"
              :key="field.id"
              class="field-item"
            >
              <div class="field-header">
                <span class="field-label">{{ field.label || '未命名字段' }}</span>
                <div class="field-actions">
                  <el-button size="small" @click="editField(index)">编辑</el-button>
                  <el-button size="small" type="danger" @click="removeField(index)">删除</el-button>
                </div>
              </div>
              <div class="field-info">
                <span>类型: {{ getFieldTypeLabel(field.type) }}</span>
                <span>参数: {{ field.paramMapping?.paramName || field.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-item>

      <!-- 布局配置 -->
      <el-collapse-item title="布局配置" name="layout">
        <el-form label-width="80px" size="small">
          <el-form-item label="表单列数">
            <el-input-number
              v-model="config.layout.columns"
              :min="1"
              :max="4"
              @change="handleConfigChange"
            />
          </el-form-item>
          
          <el-form-item label="标签位置">
            <el-select v-model="config.layout.labelPosition" @change="handleConfigChange">
              <el-option label="顶部" value="top" />
              <el-option label="左侧" value="left" />
              <el-option label="右侧" value="right" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="标签宽度">
            <el-input v-model="config.layout.labelWidth" @change="handleConfigChange" />
          </el-form-item>
          
          <el-form-item label="组件尺寸">
            <el-select v-model="config.layout.size" @change="handleConfigChange">
              <el-option label="大" value="large" />
              <el-option label="默认" value="default" />
              <el-option label="小" value="small" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="显示边框">
            <el-switch v-model="config.layout.showBorder" @change="handleConfigChange" />
          </el-form-item>
          
          <el-form-item label="字段间距">
            <el-input-number
              v-model="config.layout.spacing"
              :min="0"
              :max="50"
              @change="handleConfigChange"
            />
          </el-form-item>
          
          <el-form-item label="按钮对齐">
            <el-select v-model="config.layout.actionsAlign" @change="handleConfigChange">
              <el-option label="左对齐" value="left" />
              <el-option label="居中" value="center" />
              <el-option label="右对齐" value="right" />
            </el-select>
          </el-form-item>

          <el-form-item label="按钮位置">
            <el-select v-model="config.layout.actionsPosition" @change="handleConfigChange">
              <el-option label="字段末位" value="inline" />
              <el-option label="新行末位" value="newline" />
            </el-select>
            <div class="form-tip">字段末位：按钮显示在最后一个字段旁边；新行末位：按钮显示在新的一行</div>
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 查询配置 -->
      <el-collapse-item title="查询配置" name="query">
        <el-form label-width="80px" size="small">
          <el-form-item label="自动查询">
            <el-switch v-model="config.autoQuery" @change="handleConfigChange" />
            <div class="form-tip">字段变化时自动触发查询</div>
          </el-form-item>
          
          <el-form-item v-if="config.autoQuery" label="查询延迟">
            <el-input-number
              v-model="config.autoQueryDelay"
              :min="100"
              :max="5000"
              :step="100"
              @change="handleConfigChange"
            />
            <div class="form-tip">毫秒，防抖延迟时间</div>
          </el-form-item>
          
          <el-form-item label="初始查询">
            <el-switch v-model="config.initialQuery" @change="handleConfigChange" />
            <div class="form-tip">组件加载时自动查询</div>
          </el-form-item>
          
          <el-form-item label="重置后查询">
            <el-switch v-model="config.resetAndQuery" @change="handleConfigChange" />
            <div class="form-tip">重置表单后自动查询</div>
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 按钮配置 -->
      <el-collapse-item title="按钮配置" name="buttons">
        <el-form label-width="80px" size="small">
          <el-form-item label="查询按钮">
            <el-switch v-model="config.showQueryButton" @change="handleConfigChange" />
          </el-form-item>
          
          <el-form-item v-if="config.showQueryButton" label="查询文本">
            <el-input v-model="config.queryButtonText" @change="handleConfigChange" />
          </el-form-item>
          
          <el-form-item label="重置按钮">
            <el-switch v-model="config.showResetButton" @change="handleConfigChange" />
          </el-form-item>
          
          <el-form-item v-if="config.showResetButton" label="重置文本">
            <el-input v-model="config.resetButtonText" @change="handleConfigChange" />
          </el-form-item>
          
          <el-form-item label="清空按钮">
            <el-switch v-model="config.showClearButton" @change="handleConfigChange" />
          </el-form-item>
          
          <el-form-item v-if="config.showClearButton" label="清空文本">
            <el-input v-model="config.clearButtonText" @change="handleConfigChange" />
          </el-form-item>
        </el-form>
      </el-collapse-item>
    </el-collapse>

    <!-- 字段编辑对话框 -->
    <FieldConfig
      v-model:visible="showFieldDialog"
      :field="editingField"
      :existing-fields="config.fields"
      :is-edit="isEditMode"
      @confirm="handleFieldConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus-secondary'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import { snapshotStoreWithOut } from '@/store/modules/data-visualization/snapshot'
import { storeToRefs } from 'pinia'
import type { FormQueryConfig, FormQueryField } from './types'
import { DEFAULT_FORM_QUERY_CONFIG, FIELD_TYPE_OPTIONS, ACTIONS_POSITION_OPTIONS, generateID } from './types'
import FieldConfig from './FieldConfig.vue'

// 移除Props接口，使用curComponent代替

const dvMainStore = dvMainStoreWithOut()
const { curComponent } = storeToRefs(dvMainStore)
const snapshotStore = snapshotStoreWithOut()

// 激活的折叠面板
const activeNames = ref(['basic'])

// 配置数据 - 使用curComponent确保响应式更新
const config = computed({
  get: (): FormQueryConfig => {
    return curComponent.value?.propValue || { ...DEFAULT_FORM_QUERY_CONFIG }
  },
  set: (value: FormQueryConfig) => {
    if (curComponent.value) {
      curComponent.value.propValue = value
      snapshotStore.recordSnapshotCache('propValue')
    }
  }
})

// 字段编辑相关
const showFieldDialog = ref(false)
const editingField = ref<FormQueryField | null>(null)
const editingIndex = ref(-1)
const isEditMode = computed(() => editingIndex.value >= 0)

// 获取可用的组件列表（表格组件）
const availableComponents = computed(() => {
  const { componentData } = dvMainStore
  return componentData
    .filter((comp: any) =>
      comp.component === 'UserView' &&
      ['table-normal', 'table-info', 'table-pivot'].includes(comp.innerType)
    )
    .map((comp: any) => ({
      id: comp.id,
      label: comp.label || `表格组件 ${comp.id.slice(-6)}`
    }))
})

// 获取字段类型标签
const getFieldTypeLabel = (type: string) => {
  const option = FIELD_TYPE_OPTIONS.find(opt => opt.value === type)
  return option?.label || type
}

// 处理配置变化 - 由于使用了computed setter，会自动触发快照记录
const handleConfigChange = () => {
  // 触发响应式更新，computed setter会自动处理快照
}
 
// 添加字段
const addField = () => {
  editingField.value = null
  editingIndex.value = -1
  showFieldDialog.value = true
}

// 编辑字段
const editField = (index: number) => {
  const originalField = config.value.fields[index]
  // 深拷贝字段数据，确保所有嵌套对象都被正确复制
  editingField.value = JSON.parse(JSON.stringify(originalField))

  // 确保必要的对象存在
  if (!editingField.value.validation) {
    editingField.value.validation = {}
  }
  if (!editingField.value.paramMapping) {
    editingField.value.paramMapping = {}
  }
  if (!editingField.value.options) {
    editingField.value.options = []
  }

  // 确保paramMapping有正确的默认值
  if (editingField.value.type === 'daterange') {
    if (!editingField.value.paramMapping.startParam) {
      editingField.value.paramMapping.startParam = `${editingField.value.name}_start`
    }
    if (!editingField.value.paramMapping.endParam) {
      editingField.value.paramMapping.endParam = `${editingField.value.name}_end`
    }
  } else {
    if (!editingField.value.paramMapping.paramName) {
      editingField.value.paramMapping.paramName = editingField.value.name || ''
    }
  }

  editingIndex.value = index
  showFieldDialog.value = true
}

// 删除字段
const removeField = (index: number) => {
  config.value.fields.splice(index, 1)
  handleConfigChange()
  ElMessage.success('字段已删除')
}

// 处理字段确认
const handleFieldConfirm = (field: FormQueryField) => {
  if (isEditMode.value) {
    // 编辑模式
    config.value.fields[editingIndex.value] = field
    ElMessage.success('字段已更新')
  } else {
    // 新增模式
    if (!field.id) {
      field.id = generateID()
    }
    config.value.fields.push(field)
    ElMessage.success('字段已添加')
  }
  
  handleConfigChange()
}

// 组件挂载时确保配置完整性
onMounted(() => {
  if (!curComponent.value?.propValue) {
    curComponent.value.propValue = JSON.parse(JSON.stringify(DEFAULT_FORM_QUERY_CONFIG))
  }
})

// 监听配置变化
watch(
  () => config.value,
  () => {
    // 确保配置完整性
    if (!config.value.fields) {
      config.value.fields = []
    }
    if (!config.value.targetComponents) {
      config.value.targetComponents = []
    }
  },
  { deep: true, immediate: true }
)
</script>

<style scoped>
.form-query-attr {
  padding: 16px;
}

.fields-config {
  width: 100%;
}

.fields-header {
  margin-bottom: 16px;
}

.fields-list {
  max-height: 300px;
  overflow-y: auto;
}

.field-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
  background: #fafafa;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.field-label {
  font-weight: 500;
  color: #303133;
}

.field-actions {
  display: flex;
  gap: 8px;
}

.field-info {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

:deep(.el-collapse-item__header) {
  font-weight: 500;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-size: 12px;
}
</style>
