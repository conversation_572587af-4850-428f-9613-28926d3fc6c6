<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus-secondary'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import GlobalVariableManager from '@/components/dashboard/GlobalVariableManager.vue'
import type { GlobalVariable } from '@/types/globalVariable'
import { callRestApi, type RestConfig } from '@/utils/restApi'

const dvMainStore = dvMainStoreWithOut()

// 组件引用
const globalVariableManagerRef = ref()

// 测试状态
const testResults = ref<string[]>([])
const isLoading = ref(false)

// 计算属性
const globalVariables = computed(() => dvMainStore.globalVariables)
const globalVariableValues = computed(() => dvMainStore.globalVariableValues)

// 测试用的REST配置
const testRestConfig = ref<RestConfig>({
  url: 'https://jsonplaceholder.typicode.com/posts/${postId}',
  method: 'GET',
  headers: [
    { key: 'Authorization', value: 'Bearer ${apiToken}' },
    { key: 'Content-Type', value: 'application/json' }
  ],
  params: [
    { key: 'userId', value: '${userId}' },
    { key: 'limit', value: '10' }
  ],
  body: '',
  timeout: 5000
})

// 添加测试结果
const addTestResult = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  const timestamp = new Date().toLocaleTimeString()
  const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'
  testResults.value.push(`[${timestamp}] ${prefix} ${message}`)
}

// 清空测试结果
const clearTestResults = () => {
  testResults.value = []
}

// 测试1：创建全局变量
const testCreateVariables = () => {
  addTestResult('开始测试：创建全局变量')

  try {
    // 创建静态变量
    const staticVariable: GlobalVariable = {
      id: 'test-static-1',
      key: 'apiToken',
      type: 'static',
      value: 'test-token-123',
      description: '测试API令牌',
      enabled: true,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }

    // 创建路由参数变量
    const routeVariable: GlobalVariable = {
      id: 'test-route-1',
      key: 'userId',
      type: 'route',
      routeParam: 'id',
      description: '用户ID（从路由获取）',
      enabled: true,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }

    // 创建另一个静态变量
    const postIdVariable: GlobalVariable = {
      id: 'test-static-2',
      key: 'postId',
      type: 'static',
      value: '1',
      description: '文章ID',
      enabled: true,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }

    dvMainStore.addGlobalVariable(staticVariable)
    dvMainStore.addGlobalVariable(routeVariable)
    dvMainStore.addGlobalVariable(postIdVariable)

    addTestResult('成功创建3个全局变量', 'success')
    addTestResult(`变量列表: ${globalVariables.value.map(v => v.key).join(', ')}`)
    addTestResult('注意：全局变量现在存储在仪表板配置中，需要保存仪表板才能持久化', 'info')
  } catch (error) {
    addTestResult(`创建变量失败: ${error.message}`, 'error')
  }
}

// 测试2：变量解析
const testVariableResolution = () => {
  addTestResult('开始测试：变量解析')
  
  try {
    // 触发变量解析
    dvMainStore.resolveGlobalVariables()
    
    addTestResult('变量解析结果:')
    Object.entries(globalVariableValues.value).forEach(([key, value]) => {
      addTestResult(`  ${key}: ${value}`)
    })
    
    // 测试字符串解析
    const testString = 'API URL: https://api.example.com/users/${userId}/posts/${postId}?token=${apiToken}'
    const resolvedString = dvMainStore.resolveVariableReferences(testString)
    
    addTestResult(`原始字符串: ${testString}`)
    addTestResult(`解析后字符串: ${resolvedString}`, 'success')
  } catch (error) {
    addTestResult(`变量解析失败: ${error.message}`, 'error')
  }
}

// 测试3：REST API调用
const testRestApiCall = async () => {
  addTestResult('开始测试：REST API调用（带变量解析）')
  isLoading.value = true
  
  try {
    // 显示原始配置
    addTestResult('原始REST配置:')
    addTestResult(`  URL: ${testRestConfig.value.url}`)
    addTestResult(`  Headers: ${JSON.stringify(testRestConfig.value.headers)}`)
    addTestResult(`  Params: ${JSON.stringify(testRestConfig.value.params)}`)
    
    // 调用API（会自动解析变量）
    const result = await callRestApi(testRestConfig.value, {
      logPrefix: '全局变量测试',
      enableMockData: true
    })
    
    addTestResult('REST API调用成功', 'success')
    addTestResult(`响应数据类型: ${typeof result}`)
    if (result && typeof result === 'object') {
      addTestResult(`响应字段: ${Object.keys(result).slice(0, 5).join(', ')}`)
    }
  } catch (error) {
    addTestResult(`REST API调用失败: ${error.message}`, 'error')
  } finally {
    isLoading.value = false
  }
}

// 测试4：变量管理操作
const testVariableManagement = () => {
  addTestResult('开始测试：变量管理操作')

  try {
    // 更新变量
    const firstVariable = globalVariables.value[0]
    if (firstVariable) {
      dvMainStore.updateGlobalVariable(firstVariable.id, {
        description: '更新后的描述',
        value: firstVariable.type === 'static' ? 'updated-value' : firstVariable.value
      })
      addTestResult(`成功更新变量: ${firstVariable.key}`, 'success')
    }

    // 禁用变量
    const secondVariable = globalVariables.value[1]
    if (secondVariable) {
      dvMainStore.updateGlobalVariable(secondVariable.id, { enabled: false })
      addTestResult(`成功禁用变量: ${secondVariable.key}`, 'success')
    }

    // 重新解析变量
    dvMainStore.resolveGlobalVariables()
    addTestResult('重新解析变量完成', 'success')

  } catch (error) {
    addTestResult(`变量管理操作失败: ${error.message}`, 'error')
  }
}

// 测试5：保存和加载功能
const testSaveAndLoad = () => {
  addTestResult('开始测试：保存和加载功能')

  try {
    // 模拟保存过程中的全局变量序列化
    const serializedVariables = JSON.stringify(globalVariables.value)
    addTestResult(`序列化全局变量: ${serializedVariables.length} 字符`)

    // 模拟加载过程中的全局变量反序列化
    const deserializedVariables = JSON.parse(serializedVariables)
    addTestResult(`反序列化成功，变量数量: ${deserializedVariables.length}`)

    // 验证数据完整性
    const originalKeys = globalVariables.value.map(v => v.key).sort()
    const deserializedKeys = deserializedVariables.map(v => v.key).sort()
    const isDataIntact = JSON.stringify(originalKeys) === JSON.stringify(deserializedKeys)

    if (isDataIntact) {
      addTestResult('数据完整性验证通过', 'success')
    } else {
      addTestResult('数据完整性验证失败', 'error')
    }

    addTestResult('保存和加载功能测试完成', 'success')

  } catch (error) {
    addTestResult(`保存和加载测试失败: ${error.message}`, 'error')
  }
}

// 运行所有测试
const runAllTests = async () => {
  clearTestResults()
  addTestResult('=== 开始全局变量功能测试 ===')

  testCreateVariables()
  await new Promise(resolve => setTimeout(resolve, 500))

  testVariableResolution()
  await new Promise(resolve => setTimeout(resolve, 500))

  await testRestApiCall()
  await new Promise(resolve => setTimeout(resolve, 500))

  testVariableManagement()
  await new Promise(resolve => setTimeout(resolve, 500))

  testSaveAndLoad()

  addTestResult('=== 测试完成 ===')
  addTestResult('提示：全局变量现在与仪表板配置一起保存，请在实际使用中保存仪表板以持久化变量配置')
}

// 清理测试数据
const cleanupTestData = () => {
  const testVariableIds = ['test-static-1', 'test-route-1', 'test-static-2']
  testVariableIds.forEach(id => {
    try {
      dvMainStore.deleteGlobalVariable(id)
    } catch (error) {
      // 忽略删除错误
    }
  })
  addTestResult('清理测试数据完成', 'success')
}

// 打开全局变量管理器
const openVariableManager = () => {
  globalVariableManagerRef.value?.openManager()
}

// 组件挂载时初始化
onMounted(() => {
  addTestResult('全局变量测试页面已加载')
  addTestResult('点击"运行测试"开始测试功能')
})
</script>

<template>
  <div class="global-variable-test">
    <div class="test-header">
      <h2>全局变量功能测试</h2>
      <p>此页面用于测试全局变量的创建、解析、引用和API调用功能</p>
    </div>

    <div class="test-controls">
      <el-button type="primary" @click="runAllTests" :loading="isLoading">
        运行所有测试
      </el-button>
      <el-button @click="openVariableManager">
        打开变量管理器
      </el-button>
      <el-button @click="clearTestResults">
        清空结果
      </el-button>
      <el-button type="warning" @click="cleanupTestData">
        清理测试数据
      </el-button>
    </div>

    <div class="test-content">
      <!-- 当前变量状态 -->
      <div class="variable-status">
        <h3>当前全局变量状态</h3>
        <div v-if="globalVariables.length === 0" class="no-variables">
          暂无全局变量
        </div>
        <div v-else class="variable-list">
          <div v-for="variable in globalVariables" :key="variable.id" class="variable-item">
            <div class="variable-info">
              <span class="variable-name">{{ variable.key }}</span>
              <el-tag :type="variable.type === 'static' ? 'success' : 'info'" size="small">
                {{ variable.type === 'static' ? '静态值' : '路由参数' }}
              </el-tag>
              <el-tag :type="variable.enabled ? 'success' : 'info'" size="small">
                {{ variable.enabled ? '启用' : '禁用' }}
              </el-tag>
            </div>
            <div class="variable-value">
              配置值: {{ variable.value || variable.routeParam || '-' }}
              | 解析值: {{ globalVariableValues[variable.key] || '未解析' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 测试结果 -->
      <div class="test-results">
        <h3>测试结果</h3>
        <div class="results-container">
          <div v-if="testResults.length === 0" class="no-results">
            暂无测试结果
          </div>
          <div v-else class="results-list">
            <div
              v-for="(result, index) in testResults"
              :key="index"
              class="result-item"
            >
              {{ result }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全局变量管理器 -->
    <global-variable-manager ref="globalVariableManagerRef" />
  </div>
</template>

<style scoped lang="less">
.global-variable-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .test-header {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 8px;

    h2 {
      margin: 0 0 8px 0;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .test-controls {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .el-button + .el-button {
      margin-left: 12px;
    }
  }

  .test-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;

    .variable-status,
    .test-results {
      background-color: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 16px;

      h3 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 16px;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 8px;
      }
    }

    .variable-status {
      .no-variables {
        text-align: center;
        color: #909399;
        padding: 40px 20px;
      }

      .variable-list {
        .variable-item {
          padding: 12px;
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          margin-bottom: 8px;

          .variable-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;

            .variable-name {
              font-weight: 500;
              color: #303133;
            }
          }

          .variable-value {
            font-size: 12px;
            color: #606266;
          }
        }
      }
    }

    .test-results {
      .results-container {
        max-height: 400px;
        overflow-y: auto;

        .no-results {
          text-align: center;
          color: #909399;
          padding: 40px 20px;
        }

        .results-list {
          .result-item {
            padding: 4px 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            border-bottom: 1px solid #f0f0f0;
            white-space: pre-wrap;
            word-break: break-all;

            &:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .global-variable-test {
    .test-content {
      grid-template-columns: 1fr;
    }
  }
}
</style>
