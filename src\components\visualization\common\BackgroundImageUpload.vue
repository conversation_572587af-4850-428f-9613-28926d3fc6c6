<template>
  <div class="background-image-upload">
    <DeUploadBase64
      :img-url="imgUrl"
      :themes="themes"
      :compress="true"
      :quality="0.8"
      :max-width="1920"
      :max-height="1080"
      :background-color="'transparent'"
      :preserve-transparency="true"
      @onImgChange="handleImageChange"
    />
    
    <!-- 背景处理选项 -->
    <div v-if="showOptions" class="upload-options" :class="`options_${themes}`">
      <div class="option-group">
        <label class="option-label">背景处理:</label>
        <el-radio-group v-model="backgroundMode" @change="handleModeChange">
          <el-radio label="transparent">保持透明</el-radio>
          <el-radio label="white">白色背景</el-radio>
          <el-radio label="custom">自定义颜色</el-radio>
        </el-radio-group>
      </div>
      
      <div v-if="backgroundMode === 'custom'" class="option-group">
        <label class="option-label">背景颜色:</label>
        <el-color-picker 
          v-model="customBackgroundColor" 
          @change="handleColorChange"
          show-alpha
        />
      </div>
      
      <div class="option-group">
        <label class="option-label">压缩质量:</label>
        <el-slider 
          v-model="compressionQuality" 
          :min="0.1" 
          :max="1" 
          :step="0.1"
          @change="handleQualityChange"
        />
        <span class="quality-value">{{ Math.round(compressionQuality * 100) }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, toRefs, watch } from 'vue'
import DeUploadBase64 from './DeUploadBase64.vue'
import { ElRadioGroup, ElRadio, ElColorPicker, ElSlider } from 'element-plus-secondary'

const props = defineProps({
  imgUrl: {
    type: String
  },
  themes: {
    type: String,
    default: 'dark'
  },
  showOptions: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['onImgChange'])

const { imgUrl, themes, showOptions } = toRefs(props)

// 背景处理模式
const backgroundMode = ref('transparent')
const customBackgroundColor = ref('#FFFFFF')
const compressionQuality = ref(0.8)

// 计算实际的背景颜色
const actualBackgroundColor = computed(() => {
  switch (backgroundMode.value) {
    case 'transparent':
      return 'transparent'
    case 'white':
      return '#FFFFFF'
    case 'custom':
      return customBackgroundColor.value
    default:
      return '#FFFFFF'
  }
})

// 计算是否保持透明度
const preserveTransparency = computed(() => {
  return backgroundMode.value === 'transparent'
})

const handleImageChange = (base64Data: string) => {
  emits('onImgChange', base64Data)
}

const handleModeChange = () => {
  // 当模式改变时，如果当前有图片，重新处理
  if (imgUrl.value) {
    // 触发重新上传处理
    console.log('背景模式改变:', backgroundMode.value)
  }
}

const handleColorChange = () => {
  // 当颜色改变时，如果当前有图片，重新处理
  if (imgUrl.value && backgroundMode.value === 'custom') {
    console.log('背景颜色改变:', customBackgroundColor.value)
  }
}

const handleQualityChange = () => {
  // 当质量改变时，如果当前有图片，重新处理
  if (imgUrl.value) {
    console.log('压缩质量改变:', compressionQuality.value)
  }
}
</script>

<style scoped lang="less">
.background-image-upload {
  .upload-options {
    margin-top: 15px;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    
    &.options_dark {
      border-color: #404040;
      background-color: #2a2a2a;
      color: #ffffff;
    }
    
    .option-group {
      margin-bottom: 15px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .option-label {
        display: block;
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 500;
      }
      
      .quality-value {
        margin-left: 10px;
        font-size: 12px;
        color: #666;
      }
    }
    
    :deep(.el-radio-group) {
      .el-radio {
        margin-right: 15px;
        
        &:last-child {
          margin-right: 0;
        }
      }
    }
    
    :deep(.el-slider) {
      width: 200px;
    }
  }
  
  &.options_dark {
    .upload-options {
      .quality-value {
        color: #999;
      }
    }
  }
}
</style>
