import request from '@/config/axios'

export const loginApi = data => request.post({ url: '/login/localLogin', data })

export const queryDekey = () => request.get({ url: 'dekey' })

export const querySymmetricKey = () => request.get({ url: 'symmetricKey' })

export const modelApi = () => request.get({ url: 'model' })

export const platformLoginApi = origin => request.post({ url: '/login/platformLogin/' + origin })

export const logoutApi = () => request.get({ url: '/logout' })

export const refreshApi = (time?: any) => request.get({ url: '/login/refresh', params: { time } })

// export const uiLoadApi = () => request.get({ url: '/sysParameter/ui' })

export const uiLoadApi = () => {
  const data = {
    code: 0,
    msg: null,
    data: [
      {
        pval: true,
        pkey: 'community'
      },
      {
        pval: true,
        pkey: 'showDemoTips'
      },
      {
        pval: '用户名: admin 密码: DataEase@123456 每晚 00:00 重置数据',
        pkey: 'demoTipsContent'
      }
    ]
  }
  return Promise.resolve(data)
}

export const loginCategoryApi = () => request.get({ url: '/sysParameter/defaultLogin' })
