// 公共样式
import { deepCopy } from '@/utils/utils'
import { guid } from '@/views/visualized/data/dataset/form/util'
import { getViewConfig } from '@/views/chart/components/editor/util/chart'
import { useI18n } from '@/hooks/web/useI18n'
const { t } = useI18n()

export const commonStyle = {
  rotate: 0,
  opacity: 1,
  borderActive: false,
  borderWidth: 1,
  borderRadius: 5,
  borderStyle: 'solid',
  borderColor: '#cccccc'
}

// 轮询设置
export const BASE_CAROUSEL = {
  enable: false,
  time: 10
}

export const BASE_EVENTS = {
  checked: false,
  showTips: false,
  type: 'jump', // openHidden  jump
  typeList: [
    { key: 'jump', label: 'jump' },
    { key: 'download', label: 'download' },
    { key: 'share', label: 'share' },
    { key: 'fullScreen', label: 'fullScreen' },
    { key: 'showHidden', label: 'showHidden' },
    { key: 'refreshDataV', label: 'refreshDataV' },
    { key: 'refreshView', label: 'refreshView' }
  ],
  jump: {
    value: 'https://',
    type: '_blank'
  },
  download: {
    value: true
  },
  share: {
    value: true
  },
  showHidden: {
    value: true
  },
  refreshDataV: {
    value: true
  },
  refreshView: {
    value: true, // 事件当前值 false
    target: 'all'
  }
}

// 流媒体视频信息配置
export const STREAMMEDIALINKS = {
  videoType: 'flv',
  flv: {
    type: 'flv',
    isLive: false,
    cors: true, // 允许跨域
    loop: true,
    autoplay: false,
    url: null // 网络动画视频
  }
}

// 视频信息配置
export const VIDEO_LINKS_DE2 = {
  videoType: 'web',
  poster: null,
  web: {
    src: null, //视频源
    autoplay: true, // 如果true,浏览器准备好时开始回放。
    muted: true, // 默认情况下将会消除任何音频。
    loop: true, // 导致视频一结束就重新开始。
    preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
    language: 'zh-CN',
    fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
    notSupportedMessage: '此视频暂无法播放，请稍后再试', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
    controls: true,
    controlBar: {
      timeDivider: true,
      remainingTimeDisplay: false,
      fullscreenToggle: true // 全屏按钮
    }
  },
  rtmp: {
    sources: [
      {
        type: 'rtmp/mp4'
      }
    ],
    height: 300,
    techOrder: ['flash'],
    autoplay: false,
    controls: true,
    flash: {
      hls: {
        withCredentials: false
      }
    }
  }
}

// 视频信息配置
export const VIDEO_LINKS = {
  videoType: 'web',
  web: {
    autoplay: true,
    height: 300,
    muted: true,
    loop: true,
    controlBar: {
      timeDivider: false,
      durationDisplay: false,
      remainingTimeDisplay: false,
      currentTimeDisplay: false, // 当前时间
      volumeControl: false, // 声音控制键
      fullscreenToggle: false,
      pause: false
    },
    sources: [{}]
  },
  rtmp: {
    sources: [
      {
        type: 'rtmp/mp4'
      }
    ],
    height: 300,
    techOrder: ['flash'],
    autoplay: false,
    controls: true,
    flash: {
      hls: {
        withCredentials: false
      }
    }
  }
}

// 超链接配置
export const HYPERLINKS = {
  openMode: '_blank',
  enable: false,
  content: 'http://'
}

// 嵌套页面信息
export const FRAMELINKS = {
  src: ''
}

export const defaultStyleValue = {
  ...commonStyle,
  color: '',
  fontSize: 16,
  activeFontSize: 18,
  headHorizontalPosition: 'left',
  headFontColor: '#000000',
  headFontActiveColor: '#000000',
  headBorderColor: '#e4e7ed',
  headBorderActiveColor: '#1E90FF',
  underlineHeight: 2
}

export const ACTION_SELECTION = {
  linkageActive: 'custom'
}

export const MULTI_DIMENSIONAL = {
  enable: false,
  x: 0,
  y: 0,
  z: 0
}

export const COMMON_COMPONENT_BACKGROUND_BASE = {
  backgroundColorSelect: true,
  backdropFilterEnable: false,
  backgroundImageEnable: false,
  backgroundType: 'innerImage',
  innerImage: 'board/board_1.svg',
  outerImage: null,
  innerPadding: 12,
  borderRadius: 0,
  backdropFilter: 4
}

export const COMMON_COMPONENT_BACKGROUND_LIGHT = {
  ...COMMON_COMPONENT_BACKGROUND_BASE,
  backgroundColor: 'rgba(255,255,255,1)',
  innerImageColor: 'rgba(16, 148, 229,1)'
}

export const COMMON_COMPONENT_BACKGROUND_DARK = {
  ...COMMON_COMPONENT_BACKGROUND_BASE,
  backgroundColor: 'rgba(19,28,66,1)',
  innerImageColor: '#1094E5'
}

export const COMMON_COMPONENT_BACKGROUND_SCREEN_DARK = {
  ...COMMON_COMPONENT_BACKGROUND_BASE,
  backgroundColorSelect: false,
  backgroundColor: '#131E42',
  innerImageColor: '#1094E5'
}

export const COMMON_COMPONENT_BACKGROUND_MAP = {
  light: COMMON_COMPONENT_BACKGROUND_LIGHT,
  dark: COMMON_COMPONENT_BACKGROUND_DARK
}

export const COMMON_TAB_TITLE_BACKGROUND = {
  enable: false, // 是否启用tab标题背景
  multiply: true, // 激活状态与非激活状态背景是否复用
  active: COMMON_COMPONENT_BACKGROUND_LIGHT,
  inActive: COMMON_COMPONENT_BACKGROUND_LIGHT
}

export const commonAttr = {
  animations: [],
  canvasId: 'canvas-main',
  events: BASE_EVENTS,
  carousel: BASE_CAROUSEL,
  multiDimensional: MULTI_DIMENSIONAL, // 3d 设置
  groupStyle: {}, // 当一个组件成为 Group 的子组件时使用
  isLock: false, // 是否锁定组件
  maintainRadio: false, // 布局时保持宽高比例
  aspectRatio: 1, // 锁定时的宽高比例
  isShow: true, // 是否显示组件
  dashboardHidden: false, // 仪表板组件隐藏
  category: 'base', //组件类型 base 基础组件 hidden隐藏组件
  // 当前组件动作
  dragging: false,
  resizing: false,
  collapseName: [
    'position',
    'background',
    'style',
    'picture',
    'frameLinks',
    'videoLinks',
    'streamLinks',
    'carouselInfo',
    'events'
  ], // 编辑组件时记录当前使用的是哪个折叠面板，再次回来时恢复上次打开的折叠面板，优化用户体验
  linkage: {
    duration: 0, // 过渡持续时间
    data: [
      // 组件联动
      {
        id: '', // 联动的组件 id
        label: '', // 联动的组件名称
        event: '', // 监听事件
        style: [{ key: '', value: '' }] // 监听的事件触发时，需要改变的属性
      }
    ]
  }
}

// 编辑器左侧组件列表
const list = [
  {
    component: 'Group',
    name: t('visualization.view_group'),
    label: t('visualization.view_group'),
    propValue: '&nbsp;',
    icon: 'icon_graphical',
    innerType: 'Group',
    style: {
      width: 200,
      height: 200
    }
  },
  {
    id: 100000001,
    component: 'GroupArea',
    name: 'group_area',
    label: 'group_area',
    propValue: '&nbsp;',
    icon: 'icon_graphical',
    innerType: 'GroupArea',
    style: {
      width: 200,
      height: 200
    }
  },
  {
    component: 'VQuery',
    name: t('visualization.query_component'),
    label: t('visualization.query_component'),
    propValue: '',
    icon: 'icon_search',
    innerType: 'VQuery',
    isHang: false,
    freeze: false, // 是否冻结再顶部 主画布生效
    x: 1,
    y: 1,
    sizeX: 72,
    sizeY: 4,
    style: {
      width: 400,
      height: 100
    },
    request: {
      method: 'GET',
      data: [],
      url: '',
      series: false, // 是否定时发送请求
      time: 1000, // 定时更新时间
      paramType: '', // string object array
      requestCount: 0 // 请求次数限制，0 为无限
    },
    matrixStyle: {}
  },
  {
    component: 'FormControl',
    name: '表单控件',
    label: '表单控件',
    propValue: {
      title: '表单',
      description: '',
      fields: [
        {
          id: 'field_1',
          type: 'text',
          label: '姓名',
          name: 'name',
          placeholder: '请输入姓名',
          required: true,
          visible: true,
          order: 1,
          validation: {}
        }
      ],
      submitConfig: {
        enabled: true,
        url: '',
        method: 'POST',
        headers: [{ key: 'Content-Type', value: 'application/json' }],
        params: [],
        timeout: 30000,
        successMessage: '提交成功',
        errorMessage: '提交失败',
        resetAfterSubmit: false,
        refreshTargetComponents: []
      },
      layout: {
        columns: 1,
        labelPosition: 'top',
        labelWidth: '100px',
        size: 'default',
        showBorder: true,
        spacing: 16
      },
      showSubmitButton: true,
      submitButtonText: '提交',
      showResetButton: true,
      resetButtonText: '重置'
    },
    icon: 'icon_search',
    innerType: 'FormControl',
    editing: false,
    canvasActive: false,
    x: 1,
    y: 1,
    sizeX: 36,
    sizeY: 20,
    style: {
      width: 400,
      height: 300
    },
    matrixStyle: {},
    category: 'form' // 添加特殊分类，避免在左侧面板显示
  },
  {
    component: 'ModalDialog',
    name: '弹框组件',
    label: '弹框组件',
    propValue: {
      title: '弹框标题',
      content: '',
      width: 600,
      height: 400,
      showHeader: true,
      showFooter: true,
      showCloseButton: true,
      closeOnClickOverlay: true,
      linkedComponentId: '',
      position: 'center'
    },
    icon: 'icon_dialog',
    innerType: 'ModalDialog',
    editing: false,
    canvasActive: false,
    x: 1,
    y: 1,
    sizeX: 30,
    sizeY: 20,
    style: {
      width: 600,
      height: 400
    },
    matrixStyle: {}
  },
  {
    component: 'FormQuery',
    name: '表单查询',
    label: '表单查询',
    propValue: {
      title: '查询表单',
      showTitle: true,
      titleStyle: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: '#333',
        marginBottom: '16px',
        textAlign: 'left'
      },
      fields: [
        {
          id: 'field_1',
          type: 'text',
          label: '关键词',
          name: 'keyword',
          placeholder: '请输入关键词',
          required: false,
          visible: true,
          order: 1,
          validation: {},
          paramMapping: {
            paramName: 'keyword'
          }
        }
      ],
      layout: {
        columns: 2,
        labelPosition: 'top',
        labelWidth: '100px',
        size: 'default',
        showBorder: false,
        spacing: 16,
        actionsAlign: 'left'
      },
      style: {
        backgroundColor: 'transparent',
        padding: '16px',
        borderRadius: '4px',
        border: 'none'
      },
      targetComponents: [],
      autoQuery: false,
      autoQueryDelay: 500,
      initialQuery: false,
      resetAndQuery: true,
      showQueryButton: true,
      queryButtonText: '查询',
      showResetButton: true,
      resetButtonText: '重置',
      showClearButton: false,
      clearButtonText: '清空'
    },
    icon: 'icon_search',
    innerType: 'FormQuery',
    editing: false,
    canvasActive: false,
    x: 1,
    y: 1,
    sizeX: 36,
    sizeY: 15,
    style: {
      width: 400,
      height: 200
    },
    matrixStyle: {},
    category: 'form' // 添加特殊分类，避免在左侧面板显示
  },
  {
    component: 'UserView',
    name: t('visualization.view'),
    label: t('visualization.view'),
    propValue: { textValue: '', urlList: [] },
    icon: 'bar',
    innerType: 'bar',
    editing: false,
    canvasActive: false,
    actionSelection: ACTION_SELECTION,
    x: 1,
    y: 1,
    sizeX: 36,
    sizeY: 14,
    style: {
      adaptation: 'adaptation',
      width: 600,
      height: 300
    },
    matrixStyle: {}
  },
  {
    component: 'DeVideo',
    name: t('visualization.video'),
    label: t('visualization.video'),
    innerType: 'DeVideo',
    editing: false,
    canvasActive: false,
    icon: 'icon-video',
    x: 1,
    y: 1,
    sizeX: 36,
    sizeY: 14,
    style: {
      width: 600,
      height: 300
    },
    videoLinks: VIDEO_LINKS_DE2,
    matrixStyle: {}
  },
  {
    component: 'DeStreamMedia',
    name: t('visualization.stream_media'),
    label: t('visualization.stream_media'),
    innerType: 'DeStreamMedia',
    editing: false,
    canvasActive: false,
    icon: 'icon-stream',
    x: 1,
    y: 1,
    sizeX: 36,
    sizeY: 14,
    style: {
      width: 600,
      height: 300
    },
    streamMediaLinks: STREAMMEDIALINKS,
    matrixStyle: {}
  },
  {
    component: 'DeFrame',
    name: t('visualization.web'),
    label: t('visualization.web'),
    innerType: 'DeFrame',
    editing: false,
    canvasActive: false,
    icon: 'db-more-web',
    hyperlinks: HYPERLINKS,
    frameLinks: FRAMELINKS,
    x: 1,
    y: 1,
    sizeX: 36,
    sizeY: 14,
    style: {
      width: 600,
      height: 300
    },
    matrixStyle: {}
  },
  {
    component: 'DeTimeClock',
    name: t('visualization.time_component'),
    label: t('visualization.time_component'),
    icon: 'dv-more-time-clock',
    innerType: 'DeTimeClock',
    editing: false,
    canvasActive: false,
    x: 1,
    y: 1,
    sizeX: 36,
    sizeY: 12,
    propValue: {},
    style: {
      width: 300,
      height: 100,
      fontSize: 22,
      fontWeight: 'normal',
      fontStyle: 'normal',
      textAlign: 'center',
      color: '#000000'
    },
    formatInfo: {
      openMode: '0',
      showWeek: false,
      showDate: true,
      dateFormat: 'yyyy-MM-dd',
      timeFormat: 'hh:mm:ss'
    },
    matrixStyle: {}
  },
  {
    component: 'Picture',
    name: t('visualization.picture'),
    label: t('visualization.picture'),
    icon: 'dv-picture-real',
    innerType: 'Picture',
    editing: false,
    canvasActive: false,
    x: 1,
    y: 1,
    sizeX: 36,
    sizeY: 14,
    propValue: {
      url: '',
      flip: {
        horizontal: false,
        vertical: false
      }
    },
    style: {
      adaptation: 'adaptation',
      width: 300,
      height: 200
    },
    matrixStyle: {}
  },
  {
    component: 'CanvasIcon',
    name: t('visualization.icon'),
    label: t('visualization.icon'),
    propValue: '',
    icon: 'other_material_icon',
    innerType: '',
    editing: false,
    canvasActive: false,
    x: 1,
    y: 1,
    sizeX: 10,
    sizeY: 10,
    style: {
      width: 40,
      height: 40,
      color: '',
      backdropFilter: 'blur(0px)'
    }
  },
  {
    component: 'CanvasBoard',
    name: t('visualization.board'),
    label: t('visualization.board'),
    propValue: '',
    icon: 'other_material_board',
    innerType: '',
    editing: false,
    canvasActive: false,
    x: 1,
    y: 1,
    sizeX: 30,
    sizeY: 30,
    style: {
      width: 600,
      height: 300,
      color: 'rgb(255, 255, 255,1)',
      backdropFilter: 'blur(0px)'
    }
  },
  {
    component: 'RectShape',
    name: t('visualization.rect_shape'),
    label: t('visualization.rect_shape'),
    propValue: '&nbsp;',
    icon: 'icon_graphical',
    style: {
      width: 200,
      height: 200,
      backgroundColor: 'rgba(236,231,231,0.1)',
      borderActive: true,
      backdropFilter: 'blur(0px)'
    }
  },
  {
    component: 'CircleShape',
    name: t('visualization.circle_shape'),
    label: t('visualization.circle_shape'),
    propValue: '&nbsp;',
    icon: 'icon_graphical',
    style: {
      width: 200,
      height: 200,
      borderWidth: 1,
      borderStyle: 'solid',
      borderColor: '#cccccc',
      borderActive: true,
      backgroundColor: 'rgba(236,231,231,0.1)',
      backdropFilter: 'blur(0px)'
    }
  },
  {
    component: 'SvgTriangle',
    name: t('visualization.triangle'),
    label: t('visualization.triangle'),
    icon: 'icon_graphical',
    propValue: '',
    style: {
      width: 200,
      height: 200,
      borderWidth: 1,
      borderColor: '#cccccc',
      borderActive: true,
      backgroundColor: 'rgba(236,231,231,0.1)',
      backdropFilter: 'blur(0px)'
    }
  },
  {
    component: 'DeTabs',
    name: t('visualization.tabs'),
    label: t('visualization.tabs'),
    propValue: [
      {
        name: 'tab',
        title: t('visualization.new_tab'),
        componentData: [],
        closable: true
      }
    ],
    icon: 'dv-tab',
    innerType: '',
    editing: false,
    canvasActive: false,
    x: 1,
    y: 1,
    sizeX: 36,
    sizeY: 14,
    style: {
      width: 600,
      height: 300,
      fontSize: 16,
      activeFontSize: 18,
      headHorizontalPosition: 'left',
      headFontColor: '#000000',
      headFontActiveColor: '#000000',
      headBorderColor: '#e4e7ed',
      headBorderActiveColor: '#1E90FF',
      underlineHeight: 2,
      titleHide: false,
      showTabTitle: true,
      // #13540
      fontWeight: 'normal',
      fontStyle: 'normal',
      textDecoration: 'none'
    }
  },
  {
    component: 'ScrollText',
    name: t('visualization.scroll_text'),
    label: t('visualization.scroll_text'),
    propValue: t('visualization.component_input_tips'),
    innerType: 'ScrollText',
    icon: 'scroll-text',
    x: 1,
    y: 1,
    sizeX: 36,
    sizeY: 14,
    style: {
      width: 400,
      height: 80,
      fontSize: 14,
      fontWeight: 400,
      letterSpacing: 0,
      color: '',
      padding: 4,
      verticalAlign: 'middle',
      scrollSpeed: 0
    }
  },
  {
    component: 'CardContainer',
    name: '卡片容器',
    label: '卡片容器',
    propValue: {
      title: '卡片标题',
      showHeader: true,
      showExtra: false,
      placeholder: '暂无内容',
      componentData: [],
      headerStyle: {
        backgroundColor: '#f5f5f5',
        borderBottom: '1px solid #e8e8e8',
        padding: '12px 16px',
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        backgroundImage: '',
        backgroundImageEnable: false,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      },
      bodyStyle: {
        padding: '16px',
        minHeight: '100px'
      },
      cardStyle: {
        border: '1px solid #e8e8e8',
        borderRadius: '6px',
        backgroundColor: '#fff',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }
    },
    innerType: 'CardContainer',
    icon: 'icon_card_outlined',
    x: 1,
    y: 1,
    sizeX: 36,
    sizeY: 20,
    style: {
      width: 400,
      height: 300
    }
  },
  {
    component: 'ListContainer',
    name: '列表容器',
    label: '列表容器',
    propValue: {
      title: '通知公告',
      showHeader: true,
      showIcon: true,
      showDescription: true,
      showTime: true,
      showAction: true,
      actionType: 'icon',
      actionText: '查看',
      actionIcon: 'ArrowRight',
      buttonType: 'primary',
      buttonSize: 'small',
      iconSize: 16,
      actionIconSize: 16,
      emptyText: '暂无数据',

      // 新增配置项
      listStyle: 'notice', // notice(公告), timeline(时间轴), task(任务), warning(预警)
      dataSource: 'static', // static(静态数据), rest(REST接口)
      restConfig: null, // REST接口配置
      restFields: [], // REST字段映射配置
      restData: [], // REST数据缓存

      // 字段映射配置
      fieldMapping: {
        title: 'title',
        description: 'description',
        time: 'time',
        icon: 'icon',
        status: 'status',
        priority: 'priority',
        actionText: 'actionText'
      },

      // 边框配置
      borderConfig: {
        showContainerBorder: true,
        containerBorderColor: '#e8e8e8',
        containerBorderWidth: '1px',
        containerBorderStyle: 'solid',
        showItemBorder: true,
        itemBorderColor: '#f0f0f0',
        itemBorderWidth: '1px',
        itemBorderStyle: 'solid',
        showHeaderBorder: true,
        headerBorderColor: '#e8e8e8',
        headerBorderWidth: '1px',
        headerBorderStyle: 'solid'
      },
      items: [
        {
          title: '维护通知：2024-12-07 系统凌晨维护',
          description: '机房维护，请各单位做好工作安排，感谢支持！',
          time: '2024-12-07',
          icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMSA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDMgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjNDA5RUZGIi8+Cjwvc3ZnPgo=',
          actionText: '查看'
        },
        {
          title: '维护通知：2024-12-05 系统凌晨维护',
          description: '机房维护，请各单位做好工作安排，感谢支持！',
          time: '2024-12-05',
          icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMSA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDMgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjNDA5RUZGIi8+Cjwvc3ZnPgo=',
          actionText: '查看'
        }
      ],
      containerStyle: {
        backgroundColor: '#fff',
        border: '1px solid #e8e8e8',
        borderRadius: '6px',
        padding: '0'
      },
      headerStyle: {
        padding: '12px 16px',
        borderBottom: '1px solid #e8e8e8',
        backgroundColor: '#f5f5f5',
        fontSize: '16px',
        fontWeight: 'bold',
        color: '#333'
      },
      itemStyle: {
        padding: '12px 16px',
        borderBottom: '1px solid #f0f0f0',
        cursor: 'pointer',
        transition: 'background-color 0.2s'
      },
      itemHoverStyle: {
        backgroundColor: '#f5f5f5'
      },
      titleStyle: {
        fontSize: '14px',
        fontWeight: '500',
        color: '#333',
        marginBottom: '4px'
      },
      descriptionStyle: {
        fontSize: '12px',
        color: '#666',
        marginBottom: '4px'
      },
      timeStyle: {
        fontSize: '12px',
        color: '#999'
      },
      iconStyle: {
        marginRight: '12px',
        color: '#409eff'
      },
      actionStyle: {
        color: '#409eff',
        cursor: 'pointer'
      }
    },
    innerType: 'ListContainer',
    icon: 'icon_view-list_outlined',
    x: 1,
    y: 1,
    sizeX: 36,
    sizeY: 20,
    style: {
      width: 400,
      height: 300
    }
  }
]

for (let i = 0, len = list.length; i < len; i++) {
  const item = list[i]
  item.style = { ...commonStyle, ...item.style }
  item['commonBackground'] = deepCopy(COMMON_COMPONENT_BACKGROUND_BASE)
  item['state'] = 'prepare'
  list[i] = { ...commonAttr, ...item }
}

export function findNewComponentFromList(
  componentName,
  innerType,
  curOriginThemes,
  staticMap?: object
) {
  const isPlugin = !!staticMap
  let newComponent
  list.forEach(comp => {
    if (comp.component === componentName) {
      newComponent = deepCopy(comp)
      newComponent['commonBackground'] = deepCopy(
        COMMON_COMPONENT_BACKGROUND_MAP[curOriginThemes.value]
      )
      newComponent.innerType = innerType
      if (comp.component === 'DeTabs') {
        newComponent.propValue[0].name = guid()
        newComponent['titleBackground'] = deepCopy(COMMON_TAB_TITLE_BACKGROUND)
      }
    }
  })

  if (componentName === 'UserView') {
    const viewConfig = getViewConfig(innerType)
    newComponent.name = viewConfig?.title
    newComponent.label = viewConfig?.title
    newComponent.render = viewConfig?.render
    newComponent.type = viewConfig?.value
    newComponent.isPlugin = !!isPlugin
    if (isPlugin) {
      newComponent.staticMap = staticMap
    }

    // 为按钮类型设置特殊的默认配置
    if (innerType === 'button') {
      // 确保按钮组件不被当作插件
      newComponent.isPlugin = false
      newComponent.propValue = {
        text: '按钮',
        type: 'primary',
        size: 'default',
        disabled: false,
        loading: false,
        round: false,
        plain: false,
        fullWidth: false,
        customColor: false,
        textColor: '#ffffff',
        backgroundColor: '#409eff',
        borderColor: '#409eff',
        enableJump: false,
        jumpUrl: '',
        jumpTarget: '_self',
        customEvent: false,
        eventData: ''
      }
      // 为按钮组件设置完全透明背景，移除所有容器样式
      newComponent.commonBackground = {
        backgroundColorSelect: false,
        backdropFilterEnable: false,
        backgroundImageEnable: false,
        backgroundType: 'innerImage',
        innerImage: '',
        outerImage: '',
        innerPadding: 0,
        borderRadius: 0,
        backdropFilter: 0,
        backgroundColor: 'transparent',
        innerImageColor: 'transparent'
      }
      // 为按钮组件设置更合适的默认尺寸
      newComponent.style = {
        ...newComponent.style,
        width: 120, // 按钮默认宽度120px
        height: 40 // 按钮默认高度40px
      }
      // 设置矩阵布局的默认尺寸
      newComponent.sizeX = 6 // 矩阵宽度（相对于36列网格）
      newComponent.sizeY = 2 // 矩阵高度（相对于行高）
    }

    // 为嵌套菜单类型设置特殊的默认配置
    if (innerType === 'nested-menu') {
      // 确保嵌套菜单组件不被当作插件
      newComponent.isPlugin = false
      // 嵌套菜单组件不需要数据源
      newComponent.datasourceType = 'none'
      newComponent.propValue = {
        mode: 'vertical',
        collapsed: false,
        uniqueOpened: true,
        backgroundColor: '#ffffff',
        textColor: '#303133',
        activeTextColor: '#409eff',
        activeIndex: '',
        // 文字样式配置
        fontSize: 14,
        fontWeight: 'normal',
        fontStyle: 'normal',
        textAlign: 'left',
        lineHeight: 1.5,
        letterSpacing: 0,
        menuItems: [
          {
            id: 'menu_1',
            title: '工作台',
            icon: 'folder',
            link: '',
            target: '_self',
            children: []
          },
          {
            id: 'menu_2',
            title: '智力问答',
            icon: 'chat',
            link: '',
            target: '_self',
            children: [
              {
                id: 'menu_2_1',
                title: '问答管理',
                icon: 'document',
                link: '',
                target: '_self'
              }
            ]
          },
          {
            id: 'menu_3',
            title: '审核管理',
            icon: 'check',
            link: '',
            target: '_self',
            children: []
          }
        ]
      }
      // 为嵌套菜单组件设置透明背景
      newComponent.commonBackground = {
        backgroundColorSelect: false,
        backdropFilterEnable: false,
        backgroundImageEnable: false,
        backgroundType: 'innerImage',
        innerImage: '',
        outerImage: '',
        innerPadding: 0,
        borderRadius: 0,
        backdropFilter: 0,
        backgroundColor: 'transparent',
        innerImageColor: 'transparent'
      }
      // 为嵌套菜单组件设置合适的默认尺寸
      newComponent.style = {
        ...newComponent.style,
        width: 200, // 菜单默认宽度200px
        height: 400 // 菜单默认高度400px
      }
      // 设置矩阵布局的默认尺寸
      newComponent.sizeX = 9 // 矩阵宽度（相对于36列网格）
      newComponent.sizeY = 30 // 矩阵高度（相对于行高）
    }
  }
  return newComponent
}

export function findBaseDeFaultAttr(componentName) {
  let result = {}
  list.forEach(comp => {
    if (comp.component === componentName) {
      const stylePropertyInner = []
      Object.keys(comp.style).forEach(styleKey => {
        if (
          (!['width', 'height'].includes(styleKey) &&
            componentName === 'VQuery' &&
            !Object.keys(commonStyle).includes(styleKey)) ||
          componentName !== 'VQuery'
        ) {
          stylePropertyInner.push(styleKey)
        }
      })
      result = {
        properties: ['common-style', 'background-overall-component'],
        propertyInner: {
          'common-style': stylePropertyInner,
          'background-overall-component': ['all']
        },
        value: comp.name,
        componentType: componentName
      }
    }
  })
  return result
}

export default list
