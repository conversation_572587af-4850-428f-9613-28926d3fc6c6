<template>
  <div class="de-nested-menu-view">
    <el-menu
      :default-active="activeIndex"
      :collapse="collapsed"
      :background-color="menuConfig.backgroundColor"
      :text-color="menuConfig.textColor"
      :active-text-color="menuConfig.activeTextColor"
      :unique-opened="menuConfig.uniqueOpened"
      :mode="menuConfig.mode"
      class="nested-menu"
      :style="menuStyle"
      @select="handleMenuSelect"
    >
      <template v-for="item in menuItems" :key="item?.id || item?.title">
        <!-- 有子菜单的项 -->
        <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.id">
          <template #title>
            <el-icon :style="getSubMenuTitleStyle()">
              <Folder />
            </el-icon>
            <span :style="getSubMenuTitleStyle()">{{ item.title }}</span>
          </template>

          <!-- 子菜单项 -->
          <el-menu-item
            v-for="child in item.children"
            :key="child.id"
            :index="child.id"
            :style="getMenuItemStyle(child.id)"
          >
            <el-icon :style="getMenuItemStyle(child.id)">
              <Document />
            </el-icon>
            <span :style="getMenuItemStyle(child.id)">{{ child.title }}</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 无子菜单的项 -->
        <el-menu-item v-else :index="item.id" :style="getMenuItemStyle(item.id)">
          <el-icon :style="getMenuItemStyle(item.id)">
            <Document />
          </el-icon>
          <span :style="getMenuItemStyle(item.id)">{{ item.title }}</span>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs } from 'vue'
import { Folder, Document } from '@element-plus/icons-vue'
import { ElMenu, ElMenuItem, ElSubMenu, ElIcon } from 'element-plus-secondary'

const props = defineProps({
  element: {
    type: Object,
    required: true
  },
  showPosition: {
    type: String,
    default: 'canvas'
  },
  scale: {
    type: Number,
    default: 1
  },
  themes: {
    type: String,
    default: 'light'
  }
})

const emit = defineEmits(['onComponentEvent', 'onPointClick'])

const { element } = toRefs(props)

// 组件初始化

// 菜单配置
const menuConfig = computed(() => {
  const config = element.value.propValue || {}
  return {
    backgroundColor: config.backgroundColor || '#ffffff',
    textColor: config.textColor || '#303133',
    activeTextColor: config.activeTextColor || '#409eff',
    uniqueOpened: config.uniqueOpened !== false,
    mode: config.mode || 'vertical',
    collapsed: config.collapsed || false
  }
})

// 动态样式
const menuStyle = computed(() => {
  const config = element.value.propValue || {}
  console.log('菜单样式配置:', {
    backgroundColor: menuConfig.value.backgroundColor,
    textColor: menuConfig.value.textColor,
    activeTextColor: menuConfig.value.activeTextColor,
    fontSize: config.fontSize,
    fontWeight: config.fontWeight
  })
  return {
    backgroundColor: menuConfig.value.backgroundColor,
    color: menuConfig.value.textColor,
    fontSize: `${config.fontSize || 14}px`,
    fontWeight: config.fontWeight || 'normal',
    fontStyle: config.fontStyle || 'normal',
    lineHeight: config.lineHeight || 1.5,
    letterSpacing: `${config.letterSpacing || 0}px`
  }
})

// 菜单项数据
const menuItems = computed(() => {
  const items = element.value.propValue?.menuItems || []

  // 如果没有菜单项，返回默认菜单项
  if (!items || items.length === 0) {
    return [
      {
        id: 'menu_1',
        title: '工作台',
        icon: 'folder',
        link: '',
        target: '_self',
        children: []
      },
      {
        id: 'menu_2',
        title: '智力问答',
        icon: 'chat',
        link: '',
        target: '_self',
        children: [
          {
            id: 'menu_2_1',
            title: '问答管理',
            icon: 'document',
            link: '',
            target: '_self'
          }
        ]
      },
      {
        id: 'menu_3',
        title: '审核管理',
        icon: 'check',
        link: '',
        target: '_self',
        children: []
      }
    ]
  }

  return items
})

// 当前激活的菜单项
const activeIndex = computed(() => {
  return element.value.propValue?.activeIndex || ''
})

// 获取菜单项样式
const getMenuItemStyle = (itemId: string) => {
  const isActive = activeIndex.value === itemId
  const config = element.value.propValue || {}
  return {
    color: isActive ? menuConfig.value.activeTextColor : menuConfig.value.textColor,
    fontSize: `${config.fontSize || 14}px`,
    fontWeight: config.fontWeight || 'normal',
    fontStyle: config.fontStyle || 'normal',
    textAlign: config.textAlign || 'left',
    lineHeight: config.lineHeight || 1.5,
    letterSpacing: `${config.letterSpacing || 0}px`
  }
}

// 获取父菜单标题样式
const getSubMenuTitleStyle = () => {
  const config = element.value.propValue || {}
  return {
    color: menuConfig.value.textColor,
    fontSize: `${config.fontSize || 14}px`,
    fontWeight: config.fontWeight || 'normal',
    fontStyle: config.fontStyle || 'normal',
    textAlign: config.textAlign || 'left',
    lineHeight: config.lineHeight || 1.5,
    letterSpacing: `${config.letterSpacing || 0}px`
  }
}

// 是否折叠
const collapsed = computed(() => {
  return element.value.propValue?.collapsed || false
})

// 菜单选择事件处理
const handleMenuSelect = (index: string) => {
  // 查找对应的菜单项
  const findMenuItem = (items: any[], targetId: string): any => {
    for (const item of items) {
      if (item.id === targetId) {
        return item
      }
      if (item.children) {
        const found = findMenuItem(item.children, targetId)
        if (found) return found
      }
    }
    return null
  }
  
  const selectedItem = findMenuItem(menuItems.value, index)
  
  if (selectedItem) {
    // 更新激活状态
    if (element.value.propValue) {
      element.value.propValue.activeIndex = index
    }

    // 触发组件事件
    emit('onComponentEvent')

    // 如果配置了跳转链接
    if (selectedItem.link) {
      if (selectedItem.target === '_blank') {
        window.open(selectedItem.link)
      } else {
        window.location.href = selectedItem.link
      }
    }

    // 触发自定义事件
    emit('onPointClick', {
      type: 'menu-select',
      data: {
        index,
        item: selectedItem
      },
      element: element.value
    })
  }
}
</script>

<style lang="less" scoped>
.de-nested-menu-view {
  width: 100%;
  height: 100%;

  .empty-menu {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
  }

  .nested-menu {
    width: 100%;
    height: 100%;
    border: none;

    :deep(.el-menu-item) {
      transition: all 0.3s;

      span {
        color: inherit !important;
      }

      &.is-active {
        color: v-bind('menuConfig.activeTextColor') !important;

        span {
          color: v-bind('menuConfig.activeTextColor') !important;
        }

        .el-icon {
          color: v-bind('menuConfig.activeTextColor') !important;
        }
      }
    }

    :deep(.el-sub-menu__title) {
      transition: all 0.3s;

      span {
        color: inherit !important;
        font-size: inherit !important;
        font-weight: inherit !important;
        font-style: inherit !important;
        line-height: inherit !important;
        letter-spacing: inherit !important;
      }

      .el-icon {
        color: inherit !important;
      }
    }

    :deep(.el-icon) {
      margin-right: 8px;
    }

    // 更强的激活状态样式覆盖
    :deep(.el-menu-item.is-active) {
      color: v-bind('menuConfig.activeTextColor') !important;

      * {
        color: v-bind('menuConfig.activeTextColor') !important;
      }
    }

    // 确保子菜单的激活状态也正确
    :deep(.el-sub-menu.is-active > .el-sub-menu__title) {
      color: v-bind('menuConfig.activeTextColor') !important;

      * {
        color: v-bind('menuConfig.activeTextColor') !important;
      }
    }

    // 确保展开的子菜单标题样式正确
    :deep(.el-sub-menu.is-opened > .el-sub-menu__title) {
      span {
        color: inherit !important;
        font-size: inherit !important;
        font-weight: inherit !important;
        font-style: inherit !important;
        line-height: inherit !important;
        letter-spacing: inherit !important;
      }
    }
  }
}
</style>
