<template>
  <div class="atomic-group">
    <!-- 按钮组件 -->
    <div class="component-item" @click="newComponent('UserView', 'button')">
      <div class="component-icon">
        <Icon name="button_right">
          <button_right class="svg-icon" />
        </Icon>
      </div>
      <div class="component-label">按钮</div>
    </div>

    <!-- 嵌套菜单组件 -->
    <div class="component-item" @click="newComponent('UserView', 'nested-menu')">
      <div class="component-icon">
        <Icon name="dv_more">
          <dv_more class="svg-icon" />
        </Icon>
      </div>
      <div class="component-label">嵌套菜单</div>
    </div>

    <!-- 弹框组件 -->
    <div class="component-item" @click="newComponent('ModalDialog', 'ModalDialog')">
      <div class="component-icon">
        <Icon name="modal_icon">
          <modal_icon class="svg-icon" />
        </Icon>
      </div>
      <div class="component-label">弹框</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import eventBus from '@/utils/eventBus'
import Icon from '@/components/icon-custom/src/Icon.vue'
import button_right from '@/assets/svg/button_right.svg'
import dv_more from '@/assets/svg/dv-more.svg'
import modal_icon from '@/assets/svg/dv-more.svg' // 临时使用相同图标，后续可替换

const newComponent = (componentType: string, innerType?: string) => {
  if (innerType) {
    eventBus.emit('handleNew', { componentName: componentType, innerType: innerType })
  } else {
    eventBus.emit('handleNew', { componentName: componentType, innerType: componentType })
  }
}
</script>

<style lang="less" scoped>
.atomic-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;

  &:hover {
    border-color: #409eff;
    background: #f0f9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
  }

  .component-icon {
    font-size: 20px;
    color: #606266;
    margin-bottom: 4px;

    .svg-icon {
      width: 20px;
      height: 20px;
    }
  }

  .component-label {
    font-size: 12px;
    color: #606266;
    text-align: center;
    line-height: 1;
  }

  &:hover .component-icon,
  &:hover .component-label {
    color: #409eff;
  }
}
</style>
