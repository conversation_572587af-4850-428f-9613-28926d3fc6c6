<template>
  <div class="avatar-uploader-container" :class="`img-area_${themes}`">
    <el-upload
      action=""
      :effect="themes"
      accept=".jpeg,.jpg,.png,.gif,.svg"
      class="avatar-uploader"
      list-type="picture-card"
      :class="{ disabled: state.uploadDisabled }"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove"
      :http-request="upload"
      :before-upload="beforeUploadCheck"
      :file-list="state.fileList"
    >
      <el-icon><Plus /></el-icon>
    </el-upload>

    <input
      id="input"
      ref="files"
      type="file"
      accept=".jpeg,.jpg,.png,.gif,.svg"
      hidden
      @click="
        e => {
          e.target.value = ''
        }
      "
      v-on:change="reUpload"
    />

    <img-view-dialog v-model="state.dialogVisible" :image-url="state.dialogImageUrl" />
    
    <!-- 显示图片信息 -->
    <div v-if="state.imageInfo" class="image-info" :class="`image-info_${themes}`">
      <span class="size-info">{{ state.imageInfo }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, toRefs, watch } from 'vue'
import { snapshotStoreWithOut } from '@/store/modules/data-visualization/snapshot'
import { ElMessage } from 'element-plus-secondary'
import ImgViewDialog from '@/custom-component/ImgViewDialog.vue'
import { 
  handleImageUpload, 
  isBase64Image, 
  getBase64Size, 
  formatFileSize,
  validateImageFile
} from '@/utils/imageBase64Utils'

const snapshotStore = snapshotStoreWithOut()
const emits = defineEmits(['onImgChange'])
const files = ref(null)

const props = defineProps({
  imgUrl: {
    type: String
  },
  themes: {
    type: String,
    default: 'dark'
  },
  compress: {
    type: Boolean,
    default: true
  },
  quality: {
    type: Number,
    default: 0.8
  },
  maxWidth: {
    type: Number,
    default: 1920
  },
  maxHeight: {
    type: Number,
    default: 1080
  },
  backgroundColor: {
    type: String,
    default: '#FFFFFF'
  },
  preserveTransparency: {
    type: Boolean,
    default: undefined
  }
})

const { themes, imgUrl, compress, quality, maxWidth, maxHeight, backgroundColor, preserveTransparency } = toRefs(props)
const imgUrlInner = ref(null)

const state = reactive({
  fileList: [],
  dialogImageUrl: '',
  dialogVisible: false,
  uploadDisabled: false,
  imageInfo: ''
})

const init = () => {
  imgUrlInner.value = imgUrl.value
  if (imgUrlInner.value) {
    // 如果是base64图片，直接使用
    if (isBase64Image(imgUrlInner.value)) {
      state.fileList = [{ url: imgUrlInner.value }]
      state.imageInfo = `大小: ${formatFileSize(getBase64Size(imgUrlInner.value))}`
    } else {
      // 传统URL图片
      state.fileList = [{ url: imgUrlInner.value }]
      state.imageInfo = ''
    }
  } else {
    state.fileList = []
    state.imageInfo = ''
  }
}

const handleRemove = () => {
  state.uploadDisabled = false
  imgUrlInner.value = null
  state.fileList = []
  state.imageInfo = ''
  emits('onImgChange', null)
}

const handlePictureCardPreview = file => {
  state.dialogImageUrl = file.url
  state.dialogVisible = true
}

const beforeUploadCheck = (file) => {
  const validation = validateImageFile(file)
  if (!validation.valid) {
    ElMessage.error(validation.message)
    return false
  }
  return true
}

const upload = async (file) => {
  try {
    const result = await handleImageUpload(file.file, {
      compress: compress.value,
      quality: quality.value,
      maxWidth: maxWidth.value,
      maxHeight: maxHeight.value,
      backgroundColor: backgroundColor.value,
      preserveTransparency: preserveTransparency.value
    })

    if (result.success) {
      snapshotStore.recordSnapshotCache('deUploadBase64')
      imgUrlInner.value = result.data
      state.imageInfo = `大小: ${formatFileSize(getBase64Size(result.data))}`
      emits('onImgChange', result.data)
      ElMessage.success(result.message)
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    ElMessage.error('图片上传失败，请重试')
  }
}

const reUpload = async (e) => {
  const file = e.target.files[0]
  if (!file) return

  try {
    const result = await handleImageUpload(file, {
      compress: compress.value,
      quality: quality.value,
      maxWidth: maxWidth.value,
      maxHeight: maxHeight.value,
      backgroundColor: backgroundColor.value,
      preserveTransparency: preserveTransparency.value
    })

    if (result.success) {
      snapshotStore.recordSnapshotCache('uploadFileResultBase64')
      imgUrlInner.value = result.data
      state.fileList = [{ url: result.data }]
      state.imageInfo = `大小: ${formatFileSize(getBase64Size(result.data))}`
      emits('onImgChange', result.data)
      ElMessage.success(result.message)
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('图片重新上传失败:', error)
    ElMessage.error('图片重新上传失败，请重试')
  }
}

onMounted(() => {
  init()
})

watch(
  () => imgUrl.value,
  () => {
    init()
  }
)
</script>

<style scoped lang="less">
.image-info {
  margin-top: 8px;
  text-align: center;
  
  .size-info {
    font-size: 12px;
    color: #666;
  }
  
  &.image-info_dark .size-info {
    color: #999;
  }
}

.avatar-uploader-container {
  .el-upload--picture-card {
    width: 100px;
    height: 100px;
  }
}
</style>
