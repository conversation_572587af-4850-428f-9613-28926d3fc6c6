<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus-secondary'
import { Plus, Edit, Delete, InfoFilled, ArrowDown } from '@element-plus/icons-vue'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import type { GlobalVariable, GlobalVariableFormData } from '@/types/globalVariable'
import { GLOBAL_VARIABLE_RULES, COMMON_ROUTE_PARAMS } from '@/types/globalVariable'
import { generateID } from '@/utils/generateID'

const dvMainStore = dvMainStoreWithOut()

// 组件状态
const showDialog = ref(false)
const showFormDialog = ref(false)
const editingVariable = ref<GlobalVariable | null>(null)
const loading = ref(false)

// 表单数据
const formData = reactive<GlobalVariableFormData>({
  key: '',
  type: 'static',
  value: '',
  routeParam: '',
  description: '',
  enabled: true
})

// 表单验证规则
const rules = GLOBAL_VARIABLE_RULES

// 表单引用
const formRef = ref()

// 计算属性
const globalVariables = computed(() => dvMainStore.globalVariables)
const globalVariableValues = computed(() => dvMainStore.globalVariableValues)

// 表格列配置
const tableColumns = [
  { prop: 'key', label: '变量名', width: 150 },
  { prop: 'type', label: '类型', width: 100 },
  { prop: 'value', label: '配置值', width: 200 },
  { prop: 'resolvedValue', label: '解析值', width: 150 },
  { prop: 'enabled', label: '状态', width: 80 },
  { prop: 'description', label: '描述', minWidth: 150 },
  { prop: 'actions', label: '操作', width: 120, fixed: 'right' }
]

// 打开管理对话框
const openManager = () => {
  showDialog.value = true
  refreshVariables()
}

// 刷新变量列表
const refreshVariables = () => {
  dvMainStore.resolveGlobalVariables()
}

// 打开新增对话框
const openAddDialog = () => {
  editingVariable.value = null
  resetForm()
  showFormDialog.value = true
}

// 打开编辑对话框
const openEditDialog = (variable: GlobalVariable) => {
  editingVariable.value = variable
  formData.key = variable.key
  formData.type = variable.type
  formData.value = variable.value || ''
  formData.routeParam = variable.routeParam || ''
  formData.description = variable.description || ''
  formData.enabled = variable.enabled
  showFormDialog.value = true
}

// 重置表单
const resetForm = () => {
  formData.key = ''
  formData.type = 'static'
  formData.value = ''
  formData.routeParam = ''
  formData.description = ''
  formData.enabled = true
  formRef.value?.clearValidate()
}

// 保存变量
const saveVariable = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 检查变量名是否重复
    const existingVariable = globalVariables.value.find(
      v => v.key === formData.key && (!editingVariable.value || v.id !== editingVariable.value.id)
    )

    if (existingVariable) {
      ElMessage.error('变量名已存在，请使用其他名称')
      return
    }

    loading.value = true

    const now = new Date().toISOString()

    if (editingVariable.value) {
      // 更新现有变量
      const updatedVariable: Partial<GlobalVariable> = {
        key: formData.key,
        type: formData.type,
        value: formData.type === 'static' ? formData.value : undefined,
        routeParam: formData.type === 'route' ? formData.routeParam : undefined,
        description: formData.description,
        enabled: formData.enabled,
        updateTime: now
      }

      dvMainStore.updateGlobalVariable(editingVariable.value.id, updatedVariable)
      ElMessage.success('变量更新成功')
    } else {
      // 新增变量
      const newVariable: GlobalVariable = {
        id: generateID(),
        key: formData.key,
        type: formData.type,
        value: formData.type === 'static' ? formData.value : undefined,
        routeParam: formData.type === 'route' ? formData.routeParam : undefined,
        description: formData.description,
        enabled: formData.enabled,
        createTime: now,
        updateTime: now
      }

      dvMainStore.addGlobalVariable(newVariable)
      ElMessage.success('变量添加成功')
    }

    showFormDialog.value = false
    resetForm()
  } catch (error) {
    console.error('保存变量失败:', error)
  } finally {
    loading.value = false
  }
}

// 删除变量
const deleteVariable = async (variable: GlobalVariable) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除变量 "${variable.key}" 吗？删除后无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    dvMainStore.deleteGlobalVariable(variable.id)
    ElMessage.success('变量删除成功')
  } catch (error) {
    // 用户取消删除
  }
}

// 切换变量启用状态
const toggleVariableEnabled = (variable: GlobalVariable) => {
  dvMainStore.updateGlobalVariable(variable.id, { enabled: !variable.enabled })
}

// 获取类型显示文本
const getTypeText = (type: string) => {
  return type === 'static' ? '静态值' : '路由参数'
}

// 获取状态显示文本
const getStatusText = (enabled: boolean) => {
  return enabled ? '启用' : '禁用'
}

// 获取解析后的值
const getResolvedValue = (variable: GlobalVariable) => {
  const value = globalVariableValues.value[variable.key]
  if (value === undefined || value === null) {
    return variable.enabled ? '未解析' : '-'
  }
  return String(value)
}

// 选择常用路由参数
const selectCommonRouteParam = (paramValue: string) => {
  formData.routeParam = paramValue
}

// 组件挂载时初始化
onMounted(() => {
  refreshVariables()
})

// 暴露方法给父组件
defineExpose({
  openManager
})
</script>

<template>
  <div class="global-variable-manager">
    <!-- 管理对话框 -->
    <el-dialog
      v-model="showDialog"
      title="全局变量管理"
      width="80%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="manager-content">
        <!-- 工具栏 -->
        <div class="toolbar">
          <el-button type="primary" :icon="Plus" @click="openAddDialog"> 添加变量 </el-button>
          <el-button @click="refreshVariables"> 刷新 </el-button>
          <div class="toolbar-info">
            <el-icon><InfoFilled /></el-icon>
            <span>全局变量可在REST接口参数中使用 ${变量名} 或 {{ 变量名 }} 格式引用</span>
          </div>
        </div>

        <!-- 变量列表 -->
        <el-table
          :data="globalVariables"
          border
          stripe
          style="width: 100%"
          empty-text="暂无全局变量"
        >
          <el-table-column prop="key" label="变量名" width="150" show-overflow-tooltip />

          <el-table-column prop="type" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="row.type === 'static' ? 'success' : 'info'">
                {{ getTypeText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="配置值" width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <span v-if="row.type === 'static'">{{ row.value || '-' }}</span>
              <span v-else>{{ row.routeParam || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="解析值" width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <span :class="{ 'text-muted': !row.enabled }">
                {{ getResolvedValue(row) }}
              </span>
            </template>
          </el-table-column>

          <el-table-column prop="enabled" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.enabled ? 'success' : 'info'">
                {{ getStatusText(row.enabled) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />

          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                style="min-width: 0"
                :icon="Edit"
                size="small"
                @click="openEditDialog(row)"
              />
              <el-button
                type="danger"
                style="min-width: 0"
                :icon="Delete"
                size="small"
                @click="deleteVariable(row)"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 变量表单对话框 -->
    <el-dialog
      v-model="showFormDialog"
      :title="editingVariable ? '编辑变量' : '添加变量'"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" size="default">
        <el-form-item label="变量名" prop="key">
          <el-input v-model="formData.key" placeholder="请输入变量名，如：apiToken" clearable />
          <div class="form-tip">变量名只能包含字母、数字和下划线，且不能以数字开头</div>
        </el-form-item>

        <el-form-item label="变量类型" prop="type">
          <el-radio-group v-model="formData.type">
            <el-radio label="static">静态值</el-radio>
            <el-radio label="route">路由参数</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="formData.type === 'static'" label="变量值" prop="value">
          <el-input v-model="formData.value" placeholder="请输入变量值" clearable />
          <div class="form-tip">静态值将直接作为变量的值使用</div>
        </el-form-item>

        <el-form-item v-if="formData.type === 'route'" label="路由参数" prop="routeParam">
          <el-input v-model="formData.routeParam" placeholder="请输入路由参数名" clearable>
            <template #append>
              <el-dropdown @command="selectCommonRouteParam">
                <el-button>
                  常用参数
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="param in COMMON_ROUTE_PARAMS"
                      :key="param.value"
                      :command="param.value"
                    >
                      {{ param.label }} - {{ param.description }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-input>
          <div class="form-tip">从当前页面URL中获取指定参数的值，如：id、token等</div>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="2"
            placeholder="请输入变量描述（可选）"
          />
        </el-form-item>

        <el-form-item label="启用状态">
          <el-switch v-model="formData.enabled" active-text="启用" inactive-text="禁用" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showFormDialog = false">取消</el-button>
          <el-button type="primary" :loading="loading" @click="saveVariable">
            {{ editingVariable ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="less">
.global-variable-manager {
  .manager-content {
    .toolbar {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
      padding: 12px;
      background-color: #f5f7fa;
      border-radius: 4px;

      .toolbar-info {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-left: auto;
        font-size: 12px;
        color: #606266;

        .el-icon {
          color: #409eff;
        }
      }
    }
  }

  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .text-muted {
    color: #c0c4cc;
  }

  .dialog-footer {
    text-align: right;
  }

  :deep(.el-table) {
    .el-button + .el-button {
      margin-left: 4px;
    }
  }

  :deep(.el-form-item__content) {
    .el-input-group__append {
      padding: 0;
    }
  }
}
</style>
