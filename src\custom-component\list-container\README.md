# 列表容器组件 (ListContainer)

## 概述

列表容器组件是一个功能强大的列表展示组件，支持4种不同的列表样式，可以通过配置项灵活切换，同时支持静态数据和REST接口数据源。

## 支持的列表样式

### 1. 通知公告样式 (notice)
- **适用场景**: 公告、通知、新闻等信息展示
- **特点**: 左侧图片 + 中间内容 + 右侧操作按钮
- **显示元素**: 标题、描述、时间、图片、操作按钮

### 2. 时间轴样式 (timeline)
- **适用场景**: 工作概览、历史记录、流程展示
- **特点**: 左侧时间线 + 右侧内容卡片
- **显示元素**: 时间节点、标题、描述

### 3. 任务列表样式 (task)
- **适用场景**: 待办事项、任务管理、工作流
- **特点**: 状态指示器 + 任务信息 + 优先级标签 + 操作按钮
- **显示元素**: 状态点、标题、描述、时间、优先级、操作按钮

### 4. 预警列表样式 (warning)
- **适用场景**: 系统预警、异常提醒、监控告警
- **特点**: 预警图片 + 预警信息 + 时间 + 操作按钮
- **显示元素**: 预警图片、标题、描述、时间、操作按钮

## 数据源支持

### 静态数据 (static)
直接在配置中定义数据项，适合固定内容的展示。

### REST接口 (rest)
从REST API获取数据，支持：
- GET/POST请求方法
- 自定义请求头
- 请求参数配置
- 字段映射配置
- 自动数据刷新

## 配置项说明

### 基础配置
- `listStyle`: 列表样式类型 (notice/timeline/task/warning)
- `dataSource`: 数据源类型 (static/rest)
- `title`: 列表标题
- `showHeader`: 是否显示头部
- `emptyText`: 空数据提示文本

### 显示配置
- `showIcon`: 是否显示图片
- `showDescription`: 是否显示描述
- `showTime`: 是否显示时间
- `showAction`: 是否显示操作按钮

### 图片配置
- `iconSize`: 图片大小 (像素)
- 支持的图片格式: jpg、png、gif
- 图片大小限制: 2MB以内
- 图片存储方式: base64编码存储在配置中

### 操作配置
- `actionType`: 操作类型 (button/icon/text)
- `actionText`: 操作按钮文本
- `actionIcon`: 操作图标
- `buttonType`: 按钮类型
- `buttonSize`: 按钮大小

### 边框配置
- `borderConfig`: 边框配置对象
  - `showContainerBorder`: 是否显示容器边框
  - `containerBorderColor`: 容器边框颜色
  - `containerBorderWidth`: 容器边框宽度 (1px/2px/3px/4px)
  - `containerBorderStyle`: 容器边框样式 (solid/dashed/dotted)
  - `showHeaderBorder`: 是否显示头部边框
  - `headerBorderColor`: 头部边框颜色
  - `headerBorderWidth`: 头部边框宽度
  - `headerBorderStyle`: 头部边框样式
  - `showItemBorder`: 是否显示列表项边框
  - `itemBorderColor`: 列表项边框颜色
  - `itemBorderWidth`: 列表项边框宽度
  - `itemBorderStyle`: 列表项边框样式

### REST配置
- `restConfig`: REST接口配置
  - `url`: 接口地址
  - `method`: 请求方法
  - `headers`: 请求头
  - `params`: 请求参数
- `fieldMapping`: 字段映射配置

## 使用示例

### 1. 通知公告列表
```javascript
{
  listStyle: 'notice',
  dataSource: 'static',
  title: '通知公告',
  showHeader: true,
  showIcon: true,
  showDescription: true,
  showTime: true,
  showAction: true,
  actionType: 'icon',
  iconSize: 24,
  items: [
    {
      title: '系统维护通知',
      description: '系统将于今晚进行维护',
      time: '2024-12-07',
      icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMSA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDMgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjNDA5RUZGIi8+Cjwvc3ZnPgo=',
      actionText: '查看'
    }
  ]
}
```

### 2. 时间轴列表
```javascript
{
  listStyle: 'timeline',
  dataSource: 'static',
  title: '工作概览',
  showHeader: true,
  showAction: false,
  items: [
    {
      title: '完成项目开发',
      description: '完成用户管理模块开发',
      time: '2024-06-21'
    }
  ]
}
```

### 3. 任务列表
```javascript
{
  listStyle: 'task',
  dataSource: 'static',
  title: '待办事项',
  showHeader: true,
  showAction: true,
  actionType: 'button',
  items: [
    {
      title: '修复登录bug',
      description: '解决用户登录超时问题',
      time: '2024-12-10',
      status: 'processing',
      priority: 'high',
      actionText: '处理'
    }
  ]
}
```

### 4. 预警列表
```javascript
{
  listStyle: 'warning',
  dataSource: 'static',
  title: '研判预警',
  showHeader: true,
  showAction: true,
  items: [
    {
      title: '设备异常预警',
      description: '设备运行异常，请及时处理',
      time: '2024-06-15 09:38:17',
      priority: 'high',
      actionText: '处理'
    }
  ]
}
```

### 5. REST接口数据源
```javascript
{
  listStyle: 'notice',
  dataSource: 'rest',
  restConfig: {
    url: '/api/notices',
    method: 'GET',
    headers: [
      { key: 'Content-Type', value: 'application/json' }
    ],
    params: [
      { key: 'page', value: '1' },
      { key: 'size', value: '10' }
    ]
  },
  fieldMapping: {
    title: 'noticeTitle',
    description: 'noticeContent',
    time: 'publishTime',
    actionText: 'actionText'
  }
}
```

### 6. 边框配置示例
```javascript
{
  listStyle: 'notice',
  dataSource: 'static',
  title: '自定义边框列表',
  borderConfig: {
    // 容器边框
    showContainerBorder: true,
    containerBorderColor: '#409eff',
    containerBorderWidth: '2px',
    containerBorderStyle: 'solid',

    // 头部边框
    showHeaderBorder: true,
    headerBorderColor: '#409eff',
    headerBorderWidth: '1px',
    headerBorderStyle: 'solid',

    // 列表项边框
    showItemBorder: true,
    itemBorderColor: '#e8e8e8',
    itemBorderWidth: '1px',
    itemBorderStyle: 'dashed'
  },
  items: [
    {
      title: '自定义边框示例',
      description: '这是一个自定义边框的列表项',
      time: '2024-12-07'
    }
  ]
}
```

## 样式定制

组件支持通过配置项自定义样式：
- `containerStyle`: 容器样式
- `headerStyle`: 头部样式
- `itemStyle`: 列表项样式
- `titleStyle`: 标题样式
- `descriptionStyle`: 描述样式
- `timeStyle`: 时间样式
- `iconStyle`: 图标样式
- `actionStyle`: 操作区样式

## 事件处理

- `handleItemClick`: 列表项点击事件
- `handleActionClick`: 操作按钮点击事件

## 图片上传功能

### 上传方式
1. **静态数据配置**: 在配置面板的"静态数据配置"中，每个列表项都可以单独上传图片
2. **支持格式**: jpg、png、gif
3. **大小限制**: 单个图片不超过2MB
4. **存储方式**: 图片会被转换为base64格式存储在配置中

### 使用步骤
1. 在配置面板中选择"静态数据配置"
2. 点击列表项的"上传图片"按钮
3. 选择本地图片文件
4. 图片会自动转换为base64并显示预览
5. 可以点击"删除"按钮移除图片

### 图片字段映射
- 对于REST数据源，可以在"字段映射配置"中设置图片字段名
- 图片字段应包含完整的图片URL或base64数据

## 注意事项

1. 使用REST数据源时，确保接口返回的数据格式正确
2. 字段映射配置要与实际数据字段名对应
3. 不同列表样式有不同的必需字段，请参考上述示例
4. 任务列表的状态字段支持：pending(待处理)、processing(处理中)、completed(已完成)、cancelled(已取消)
5. 优先级字段支持：high(高)、medium(中)、low(低)
6. 图片加载失败时会显示默认占位图
7. 建议图片尺寸保持一致以获得最佳显示效果

## 文件结构

```
src/custom-component/list-container/
├── Component.vue          # 主组件
├── Attr.vue             # 配置面板
├── test-data.js         # 测试数据
├── ListStyleDemo.vue    # 样式演示组件
└── README.md           # 说明文档
```
