<script setup lang="ts">
import { ref, computed, reactive, watch, onMounted, onBeforeUnmount } from 'vue'
// import { ElForm, ElFormItem, ElInput, ElInputNumber, ElSelect, ElOption, ElRadioGroup, ElRadio, ElCheckboxGroup, ElCheckbox, ElDatePicker, ElButton, ElMessage } from 'element-plus'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import { storeToRefs } from 'pinia'
import { callRestApi } from '@/utils/restApi'
import emitter from '@/utils/eventBus'
import type { FormConfig, FormField, FormData, FormValidationResult, FormSubmitResult } from './types'
import { DEFAULT_FORM_CONFIG, VALIDATION_PATTERNS } from './types'

const props = defineProps({
  propValue: {
    type: Object as () => FormConfig,
    default: () => ({ ...DEFAULT_FORM_CONFIG })
  },
  element: {
    type: Object,
    default: () => ({})
  },
  showPosition: {
    type: String,
    default: 'preview'
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['input'])

const dvMainStore = dvMainStoreWithOut()
const { editMode } = storeToRefs(dvMainStore)

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive<FormData>({})

// 表单验证错误
const formErrors = reactive<Record<string, string>>({})

// 表单验证状态
const validationStatus = reactive<Record<string, 'success' | 'error' | 'validating' | ''>>({})

// 表单是否已经尝试过提交（用于显示验证错误）
const hasSubmitAttempt = ref(false)

// 提交状态
const submitting = ref(false)

// 检测是否在弹框中（通过检查是否有弹框组件关联了当前表单）
const isInModal = ref(false)

// 计算属性
const config = computed(() => props.propValue || DEFAULT_FORM_CONFIG)

const visibleFields = computed(() => 
  config.value.fields
    .filter(field => field.visible !== false)
    .sort((a, b) => (a.order || 0) - (b.order || 0))
)

const formRules = computed(() => {
  const rules: Record<string, any[]> = {}
  
  config.value.fields.forEach(field => {
    const fieldRules: any[] = []
    
    if (field.required) {
      fieldRules.push({
        required: true,
        message: `请输入${field.label}`,
        trigger: ['blur', 'change']
      })
    }
    
    if (field.validation) {
      const validation = field.validation
      
      if (validation.min !== undefined) {
        fieldRules.push({
          min: validation.min,
          message: validation.message || `最小长度为${validation.min}`,
          trigger: 'blur'
        })
      }
      
      if (validation.max !== undefined) {
        fieldRules.push({
          max: validation.max,
          message: validation.message || `最大长度为${validation.max}`,
          trigger: 'blur'
        })
      }
      
      if (validation.pattern) {
        fieldRules.push({
          pattern: new RegExp(validation.pattern),
          message: validation.message || '格式不正确',
          trigger: 'blur'
        })
      }
    }
    
    // 内置验证规则
    if (field.type === 'email') {
      fieldRules.push({
        pattern: VALIDATION_PATTERNS.email,
        message: '请输入正确的邮箱地址',
        trigger: 'blur'
      })
    }
    
    if (fieldRules.length > 0) {
      rules[field.name] = fieldRules
    }
  })
  
  return rules
})

// 初始化表单数据
const initFormData = () => {
  config.value.fields.forEach(field => {
    if (field.defaultValue !== undefined) {
      formData[field.name] = field.defaultValue
    } else {
      // 根据字段类型设置默认值
      switch (field.type) {
        case 'checkbox':
          formData[field.name] = []
          break
        case 'number':
          formData[field.name] = null
          break
        default:
          formData[field.name] = ''
      }
    }
  })
}

// 验证表单
const validateForm = (): Promise<FormValidationResult> => {
  return new Promise((resolve) => {
    if (!formRef.value) {
      resolve({ valid: false, errors: {} })
      return
    }
    
    formRef.value.validate((valid: boolean, fields: any) => {
      const errors: Record<string, string> = {}
      
      if (!valid && fields) {
        Object.keys(fields).forEach(fieldName => {
          const fieldErrors = fields[fieldName]
          if (fieldErrors && fieldErrors.length > 0) {
            errors[fieldName] = fieldErrors[0].message
          }
        })
      }
      
      resolve({ valid, errors })
    })
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  initFormData()
  Object.keys(formErrors).forEach(key => {
    delete formErrors[key]
  })
}

// 提交表单
const submitForm = async () => {
  hasSubmitAttempt.value = true

  if (!config.value.submitConfig.enabled) {
    ElMessage.warning('表单提交功能未启用')
    return
  }

  if (!config.value.submitConfig.url) {
    ElMessage.error('请配置提交URL')
    return
  }

  try {
    submitting.value = true

    // 清除之前的错误状态
    Object.keys(formErrors).forEach(key => {
      delete formErrors[key]
    })
    Object.keys(validationStatus).forEach(key => {
      validationStatus[key] = 'validating'
    })

    // 验证表单
    const validation = await validateForm()
    if (!validation.valid) {
      Object.assign(formErrors, validation.errors)

      // 设置验证状态
      Object.keys(validation.errors).forEach(fieldName => {
        validationStatus[fieldName] = 'error'
      })

      // 找到第一个错误字段并滚动到该位置
      const firstErrorField = Object.keys(validation.errors)[0]
      if (firstErrorField) {
        const errorElement = document.querySelector(`[name="${firstErrorField}"]`)
        if (errorElement) {
          errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      }

      ElMessage.error('请检查表单输入')
      return
    }

    // 设置所有字段为成功状态
    config.value.fields.forEach(field => {
      validationStatus[field.name] = 'success'
    })

    // 准备提交数据
    const submitData = { ...formData }

    console.log('表单提交数据:', submitData)

    // 调用REST API
    const result = await callRestApi({
      url: config.value.submitConfig.url,
      method: config.value.submitConfig.method,
      headers: config.value.submitConfig.headers || [],
      params: config.value.submitConfig.params || [],
      body: JSON.stringify(submitData),
      timeout: config.value.submitConfig.timeout || 30000
    }, {
      logPrefix: '表单提交',
      resolveVariables: true
    })

    console.log('表单提交成功:', result)

    // 处理成功响应
    ElMessage.success(config.value.submitConfig.successMessage || '提交成功')

    // 重置表单（如果配置了）
    if (config.value.submitConfig.resetAfterSubmit) {
      resetForm()
      hasSubmitAttempt.value = false
      // 清除验证状态
      Object.keys(validationStatus).forEach(key => {
        validationStatus[key] = ''
      })
    }

    // 通知弹框提交成功（如果在弹框中）
    if (isInModal.value) {
      emitter.emit(`form-submit-success-${props.element.id}`)
    }

    // 刷新目标组件（如果配置了）
    if (config.value.submitConfig.refreshTargetComponents && config.value.submitConfig.refreshTargetComponents.length > 0) {
      config.value.submitConfig.refreshTargetComponents.forEach(targetId => {
        console.log(`表单提交成功，触发目标组件 ${targetId} 重新查询数据`)
        emitter.emit(`query-data-${targetId}`)
      })
    }

    // 跳转（如果配置了）
    if (config.value.submitConfig.redirectUrl) {
      const resolvedUrl = dvMainStore.resolveVariableReferences(config.value.submitConfig.redirectUrl)
      window.open(resolvedUrl, '_blank')
    }

  } catch (error) {
    console.error('表单提交失败:', error)

    // 设置所有字段为错误状态
    config.value.fields.forEach(field => {
      validationStatus[field.name] = 'error'
    })

    let errorMessage = config.value.submitConfig.errorMessage || '提交失败'

    // 根据错误类型提供更具体的错误信息
    if (error instanceof Error) {
      if (error.message.includes('fetch')) {
        errorMessage = '网络连接失败，请检查网络设置'
      } else if (error.message.includes('timeout')) {
        errorMessage = '请求超时，请稍后重试'
      } else if (error.message.includes('400')) {
        errorMessage = '请求参数错误，请检查表单数据'
      } else if (error.message.includes('401')) {
        errorMessage = '身份验证失败，请重新登录'
      } else if (error.message.includes('403')) {
        errorMessage = '没有权限执行此操作'
      } else if (error.message.includes('404')) {
        errorMessage = '提交地址不存在，请检查配置'
      } else if (error.message.includes('500')) {
        errorMessage = '服务器内部错误，请联系管理员'
      }
    }

    ElMessage.error(errorMessage)
  } finally {
    submitting.value = false
  }
}

// 监听配置变化
watch(() => config.value, () => {
  initFormData()
}, { deep: true })

// 组件挂载时初始化
onMounted(() => {
  initFormData()

  // 检测是否在弹框中
  checkIfInModal()

  // 监听弹框触发的提交和重置事件
  emitter.on(`form-submit-${props.element.id}`, handleModalSubmit)
  emitter.on(`form-reset-${props.element.id}`, handleModalReset)
})

// 组件卸载时清理事件监听
onBeforeUnmount(() => {
  emitter.off(`form-submit-${props.element.id}`, handleModalSubmit)
  emitter.off(`form-reset-${props.element.id}`, handleModalReset)
})

// 检测是否在弹框中
const checkIfInModal = () => {
  // 通过检查是否有弹框组件关联了当前表单来判断
  const { componentData } = storeToRefs(dvMainStore)
  const modalComponents = componentData.value.filter(comp => comp.component === 'ModalDialog')

  isInModal.value = modalComponents.some(modal =>
    modal.propValue?.linkedComponentId === props.element.id
  )


}

// 处理弹框触发的提交
const handleModalSubmit = () => {
  submitForm()
}

// 处理弹框触发的重置
const handleModalReset = () => {
  resetForm()
}

// 处理输入变化
const handleInput = () => {
  emit('input', props.element, config.value)
}

// 实时验证单个字段
const validateSingleField = async (fieldName: string) => {
  if (!formRef.value || !hasSubmitAttempt.value) return

  try {
    await formRef.value.validateField(fieldName)
    validationStatus[fieldName] = 'success'
    delete formErrors[fieldName]
  } catch (error: any) {
    validationStatus[fieldName] = 'error'
    if (error && error.message) {
      formErrors[fieldName] = error.message
    }
  }
}

// 处理字段值变化
const handleFieldChange = (fieldName: string) => {
  handleInput()
  // 延迟验证，避免用户输入时频繁提示错误
  setTimeout(() => {
    validateSingleField(fieldName)
  }, 300)
}

// 处理字段失焦
const handleFieldBlur = (fieldName: string) => {
  if (hasSubmitAttempt.value) {
    validateSingleField(fieldName)
  }
}
</script>

<template>
  <div class="form-control-wrapper">
    <!-- 表单标题和描述 -->
    <div v-if="config.title || config.description" class="form-header">
      <h3 v-if="config.title" class="form-title">{{ config.title }}</h3>
      <p v-if="config.description" class="form-description">{{ config.description }}</p>
    </div>
    
    <!-- 表单内容 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-position="config.layout.labelPosition"
      :label-width="config.layout.labelWidth"
      :size="config.layout.size"
      class="form-control-form"
      :class="{ 'form-with-border': config.layout.showBorder }"
    >
      <div 
        class="form-fields-grid"
        :style="{ 
          gridTemplateColumns: `repeat(${config.layout.columns}, 1fr)`,
          gap: `${config.layout.spacing}px`
        }"
      >
        <el-form-item
          v-for="field in visibleFields"
          :key="field.id"
          :label="field.label"
          :prop="field.name"
          :required="field.required"
          class="form-field-item"
        >
          <!-- 文本输入框 -->
          <el-input
            v-if="field.type === 'text' || field.type === 'email' || field.type === 'password'"
            v-model="formData[field.name]"
            :name="field.name"
            :type="field.type"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :readonly="field.readonly"
            :validate-event="false"
            @input="() => handleFieldChange(field.name)"
            @blur="() => handleFieldBlur(field.name)"
          />

          <!-- 多行文本 -->
          <el-input
            v-else-if="field.type === 'textarea'"
            v-model="formData[field.name]"
            :name="field.name"
            type="textarea"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :readonly="field.readonly"
            :rows="4"
            :validate-event="false"
            @input="() => handleFieldChange(field.name)"
            @blur="() => handleFieldBlur(field.name)"
          />

          <!-- 数字输入框 -->
          <el-input-number
            v-else-if="field.type === 'number'"
            v-model="formData[field.name]"
            :name="field.name"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :min="field.validation?.min"
            :max="field.validation?.max"
            style="width: 100%"
            :validate-event="false"
            @change="() => handleFieldChange(field.name)"
            @blur="() => handleFieldBlur(field.name)"
          />

          <!-- 下拉选择 -->
          <el-select
            v-else-if="field.type === 'select'"
            v-model="formData[field.name]"
            :name="field.name"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            style="width: 100%"
            :validate-event="false"
            @change="() => handleFieldChange(field.name)"
            @blur="() => handleFieldBlur(field.name)"
          >
            <el-option
              v-for="option in field.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="option.disabled"
            />
          </el-select>

          <!-- 单选按钮组 -->
          <el-radio-group
            v-else-if="field.type === 'radio'"
            v-model="formData[field.name]"
            :name="field.name"
            :disabled="field.disabled"
            @change="() => handleFieldChange(field.name)"
          >
            <el-radio
              v-for="option in field.options"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>

          <!-- 复选框组 -->
          <el-checkbox-group
            v-else-if="field.type === 'checkbox'"
            v-model="formData[field.name]"
            :name="field.name"
            :disabled="field.disabled"
            @change="() => handleFieldChange(field.name)"
          >
            <el-checkbox
              v-for="option in field.options"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>

          <!-- 日期选择器 -->
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="formData[field.name]"
            :name="field.name"
            type="date"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            style="width: 100%"
            :validate-event="false"
            @change="() => handleFieldChange(field.name)"
            @blur="() => handleFieldBlur(field.name)"
          />

          <!-- 字段错误提示 -->
          <div
            v-if="hasSubmitAttempt && formErrors[field.name]"
            class="field-error-message"
          >
            {{ formErrors[field.name] }}
          </div>
        </el-form-item>
      </div>
      
      <!-- 表单按钮 - 如果在弹框中则隐藏，由弹框统一控制 -->
      <div v-if="!isEdit && !isInModal && (config.showSubmitButton || config.showResetButton)" class="form-buttons">
        <el-button
          v-if="config.showSubmitButton"
          type="primary"
          :loading="submitting"
          @click="submitForm"
        >
          {{ config.submitButtonText }}
        </el-button>
        
        <el-button
          v-if="config.showResetButton"
          @click="resetForm"
        >
          {{ config.resetButtonText }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<style lang="less" scoped>
.form-control-wrapper {
  width: 100%;
  padding: 16px;
  
  .form-header {
    margin-bottom: 20px;
    
    .form-title {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
    
    .form-description {
      margin: 0;
      font-size: 14px;
      color: #606266;
      line-height: 1.5;
    }
  }
  
  .form-control-form {
    &.form-with-border {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      padding: 20px;
    }
    
    .form-fields-grid {
      display: grid;
    }
    
    .form-field-item {
      margin-bottom: 0;

      .field-error-message {
        margin-top: 4px;
        font-size: 12px;
        color: #f56c6c;
        line-height: 1.4;
      }
    }
    
    .form-buttons {
      margin-top: 24px;
      text-align: center;
      
      .el-button + .el-button {
        margin-left: 12px;
      }
    }
  }
}

// 编辑模式样式
.form-control-wrapper {
  &:hover {
    border: 1px dashed #409eff;
  }
}

// 主题适配
.form-control-wrapper {
  // 深色主题适配
  :deep(.el-form-item__label) {
    color: inherit;
  }

  :deep(.el-input__inner),
  :deep(.el-textarea__inner),
  :deep(.el-select .el-input__inner) {
    background-color: transparent;
    border-color: rgba(255, 255, 255, 0.1);
    color: inherit;

    &:focus {
      border-color: #409eff;
    }
  }

  :deep(.el-radio__label),
  :deep(.el-checkbox__label) {
    color: inherit;
  }

  // 错误状态样式
  :deep(.el-form-item.is-error .el-input__inner),
  :deep(.el-form-item.is-error .el-textarea__inner) {
    border-color: #f56c6c;
  }
}
</style>
