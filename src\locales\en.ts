export default {
  common: {
    empty: ' ',
    first_item: 'First Item',
    cross_source: 'Cross-source',
    single_source: 'Single-source',
    source_tips:
      'The data set is cross-source. Please check the syntax of other SQL nodes to confirm whether the type should be changed to single-source?',
    associated_chart: 'Associated chart',
    associated_chart_first: 'First level associated chart',
    changing_the_display:
      'Changing the display type will clear the drop-down tree related settings. Are you sure?',
    other_levels:
      'Except for level 1, no query condition configuration is required for other levels',
    tree_structure: 'The drop-down tree structure cannot be empty',
    component: {
      input: 'Input',
      textarea: 'Textarea',
      select: 'Select',
      radio: 'Radio',
      checkbox: 'Checkbox',
      date: 'Date Picker',
      dateRange: 'Date Range Picker',
      add_component_hint: 'Click or drag the component on the left to add a field'
    },
    local_excel: 'Local Excel/CSV',
    remote_excel: 'Remote Excel/CSV',
    list_selection: 'List selection',
    date_setting: 'Date setting',
    openMobileTerminal: 'Open mobile terminal',
    watermarkManagement: 'Watermark Management',
    inputText: 'Please enter',
    selectText: 'Please select',
    add: 'Add',
    account: 'Account',
    email: 'Email',
    phone: 'Phone',
    pwd: 'Password',
    require: 'Required',
    personal_info: 'Personal Info',
    about: 'About',
    exit_system: 'Log Out',
    letter_start: 'Must start with a letter',
    required: 'Required',
    operate: 'Operation',
    create_time: 'Creation time',
    edit: 'Edit',
    delete: 'Delete',
    please_input: 'Please input ',
    please_select: 'Please select ',
    cancel: 'Cancel',
    sure: 'Confirm',
    save: 'Save',
    input_limit: 'Length is {0} to {1} characters',
    save_success: 'Save successfully',
    roger_that: 'Got it',
    delete_success: 'Delete successfully',
    copy: 'Copy',
    operating: 'Operation',
    label: 'Notes',
    search_keywords: 'Enter keywords to search',
    detail: 'Details',
    prev: 'Previous step',
    description: 'Description',
    next: 'Next step',
    name: 'Name',
    input_name: 'Please enter a name',
    yes: 'Yes',
    no: 'No',
    every: 'Every',
    minute: 'Minute',
    second: 'Second',
    hour: 'Hour',
    day: 'Day',
    every_exec: 'Execute once',
    cron_exp: 'Cron expression',
    copy_success: 'Copy successful',
    copy_unsupported: 'Your browser does not support copying',
    filter: 'Filter',
    filter_condition: 'Filter condition',
    no_auth_tips: 'Missing menu permissions, please contact the administrator',
    no_menu_tips: 'Resource not found 401 error'
  },
  toolbox: {
    name: 'Toolbox',
    template_center: 'Template center',
    org_center: 'Organization Management Center'
  },
  api_pagination: {
    help_documentation: 'Doc',
    product_forum: 'Forum',
    technical_blog: 'Blog',
    enterprise_edition_trial: 'Trial',
    paging_ettings: 'Paging Settings',
    parameter_name: 'Parameter name',
    built_in_parameter_name: 'Built-in parameter name',
    request_parameter_name: 'Request parameter name',
    parameter_default_value: 'Parameter default value',
    parsing_path: 'Parsing path',
    total_number: 'Total number',
    total_number_de: 'Total number de',
    number_of_pages: 'Total number of pages',
    number__size: 'Page number + size',
    cursor__size: 'Cursor + size',
    page_number: 'Page number',
    pagination_size: 'Pagination size',
    cursor: 'Cursor',
    pagination_method: 'Pagination method',
    response: 'Response',
    please_enter_jsonpath: 'Please enter JsonPath',
    enter_parameter_name: 'Please enter parameter name',
    enter_default_value: 'Please enter default value',
    enter_first_page: 'Please enter the page number of the first page'
  },
  operate_log: {
    name: 'Operation log',
    search_by_operate_info: 'Search by operation target',
    detail: 'Operation details',
    type: 'Operation type',
    status: 'Operation status',
    success: 'Success',
    failed: 'Failure',
    user: 'Operation user',
    time: 'Operation time',
    ip: 'IP address',
    organization: 'Organization',
    error_msg: 'Error message',
    confirm_export: 'Are you sure you want to export the log',
    export_success: 'Export successful',
    excel_file_name: 'DataEase operation log',
    relevant_content_found: 'No relevant content found',
    mobile: 'Mobile terminal',
    client: 'Client'
  },
  data_set: {
    validation_succeeded: 'Field expression validation succeeded',
    to_nth_digits: 'Retain the Mth to Nth digits',
    the_column_permissions: 'Are you sure you want to delete column permissions?',
    last_n_digits: 'Retain the first M digits and the last N digits',
    rule_preview: 'Rule preview',
    prohibit_viewing: 'Prohibit viewing',
    set_desensitization_rules: 'Set desensitization rules',
    the_row_permissions: 'Are you sure you want to delete the row permissions?',
    ten_wan: '100,000',
    can_go_to: 'Backend export in progress, can go to',
    progress_and_download: 'Check progress and download',
    this_data_set: 'Are you sure you want to delete this dataset? ',
    to_delete_them:
      'This dataset has the following blood relationship. Deleting it will invalidate the charts on the related dashboard. Are you sure you want to delete it? ',
    check_blood_relationship: 'Check blood relationship',
    dataset_export: 'Dataset export',
    pieces_of_data: 'Tip: Supports exporting up to {limit} pieces of data',
    enter_parameter_name: 'Please enter the parameter name',
    enter_1_50_characters: 'Please enter 1-50 characters',
    parameter_default_value: 'Please enter the default value of the parameter',
    edit_calculation_parameters: 'Edit calculation parameters',
    add_calculation_parameters: 'Add calculation parameters',
    parameter_is_supported: 'Only one calculation parameter can be added. ',
    enter_a_number: 'Please enter a number',
    parameter_name: 'Parameter name',
    parameter_default_value_de: 'Parameter default value',
    confirm_the_deletion:
      'Not checked, the associated calculated fields will be deleted, confirm the deletion? ',
    confirm_to_delete: 'Are you sure you want to delete {a}?',
    also_be_deleted:
      'After deletion, the associated table or sql fragment will be deleted, and the associated calculated fields will also be deleted. ',
    deleted_confirm_deletion:
      'If the field is deleted, the associated calculated fields will be deleted, confirm the deletion? ',
    delete_field_a: 'Are you sure you want to delete field {a}?',
    field_name: 'Field name',
    field_type: 'Field type',
    field_notes: 'Field notes',
    operate_with_caution:
      'After deletion, all resources under this folder will be deleted, please operate with caution. ',
    delete_this_folder: 'Are you sure you want to delete this folder?',
    a_new_dataset: 'Create a new dataset',
    structure_preview: 'Structure preview',
    data_set_yet: 'No dataset yet',
    new_data_screen: 'Create a new data screen',
    pieces_in_total: 'Show 100 pieces of data, a total of {msg} pieces',
    no_data: 'No data yet',
    no_tasks_yet: 'No tasks yet',
    exporting: 'Exporting',
    success: 'Success',
    fail: 'Failure',
    waiting: 'Waiting',
    all: 'All',
    successful_go_to: 'Export successful, go to',
    failed_go_to: 'Export failed, go to',
    data_set: 'Dataset',
    view: 'Chart',
    organization: 'Organization',
    download: 'Download',
    re_export: 'Re-export',
    delete: 'Delete',
    reason_for_failure: 'Reason for failure',
    closure: 'Close',
    cannot_be_empty: 'SQL name cannot be empty',
    cannot_be_empty_de: 'SQL cannot be empty',
    sure_to_exit: 'The information you filled in is not saved, are you sure to exit? ',
    copied_successfully: 'Copied successfully',
    not_support_copying: 'Your browser does not support copying:',
    parameters_set_successfully: 'Parameters set successfully',
    run: 'Run',
    parameter_settings: 'Parameter settings',
    save: 'Save',
    current_data_source: 'Current Datasource',
    relevant_content_found: 'No relevant content found',
    physical_field_name: 'Physical field name',
    click_above: 'Click above',
    see_the_results: 'Run to see the results',
    a_folder_name: 'Please enter the folder name',
    the_dataset_name: 'Please enter the dataset name',
    the_destination_folder: 'Please select the destination folder',
    moved_successfully: 'Moved successfully',
    rename_successful: 'Renamed successfully',
    field: 'Field',
    want_to_continue:
      'Not selected, its related newly created fields will be deleted, do you want to continue? ',
    field_selection: 'Field selection',
    edit_sql: 'Edit SQL',
    custom_sql_here: 'Drag table or custom SQL here',
    on_the_left: 'Drag the data table and custom SQL on the left',
    a_data_set: 'Drag here to create a Dataset',
    rename_table: 'Rename table',
    table_name: 'Table name',
    table_name_de: 'Table name',
    table_remarks: 'Table remarks',
    customize: 'Customize',
    change_field_type: 'Change field type',
    text: 'Text',
    time: 'Time',
    geographical_location: 'Geographical location',
    numerical_value: 'Numerical value',
    numeric_value_decimal: 'Numerical value (decimal)',
    edit: 'Edit',
    rename: 'Rename',
    copy: 'Copy',
    unnamed_dataset: 'Unnamed dataset',
    cannot_be_empty_time: 'Custom time format cannot be empty',
    custom_sql: 'Custom SQL',
    want_to_exit: 'Current changes have not been saved, are you sure you want to exit?',
    saved_successfully: 'Save successfully',
    cannot_be_empty_de_: 'Expression cannot be empty!',
    copy_a_dataset: 'Copy dataset',
    cannot_be_empty_de_field: 'Related field cannot be empty!',
    dataset_cannot_be: 'Dataset cannot be empty',
    save_and_return: 'Save and return',
    select_data_source: 'Select Datasource',
    invalid_data_source: 'Invalid Datasource',
    be_reported_incorrectly:
      "You are doing cross-data source table association, please make sure to use calcite's standard syntax and functions,otherwise it will cause dataset errors",
    refresh_data: 'Refresh data',
    convert_to_indicator: 'Convert to indicator',
    convert_to_dimension: 'Convert to dimension',
    selected: 'Selected',
    bar: 'Bar',
    format_edit: 'Format edit',
    custom_time_format: 'Custom time format',
    cannot_be_empty_: 'Filter field cannot be empty',
    cannot_be_empty_de_ruler: 'Rule condition cannot be empty'
  },
  data_source: {
    successfully_created: 'Created successfully',
    continue_to_create: 'Continue to create',
    data_source_list: 'Return to Datasource list',
    prompts_next_time: "Don't prompt next time",
    also_want_to: 'You may also want',
    or_large_screen: 'Prepare for the next dashboard or data screen',
    go_to_create: 'Go to create',
    verification_successful: 'Verification successful',
    verification_failed: 'Verification failed',
    create_successfully: 'Created successfully',
    operate_with_caution:
      'After deletion, all resources under this folder will be deleted. Please operate with caution.',
    confirm_to_delete:
      'A dataset is using this Datasource. After deletion, the dataset will be unavailable. Confirm the deletion? ',
    view_blood_relationship: 'View blood relationship',
    no_data_source: 'No Datasource',
    replace_data: 'Replace data',
    append_data: 'Append data',
    latest_update_status: 'Latest update status',
    latest_update_time: 'Latest update time',
    data_time: 'Data time:',
    update_all: 'Update all',
    on_the_left: 'Please select a Datasource on the left',
    update_result: 'Update result',
    failure_details: 'Failure details',
    the_request_address: 'Please enter the request address',
    name_already_exists: 'Parameter with the same name already exists:',
    name_already_exists_de: 'Parameter table with the same name already exists',
    interface_parameters: 'Interface parameters',
    extract_parameters: 'Extract parameters',
    view_data_structure: 'View data structure',
    the_data_structure: 'No data yet, please check the fields in the data structure',
    parameter: 'Parameter',
    page_parameter: 'Page Parameter',
    fixed_value: 'Fixed value',
    time_function: 'Time function',
    customize: 'Customize',
    that_day: 'That day',
    value: 'Value',
    name_use_parameters: "You can use ${'{'}parameter name{'}'}, use parameters",
    add_parameters: 'Add parameters',
    data_source_name: 'Datasource name',
    data_source_name_de: 'Please enter the Datasource name',
    a_folder_name: 'Please enter the folder name',
    data_source: 'Datasource',
    the_destination_folder: 'Please select the destination folder',
    relevant_content_found: 'No relevant content found',
    cannot_be_empty: 'SSH host cannot be empty',
    cannot_be_empty_de: 'SSH port cannot be empty',
    cannot_be_empty_de_name: 'SSH username cannot be empty',
    cannot_be_empty_de_pwd: 'SSH password cannot be empty',
    cannot_be_empty_de_key: 'SSH key cannot be empty',
    to_64_characters: 'Parameter name limit 2 to 64 characters',
    to_64_characters_de: 'Interface name limit 2 to 64 characters',
    sure_to_delete: 'Are you sure you want to delete?',
    delete: 'Delete',
    source_configuration_information: 'Datasource configuration information',
    data_update_settings: 'Data update settings',
    connection_method: 'Connection method',
    hostname: 'Host name',
    jdbc_connection: 'JDBC connection',
    jdbc_connection_string: 'JDBC connection string',
    ssh_settings: 'SSH settings',
    enable_ssh: 'Enable SSH',
    host: 'Host',
    please_enter_hostname: 'Please enter the host name',
    port: 'Port',
    password: 'Password',
    enter_ssh_key: 'Please enter the SSH key',
    ssh_key_password: 'SSH key password',
    update_now: 'Update now',
    update_once: 'Update once',
    edit_parameters: 'Edit parameter',
    enter_parameter_name: 'Please enter the parameter name',
    text: 'Text',
    numerical_value: 'Numerical value',
    numeric_value_decimal: 'Numerical value (decimal)',
    rename: 'Rename',
    interface_name: 'Interface name',
    the_interface_name: 'Please enter the interface name',
    to_replace_it:
      'Replacement may affect custom datasets, associated datasets, dashboards, etc. Do you want to replace it? ',
    document: 'File',
    reupload: 'Reupload',
    and_csv_formats: 'Only xlsx, xls, csv formats are supported',
    please_upload_files: 'Please upload files',
    cannot_be_empty_table: 'The data table cannot be empty',
    the_previous_step:
      'The information you filled in will be cleared. Are you sure you want to return to the previous step? ',
    add_data_table: 'Need to add a data table',
    source_saved_successfully: 'Save Datasource successfully',
    copy_data_source: 'Copy Datasource',
    create_data_source: 'Create Datasource',
    want_to_exit: 'Current changes have not been saved, are you sure you want to exit?',
    configuration_information: 'Configuration information',
    recently_created: 'Recently created',
    all: 'All',
    api_data: 'API data'
  },
  dynamic_time: {
    set_default: 'Default value',
    fix: 'Fixed time',
    dynamic: 'Dynamic time',
    relative: 'Relative to current',
    today: 'Today',
    yesterday: 'Yesterday',
    firstOfMonth: 'Beginning of the month',
    endOfMonth: 'End of the month',
    firstOfYear: 'Beginning of the year',
    custom: 'Custom',
    date: 'Day',
    week: 'Week',
    month: 'Month',
    year: 'Year',
    before: 'Before',
    after: 'After',
    preview: 'Preview',
    set: 'Set',
    cweek: 'This week',
    lweek: 'Last week',
    cmonth: 'This month',
    cquarter: 'This quarter',
    tquarter: 'This quarter',
    lquarter: 'Last quarter',
    cyear: 'This year'
  },
  dynamic_year: {
    fix: 'Fixed year',
    dynamic: 'Dynamic year',
    current: 'This year',
    last: 'Last year'
  },
  dynamic_month: {
    fix: 'Fixed year and month',
    dynamic: 'Dynamic year and month',
    current: 'This month',
    last: 'Last month',
    firstOfYear: 'First month of the current year',
    sameMonthLastYear: 'Same month last year'
  },
  data_export: {
    export_center: 'Data Export Center',
    export_info: 'Check progress and download',
    exporting: 'Background export in progress, you can go',
    del_all: 'Delete all',
    export_failed: 'Export failed',
    export_from: 'Export source',
    export_obj: 'Export object',
    export_time: 'Export time',
    sure_del_all: 'Are you sure to delete all export records? ',
    sure_del: 'Are you sure to delete this export record? ',
    no_failed_file: 'No failed file',
    no_file: 'No file',
    no_task: 'No task',
    download_all: 'Download all',
    download: 'Download'
  },
  driver: {
    driver: 'Driver',
    please_choose_driver: 'Please select a driver',
    mgm: 'Driver management',
    exit_mgm: 'Exit driver management',
    add: 'Add driver',
    modify: 'Modify',
    show_info: 'Driver information',
    file_name: 'File name',
    version: 'Version',
    please_set_driverClass: 'Please specify the driver class',
    please_set_supportVersions: 'Please enter the supported database major version',
    supportVersions: 'Supported version'
  },
  login: {
    welcome: 'Welcome to use',
    btn: 'Login',
    username_format: "1-25 alphanumeric characters or ._-:{'@'} and start with a letter or number",
    pwd_format: 'Password length is 5-15',
    default_login: 'Default',
    ldap_login: 'LDAP Login',
    account_login: 'Account Login',
    other_login: 'Other login methods',
    pwd_invalid_error:
      'The password has expired. Please contact the administrator to modify or reset it',
    pwd_exp_tips:
      'Password will expire in {0} days, please change your password as soon as possible',
    qr_code: 'QR Code',
    platform_disable: '{0} setting disabled!',
    input_account: 'Please enter account number',
    redirect_2_auth: 'Redirecting to {0} authentication, {1} seconds...'
  },
  component: {
    columnList: 'List item',
    selectInfo: 'Please select the information to be displayed in the list',
    allSelect: 'Select all'
  },
  system: {
    user: 'User',
    role: 'Role',
    addUser: '@:common.add @:system.user',
    click_to_show: 'Click to show',
    click_to_hide: 'Click to hide',
    basic_settings: 'Basic settings',
    login_settings: 'Login settings',
    and_0_seconds: '0 minutes and 0 seconds',
    time_0_seconds: 'Minutes (execution time: 0 seconds)',
    and_0_seconds_de: 'Hours (execution time: 0 minutes and 0 seconds)',
    fonts_before_deleting: 'Please set other fonts as default fonts before deleting. ',
    sure_to_delete:
      'After the current font is deleted, the components using this font will use the default font. Are you sure to delete it?',
    setting_successful: 'Setting successful',
    font_management: 'Font Management',
    search_font_name: 'Search font name',
    a_new_font: 'Create a new font',
    add_font: 'Add font',
    default_font: 'Default font',
    system_built_in: 'System built-in',
    update_time: 'Update time:',
    font_file: 'Font file:',
    upload_font_file: 'Upload font file',
    replace_font_file: 'Replace font file',
    as_default_font: 'Set as default font',
    the_font_name: 'Please enter the font name',
    in_ttf_format: 'Only supports uploading font files in ttf format!',
    character_length_1_50: 'Character length 1-50',
    upload_font_file_de: 'Please upload font file',
    font_name: 'Font name',
    font_file_de: 'Font file',
    be_the_same: 'Old and new passwords cannot be the same',
    twice_are_inconsistent: 'The passwords entered twice are inconsistent',
    log_in_again: 'Change successfully, please log in again',
    original_password: 'Original password',
    the_original_password: 'Please enter the original password',
    new_password: 'New password',
    the_new_password: 'Please enter the new password',
    confirm_password: 'Confirm password',
    the_confirmation_password: 'Please enter the confirmation password',
    map_settings: 'Map settings',
    engine_settings: 'Engine settings',
    normal_login: 'Normal login',
    to_take_effect:
      'Request timeout (unit: seconds, note: refresh the browser after saving to take effect)',
    and_platform_docking: 'Scope includes authentication settings and platform docking',
    not_enabled: 'Disabled',
    default_organization: 'Default organization',
    normal_role: 'Normal role',
    engine_type: 'Engine type',
    on_the_left: 'Please select a region on the left',
    region_code: 'Region code',
    superior_region: 'Superior region',
    coordinate_file: 'Coordinate file',
    delete_this_node: 'Are you sure you want to delete this node',
    at_the_end:
      'Country codes consist of three digits, province, city, district, county, and township codes consist of two digits; non-national regions need to be followed by 0',
    non_zero_three_digit_number: 'Please enter a non-zero three-digit number',
    or_11_digits: 'Please enter 9 or 11 digits',
    contact_the_administrator: 'If execution fails, please contact the administrator',
    upload_json_files: 'Only json files can be uploaded',
    maximum_upload_200m: 'Maximum upload size 200M',
    geographic_information: 'Geographic information',
    superior_region_first: 'Please select the superior region first',
    region_name: 'Region name',
    appearance_configuration: 'Appearance Configuration',
    platform_display_theme: 'Platform display theme',
    navigation_background_color: 'Top navigation background color',
    dark_color: 'Dark color',
    light_color: 'Light color',
    theme_color: 'Theme color',
    default_blue: 'Default (blue)',
    custom_color_value: 'Custom color value',
    platform_login_settings: 'Platform login settings',
    page_preview: 'Page preview',
    restore_default: 'Restore default',
    platform: 'Default is {msg} platform interface, supports custom settings',
    supports_custom_settings: 'Default is {msg} login interface, supports custom settings',
    replace_image: 'Replace image',
    website_name: 'Website name',
    web_page_tab: 'Platform name displayed on web page tab',
    under_product_logo: 'Slogan under product logo',
    footer: 'Footer',
    footer_content: 'Footer content',
    platform_settings: 'Platform settings',
    top_navigation_logo: 'Top navigation logo',
    not_exceeding_200kb:
      'Logo displayed in the top navigation menu; recommended size 134 x 34, supports JPG, PNG, size not exceeding 200KB',
    help_document: 'Help document',
    ai_assistant_button: 'AI assistant button',
    copilot_button: 'Copilot button',
    document_button: 'Document button',
    about_button: 'About button',
    mobile_login_settings: 'Mobile login settings',
    user_login: 'User login',
    in_user_name: 'Please fill in the user name',
    fill_in_password: 'Please fill in the password',
    supports_custom_settings_de:
      'Default is {msg} Mobile login interface, supports custom settings',
    login_logo: 'Login Logo',
    not_exceeding_200kb_de:
      'Login page right side logo, recommended size 120*30, support JPG, PNG, SVG, size not exceeding 200KB',
    login_background_image: 'Login background image',
    not_exceeding_5m:
      'Left side background image, recommended size 375*480 for vector image, recommended size 1125*1440 for bitmap; support JPG, PNG, SVG, size not exceeding 5M',
    hidden_in_iframe: 'Hidden in Iframe',
    available_to_everyone: 'Open source BI tool available to everyone',
    the_website_name: 'Please enter the website name',
    enter_the_slogan: 'Please enter the slogan',
    the_help_document: 'Please enter the help document',
    assistant: 'Please choose whether to display the AI ​​assistant',
    to_display_copilot: 'Please select whether to display Copilot',
    display_the_document: 'Please select whether to display the document',
    display_the_about: 'Please select whether to display the about',
    website_logo: 'Website Logo',
    not_exceeding_200kb_de_:
      'The Logo displayed on the top website, recommended size 48 x 48, support JPG, PNG, SVG, size not exceeding 200KB',
    not_exceeding_200kb_de_right:
      'Logo on the right side of the login page, recommended size 204 x 52, support JPG, PNG, SVG, size not exceeding 200KB',
    not_exceeding_5m_de:
      'Background image on the left, recommended size 640 x 900 for vector graphics, 1280 x 1800 for bitmap graphics; support JPG, PNG, SVG, size not exceeding 5M',
    tab: 'Tab',
    incorrect_please_re_enter: 'The callback domain name format is incorrect, please re-enter',
    cas_settings: 'CAS settings',
    callback_domain_name: 'Callback domain name',
    authentication_settings: 'Authentication Settings',
    be_turned_on: 'After the test connection is valid, it can be turned on',
    platform_information_first: 'Please save the platform information first',
    for_example: 'Such as: {\'{\'}"account":"uid","name":"cn","email":"mail"{\'}\'}',
    in_json_format: 'Please enter json format',
    ldap_settings: 'LDAP settings',
    ldap_address: 'LDAP address',
    such_as_ldap: 'LDAP address (such as ldap://127.0.0.1:389)',
    bind_dn: 'Bind DN',
    user_ou: 'User OU',
    separate_each_ou: 'OU (use | to separate each OU)',
    user_filter: 'User filter',
    such_as_uid:
      "Filter [Possible options are cn or uid or sAMAccountName={'{'}0{'}'}, such as: (uid={'{'}0{'}'})]",
    ldap_attribute_mapping: 'LDAP attribute mapping',
    incorrect_please_re_enter_de: 'URL format error, please re-enter',
    oauth2_settings: 'OAuth2 settings',
    authorization_end_address: 'Authorization end address',
    token_end_address: 'Token end address',
    information_end_address: 'User information end address',
    connection_range: 'Connection range',
    client_id: 'Client ID',
    client_key: 'Client key',
    callback_address: 'Callback address',
    field_mapping: 'Field Mapping',
    oauth2name:
      'For example: {\'{\'}"account": "oauth2Account", "name": "oauth2Name", "email": "email"{\'}\'}',
    oidc_settings: 'OIDC Settings',
    test_mail_recipient: 'Only used as a test email recipient',
    to_enable_ssl: 'If the SMTP port is 465, you usually need to enable SSL',
    to_enable_tsl: 'If the SMTP port is 587, you usually need to enable TSL',
    wrong_please_re_enter: 'The address format is wrong, please re-enter',
    create_embedded_application: 'Create an embedded application',
    edit_embedded_application: 'Edit an embedded application',
    application_name: 'Application name',
    cross_domain_settings: 'Cross-domain settings',
    embedded_del_confirm: 'Are you sure you want to delete {0} applications?',
    embedded_search_placeholder: 'Search by application name, APP ID, or cross-domain settings',
    embedded_secret_len_change:
      'The key length has changed. The key will be reset soon. Are you sure?',
    embedded_management: 'Embedded Management',
    to_5_applications: 'You can create up to 5 applications',
    update_app_secret: 'Are you sure you want to update the APP Secret?',
    operate_with_caution:
      'After the reset, the existing APP Secret will become invalid. Please operate with caution. ',
    no_application: 'No application yet',
    delete_this_application: 'Are you sure you want to delete this application',
    platform_connection: 'Platform Integration',
    dingtalk_settings: 'DingTalk settings',
    enabled: 'Enabled',
    close: 'Closed',
    can_enable_it: 'Can be enabled after the test connection is valid',
    access: 'Access',
    feishu_settings: 'Lark settings',
    international_feishu_settings: 'International Lark settings',
    international_feishu: 'International Lark',
    enterprise_wechat_settings: 'Enterprise WeChat settings',
    enterprise_wechat: 'Enterprise WeChat',
    plugin_management: 'Plugin Management',
    search_plugin_name: 'Search plugin name',
    local_installation: 'Local installation',
    relevant_content_found: 'No relevant content found',
    no_plugins_yet: 'No plugin yet',
    installation_time: 'Installation time:',
    developer: 'Developer:',
    update_the_plugin: 'Are you sure you want to update this plugin?',
    to_take_effect_update: 'Update and restart the server to take effect',
    uninstall_the_plugin: 'Are you sure you want to uninstall this plugin?',
    to_take_effect_de: 'Uninstall and restart the server to take effect',
    uninstall_successful: 'Uninstall successful',
    update_successful: 'Update successful',
    installation_successful: 'Installation successful',
    can_be_uploaded: 'Only jar files can be uploaded',
    to_change_it:
      'After the variable type is changed, the variable value will be cleared. Are you sure you want to change it? ',
    add_variable: 'Add variable',
    edit_variable: 'Edit variable',
    variable_name: 'Variable name',
    variable_type: 'Variable type',
    system_built_in_variable: 'System built-in variable',
    custom_variable: 'Custom variable',
    account: 'Account',
    delete_this_variable: 'Are you sure you want to delete this variable? ',
    this_variable_value: 'Are you sure you want to delete this variable value? ',
    variable_list: 'Variable list',
    add_variable_value: 'Add variable value',
    search_variable_value: 'Search variable value',
    variable_value: 'Variable value',
    set_variable_value: 'Set variable value',
    the_minimum_value: 'Please enter the minimum value',
    the_maximum_value: 'Please enter the maximum value',
    the_minimum_date: 'Please select the minimum date',
    the_maximum_date: 'Please select the maximum date',
    on_the_left_p: 'Please select the left variable',
    edit_variable_value: 'Edit variable value',
    secret_length: 'Secret length',
    custom_area: 'Custom area',
    custom_area_tip:
      'Only supports custom geographic areas for provinces and municipalities in China',
    add_area: 'Add area',
    area_name: 'Area name',
    area_scope: 'Area scope',
    operation: 'Operation',
    sub_area_tip: 'Please select a province or municipality',
    delete_custom_area_tip:
      'This operation will cause the map using the custom area to fail to display normally, are you sure to delete?',
    please_select_area: 'Please select an area',
    delete_custom_sub_area_tip: 'Are you sure you want to delete this custom area?'
  },
  components: {
    dashboard_style: 'Dashboard style',
    overall_configuration: 'Overall configuration',
    dashboard_background: 'Dashboard background',
    chart_color: 'Chart color',
    advanced_style_settings: 'Advanced style settings',
    length_1_64_characters: 'Name field length 1-64 characters',
    current_page_first: 'Please save the current page first',
    from_other_organizations:
      'Switch to new organization, no permission to save resources from other organizations',
    close_the_page: 'Close the page',
    sure_to_exit: 'Current changes have not been saved, are you sure to exit? ',
    add_components_first: 'The current dashboard is empty, please add components first',
    rich_text: 'Rich text',
    media: 'Media',
    dashboard_configuration: 'Dashboard configuration',
    to_mobile_layout: 'Switch to mobile layout',
    complete: 'Complete',
    pager_color: 'Pager color',
    title_horizontal_position: 'Title horizontal position',
    title_display_position: 'Title display position',
    title_color: 'Title color',
    label_color: 'Label color',
    input_box_style: 'Input box style',
    overall_refresh: 'Overall refresh',
    previews_take_effect: 'Only public links take effect',
    jump_icon_color: 'Link, drill, jump icon color',
    level_display_color: 'Drill level display color',
    a_new_theme: 'Create a new theme',
    upload_a_cover: 'Please upload a cover',
    edit_theme: 'Edit theme',
    cover: 'Cover',
    to_delete_: 'Are you sure you want to delete [{0}]?',
    to_delete_variable: 'Are you sure you want to delete {0}?'
  },
  user: {
    change_password: 'Change Password',
    select_users: 'Please select a user',
    account: 'Account',
    name: 'Name',
    role: 'Role',
    state: 'Status',
    default_pwd: 'Default password',
    confirm_delete: 'Are you sure you want to delete this user? ',
    add_title: 'Add User',
    edit_title: 'Edit User',
    user_id: 'User',
    user_id_empty: 'Please enter an accurate user ID/account',
    search_placeholder: 'Search by name, account, email',
    batch_del: 'Batch delete',
    selection_info: '{0} items selected',
    clear_button: 'Clear',
    confirm_batch_delete: 'Are you sure you want to delete {0} users? ',
    reset_pwd: 'Reset password',
    reset_confirm: 'Do you want to restore to the initial password? ',
    reset_success: 'Reset successful',
    modify_cur_pwd: 'You need to log in again after modifying the current user password',
    switch_success: 'Switch successful',
    user_name_pattern_error:
      "Only numbers and letters and {'@'}._- are allowed, and must start with a number or letter",
    pwd_pattern_error:
      '6-20 characters and at least one uppercase letter, lowercase letter, number, special character',
    special_characters_are_not_supported: 'Special characters are not allowed',
    phone_format: 'Please fill in the correct format of the mobile phone number',
    email_format_is_incorrect: 'Please fill in the correct format of the email address',
    enable_success: 'Enabled',
    disable_success: 'Disabled',
    feishu: 'Lark',
    dingtalk: 'DingTalk',
    wechat_for_business: 'Enterprise WeChat',
    international_feishu: 'International Lark',
    user_management: 'User Management',
    cannot_be_modified: 'Administrator status cannot be modified',
    cannot_be_modified_de: 'Current user status cannot be modified',
    has_been_disabled: 'User has been disabled',
    selected_user: 'Selected: {msg} users',
    cannot_be_empty: 'Variable cannot be empty! ',
    set_variable_value: 'Please set the variable value:',
    be_an_integer: 'The variable value must be an integer:',
    be_less_than: 'Cannot be less than:',
    be_greater_than: 'Cannot be greater than:',
    than_start_time: ', Cannot be less than the start time:',
    than_end_time: ', Cannot be greater than the end time:',
    variable: 'Variable',
    variable_value: 'Variable value',
    enter_a_value: 'Please enter a value',
    contact_the_administrator: 'If the execution fails, please contact the administrator',
    data_import_successful: 'Import data successfully',
    imported_1_data: 'Successfully imported data {msg} items',
    import_1_data: ', Import failed {msg} items',
    can: 'Can',
    download_error_report: 'Download error report',
    modify_and_re_import: ', re-import after modification',
    return_to_view: 'Return to view',
    continue_importing: 'Continue importing',
    data_import_failed: 'Some data import failed',
    data_import_failed_de: 'Data import failed'
  },
  userimport: {
    buttonText: 'Batch import',
    dialogTitle: 'Batch upload',
    success: 'Import successful',
    placeholder: 'Click to select a file',
    defaultTip: 'Only supports xlsx and xls format files',
    errorTip:
      'Upload failed: There is non-compliant data in the file, if you need to view details,',
    downTip: 'Download template',
    uploadAgain: 'Upload again',
    backUserGrid: 'Return to user list',
    sure: 'Confirm',
    cancel: 'Cancel',
    repeatDown: 'Do not download repeatedly',
    limitMsg: 'File maximum 10M',
    suffixMsg: 'Only files ending with .xlsx|.xls are supported',
    exceedMsg: 'Only one file can be uploaded',
    templateError: 'User template error',
    first_please: 'Please first',
    fill_and_upload: 'Fill in as required and upload',
    import: 'Import'
  },
  role: {
    add_title: 'Add role',
    edit_title: 'Edit role',
    role_title: 'Role list',
    name: 'Role name',
    type: 'Role type',
    desc: 'Role description',
    average_role: 'Ordinary user',
    org_admin: 'Organization administrator',
    confirm_delete: 'Confirm to delete this role? ',
    delete_tips:
      '<div id="u7755_text" class="text" style="font-size: 12px;"><p><span style="color:#F59A23;">Friendly reminder, after the role is deleted, the users belonging to the role will be processed as follows:</span></p><p><span style="color:#7F7F7F;">1. If the user has other roles in the current organization, then after the role is deleted, the user will be removed from the role.</span></p><p><span style="color:#7F7F7F;">2. This role is the only role the user has in the current organization, but the user has roles in other organizations, then after the role is deleted, the user will also be removed from the current organization.</span></p><p><span style="color:#7F7F7F;">3. This role is the only role the user has in the current organization, and the user does not have any roles in other organizations in the system, then after the role is deleted, the user will also be deleted from the current system. </span></p><p><span style="color:#7F7F7F;"><br></span></p></div>',
    confirm_unbind_user: 'Are you sure you want to remove the user from the role? ',
    clear_in_system:
      'Friendly reminder, after being removed from the current role, the user no longer has any role in any organization and will be deleted from the system. ',
    clear_in_org:
      'Friendly reminder, after being removed from the current role, the user no longer has any role in the current organization and will be removed from the current organization. ',
    add_user: 'Add user to role ({0})',
    unbind_success: 'Removed successfully',
    bind_success: 'Binding successfully',
    bound_user: 'User added',
    option_user: 'Users can be added',
    org_user_title: 'Add organizational user',
    out_user_title: 'Add external user',
    search_user: 'Find user',
    search_one: 'Find a user',
    search_no: 'No results yet, the user may not exist! ',
    system_role: 'System built-in role',
    custom_role: 'Custom role',
    system: 'System',
    user_search_placeholder: 'Search by name, account',
    manager: 'Administrator',
    staff: 'User',
    system_role_edit_tips: 'System role cannot be edited',
    system_role_del_tips: 'System role cannot be deleted',
    empty_description: 'Please select the role on the left first'
  },
  org: {
    resource_migration: 'Resource migration',
    migration_type: 'Migration type',
    migrate_resources_only: 'Migrate resources only',
    and_authorization_related: 'Migrate resources and authorization related',
    target_organization: 'Target organization',
    target_directory: 'Target directory',
    resource_type: 'Resource type',
    user_dimension: 'Configured by user',
    resource_dimension: 'By resource allocation',
    org_title: 'Organization Management',
    org_move: 'Organization migration',
    add: 'Add organization',
    name: 'Organization name',
    sub_count: 'Number of sub-organizations',
    search_placeholder: 'Please enter a name to search',
    add_sub: 'Add sub-organization',
    edit: 'Edit organization',
    parent: 'Parent organization',
    default_cannot_move: 'Default organization cannot be deleted',
    cannot_delete: 'Cannot delete',
    confirm_delete: 'Are you sure you want to delete this organization? ',
    delete_children_first:
      'Please delete the child organization first, then delete the current organization',
    confirm_content:
      'A friendly reminder: After an organization is deleted, the resources under it will be treated as free-floating resources.',
    give_up_resource: 'Give up resources and delete directly',
    move_resource_first: 'Migrate resources first',
    default_parent_tips: '(Default current organization)',
    admin_parent_tips: '(Default root organization)',
    please_login_per_changed: 'Current user permissions have changed, please log in again'
  },
  auth: {
    permission_configuration: 'Permission Configuration',
    was_not_obtained: 'Resource node not obtained',
    search_name: 'Search name',
    loading: 'Loading...',
    on_the_left: 'Please select the left node',
    sysParams_type: {
      user_id: 'Account',
      user_name: 'Name',
      user_source: 'Origin',
      user_label: 'User label',
      user_email: 'Email',
      dept: 'Organization',
      role: 'Role'
    },
    user: 'User',
    role: 'Role',
    resource: 'Resource permissions',
    menu: 'Menu permissions',
    panel: 'Dashboard',
    screen: 'Data screen',
    dataset: 'Dataset',
    datasource: 'Data source',
    all_types: 'All types',
    empty_desc: 'Please select user/role and resource type',
    row_column: 'Row and column permission settings',
    row_permission: 'Row permission rules',
    enable_row: 'Enable row permissions',
    white_list: 'White list',
    white_user_not: 'The above permission rules are not effective for whitelist users',
    organization_or_role: 'Please select an organization or role',
    column_permission: 'Column permission rules',
    enable_column: 'Enable column permissions',
    search_by_field: 'Search by field name',
    add_condition: 'Add condition',
    add_relationship: 'Add relationship',
    filter_fields: 'Filter fields',
    select_filter_fields: 'Please select filter fields',
    enter_keywords: 'Please enter keywords',
    screen_method: 'Filter method',
    select: 'Please select',
    fixed_value: 'Fixed value',
    default_method: 'Default condition',
    select_all: 'Select all',
    added: 'Added',
    manual_input: 'Manual input',
    please_fill:
      'Please fill in one per line, up to 500 items can be added. Duplicate options and already added options will be automatically filtered out when identifying input',
    close: 'Close',
    add: 'Add',
    sure: 'Confirm',
    uncommitted_tips: 'There are uncommitted permission changes, do you want to submit? ',
    use: 'Use',
    check: 'View',
    export: 'Export',
    manage: 'Manage',
    auth: 'Authorization',
    resource_name: 'Resource name',
    menu_name: 'Menu name',
    from_role: 'Inherited from the following roles:',
    auth_alone: 'Individual authorization',
    org_role_empty:
      'Organization administrator has all resource permissions, no need to authorize again',
    user_role_empty:
      'This user is an organization administrator and has all resource permissions, no need to authorize again',
    sysParams: 'System Variables',
    set_rules: 'Set rules',
    inner_role_tips: 'System built-in role, permissions cannot be edited'
  },
  datasource: {
    datasource: 'Datasource',
    create: 'Create a new Datasource',
    config: 'Datasource configuration',
    table: 'Datasource table',
    table_name: 'Table name',
    remark: 'Remark',
    column_name: 'Field name',
    field_type: 'Field type',
    field_description: 'Field remark',
    dl: 'Data lake',
    other: 'Other',
    local_file: 'File',
    select_ds_type: 'Select Datasource type',
    select_ds: 'Select Datasource',
    ds_info: 'Enter Datasource information',
    sync_info: 'Data synchronization settings',
    input_name: 'Please enter a name',
    input_limit_2_25: '{0}-{1} characters',
    input_limit_2_50: '2-50 characters',
    input_limit_2_64: '2-64 characters',
    input_limit_1_64: '1-64 characters',
    data_source_configuration: 'Datasource configuration',
    data_source_table: 'Datasource Table',
    auth_method: 'Authentication Method',
    passwd: 'Username and Password',
    kerbers_info:
      'Please make sure krb5.Conf, Keytab Key, have been added to the path: /opt/dataease2.0/conf',
    client_principal: 'Client Principal',
    keytab_Key_path: 'Keytab Key Path',
    please_select_left: 'Please select from the left',
    show_info: 'Datasource Information',
    type: 'Type',
    please_choose_type: 'Please select the Datasource type',
    please_choose_data_type: 'Please select the calculation mode',
    data_base: 'Database Name',
    user_name: 'User Name',
    password: 'Password',
    host: 'Host Name/IP Address',
    doris_host: 'Doris Address',
    query_port: 'Query Port',
    http_port: 'Http Port',
    port: 'Port',
    datasource_url: 'Address',
    please_input_datasource_url:
      'Please enter the Elasticsearch address, such as: http://es_host:es_port',
    please_input_data_base: 'Please enter the database name',
    please_input_jdbc_url: 'Please enter the JDBC connection',
    please_select_oracle_type: 'Select the connection type',
    please_input_user_name: 'Please enter the username',
    please_input_password: 'Please enter the password',
    please_input_host: 'Please enter the host',
    please_input_url: 'Please enter the URL address',
    please_input_port: 'Please enter the port',
    please_input_be_port: 'Please enter the BE port',
    modify: 'Edit Datasource',
    copy: 'Copy Datasource',
    validate_success: 'Validation successful',
    validate_failed: 'Validation failed',
    validate: 'Validation',
    search_by_name: 'Search by name',
    delete_warning: 'Are you sure you want to delete?',
    input_limit: '{num} characters',
    oracle_connection_type: 'Service name/SID',
    oracle_sid: 'SID',
    oracle_service_name: 'Service name',
    get_schema: 'Get Schema',
    get_tables: 'Get data tables',
    get_views: 'Get views',
    view: 'View',
    schema: 'Schema',
    charset: 'Character set',
    targetCharset: 'Target character set',
    please_choose_schema: 'Please select database Schema',
    please_choose_charset: 'Please select database character set',
    please_choose_targetCharset: 'Please select target character set',
    edit_datasource_msg:
      'Modifying the Datasource information may make the Dataset under the Datasource unavailable. Confirm the modification? ',
    repeat_datasource_msg: 'Datasource information with the same configuration already exists, ',
    confirm_save: 'Confirm to save?',
    in_valid: 'Invalid Datasource',
    initial_pool_size: 'Initial number of connections',
    min_pool_size: 'Minimum number of connections',
    max_pool_size: 'Maximum number of connections',
    max_idle_time: 'Maximum idle time (seconds)',
    bucket_num: 'Bucket number',
    replication_num: 'Number of replicas',
    please_input_bucket_num: 'Please enter the number of Buckets',
    please_input_replication_num: 'Please enter the number of replicas',
    acquire_increment: 'Increase number',
    connect_timeout: 'Connection timeout (seconds)',
    please_input_initial_pool_size: 'Please enter the initial number of connections',
    please_input_min_pool_size: 'Please enter the minimum number of connections',
    please_input_max_pool_size: 'Please enter the maximum number of connections',
    please_input_max_idle_time: 'Please enter the maximum idle time (seconds)',
    please_input_acquire_increment: 'Please enter the increment number',
    please_input_query_timeout: 'Please enter the query timeout',
    please_input_connect_timeout: 'Please enter the connection timeout (seconds)',
    no_less_then_0: 'Parameters in advanced settings cannot be less than zero',
    port_no_less_then_0: 'Port cannot be less than zero',
    priority: 'Advanced settings',
    data_mode: 'Data mode',
    direct: 'Direct connection mode',
    extract: 'Extraction mode',
    all_compute_mode: 'Direct connection, extraction mode',
    extra_params: 'Additional JDBC connection string',
    jdbcUrl: 'JDBC connection',
    please_input_dataPath: 'Please enter the JsonPath data path',
    show_api_data: 'View API data structure',
    warning: 'Invalid data table included',
    data_table: 'Data table',
    data_table_name: 'Data table name',
    method: 'Request method',
    url: 'URL',
    add_api_table: 'Add API data table',
    edit_api_table: 'Edit API data table',
    base_info: 'Basic information',
    column_info: 'Data structure',
    request: 'Request',
    isUseJsonPath: 'Whether to specify JsonPath',
    path_all_info: 'Please fill in the complete address',
    jsonpath_info: 'Please fill in JsonPath',
    req_param: 'Request parameters',
    headers: 'Request headers',
    query_param: 'QUERY parameters',
    query_info: 'Follow in the address bar? The following parameters, such as: updateapi?id=112',
    key: 'Key',
    value: 'Value',
    data_path: 'Extract data',
    data_path_desc: 'Please use JsonPath to fill in the data path',
    body_form_data: 'form-data',
    body_x_www_from_urlencoded: 'x-www-form-urlencoded',
    body_json: 'json',
    body_xml: 'xml',
    body_raw: 'raw',
    request_body: 'Request body',
    auth_config: 'Authentication configuration',
    auth_config_info: 'Request requires permission verification',
    verified: 'Authentication',
    verification_method: 'Authentication method',
    username: 'Username',
    api_table_not_empty: 'API data table cannot be empty',
    has_repeat_name: 'API Duplicate table name',
    has_repeat_field_name: 'Duplicate field name, please modify before selecting',
    primary_key_change: 'Primary key cannot be changed:',
    api_field_not_empty: 'Field cannot be empty',
    file_not_empty: 'File cannot be empty',
    success_copy: 'Copy successfully',
    valid: 'Valid',
    invalid: 'Invalid',
    api_step_1: 'Connect to API',
    api_step_2: 'Extract data',
    _ip_address: 'Please enter the host name/IP address',
    display_name: 'Display name',
    connection_mode: 'Connection mode',
    driver_file: 'Driver file',
    edit_driver: 'Edit driver',
    driver_name: 'Driver name',
    drive_type: 'Driver type',
    add_driver: 'Add driver',
    diver_on_the_left: 'Please select a driver on the left',
    no_data_table: 'No data table yet',
    on_the_left: 'Please select a Datasource on the left',
    create_dataset: 'Create a dataset',
    table_description: 'Table notes',
    relational_database: 'Relational database',
    data_warehouse_lake: 'Data warehouse/data lake',
    non_relational_database: 'Non-relational database',
    all: 'All',
    this_data_source: 'Are you sure you want to delete this Datasource? ',
    delete_this_dataset: 'Are you sure you want to delete this dataset? ',
    edit_folder: 'Edit folder',
    click_to_check: 'Click to view blood relationship',
    please_select: 'Please select ',
    delete_this_item: 'Do you want to delete this item? ',
    can_be_uploaded: 'Only supports uploading JAR format files',
    query_timeout: 'Query timeout',
    add_data_source: 'Add Datasource',
    delete_this_driver: 'Are you sure you want to delete this driver? ',
    basic_info: 'Basic information',
    data_preview: 'Preview data',
    update_type: 'Update method',
    all_scope: 'Full update',
    add_scope: 'Incremental update',
    select_data_time: 'Select date and time',
    execute_rate: 'Execution frequency',
    execute_once: 'Immediate execution',
    simple_cron: 'Simple repetition',
    manual: 'Manual update',
    cron_config: 'Expression setting',
    no_limit: 'Unlimited',
    set_end_time: 'Set end time',
    exec_time: 'Execution time',
    start_time: 'Start time',
    end_time: 'End time',
    parse_filed: 'Parse field',
    set_key: 'Set as primary key',
    field_rename: 'Rename',
    select_type: 'Select Datasource type',
    sync_table: 'Synchronize the specified table',
    req_completed: 'Request successful',
    sync_rate: 'Update frequency',
    has_same_ds: 'There is a Datasource with the same configuration, confirm to save? ',
    app_token: 'app_token',
    input_app_token: 'Please enter the app_token',
    table_id: 'table_id',
    input_table_id: 'Please select a data table',
    view_id: 'view_id',
    input_view_id: 'Please select a view',
    remote_excel_url: 'Remote Excel/CSV Address',
    remote_excel_url_placeholder:
      'Please enter the remote Excel/CSV address, for example, ftp://*************/files/data.xlsx',
    remote_excel_url_empty: 'Please enter the remote Excel/CSV address',
    load_data: 'Load Data'
  },
  chart: {
    align: 'Alignment',
    reset: 'Reset',
    chart_refresh_tips: 'Chart refresh settings take precedence over dashboard refresh settings',
    '1-trend': 'Trend',
    '2-state': 'State',
    '3-rank': 'Rank',
    '4-location': 'Location',
    '5-weather': 'Weather',
    chinese: 'Chinese',
    mark_field: 'Field',
    mark_value: 'Value',
    function_style: 'Functional style',
    condition_style: 'Marker style',
    longitude: 'Longitude',
    latitude: 'Latitude',
    longitude_and_latitude: 'Longitude and latitude',
    gradient: 'Gradient',
    layer_controller: 'Indicator switch',
    show_zoom: 'Show zoom button',
    button_color: 'Button color',
    button_background_color: 'Button background color',
    chart_background: 'Component background',
    date_format: 'Please select the date parsing format',
    solid_color: 'Pure color',
    split_gradient: 'Separate gradient',
    continuous_gradient: 'Continuous gradient',
    map_center_lost:
      'The graph is missing the centroid or center attribute, please complete it and try again',
    margin_model: 'Mode',
    margin_model_auto: 'Automatic',
    margin_model_absolute: 'Absolute',
    margin_model_relative: 'Relative',
    margin_placeholder: 'Please enter a number between 0 and 100',
    margin_absolute_placeholderolder: 'Please enter a number between 0 and 40',
    rich_text_view_result_tips: 'Rich text only selects the first result',
    rich_text_view: 'Rich text chart',
    view_reset: 'Chart reset',
    view_reset_tips: 'Abandon changes to the chart? ',
    export_img: 'Export image',
    title_repeat: 'Current title already exists',
    save_snapshot: 'Save thumbnail',
    datalist: 'Chart',
    add_group: 'Add group',
    add_scene: 'Add scene',
    group: 'Group',
    scene: 'Scene',
    delete: 'Delete',
    move_to: 'Move to',
    rename: 'Rename',
    tips: 'Tips',
    confirm_delete: 'Confirm deletion',
    delete_success: 'Delete successfully',
    confirm: 'Confirm',
    cancel: 'Cancel',
    search: 'Search',
    back: 'Return',
    add_table: 'Add Dataset',
    process: 'Progress',
    add_chart: 'Add chart',
    db_data: 'Database Dataset',
    sql_data: 'SQL Dataset',
    excel_data: 'Excel dataset',
    custom_data: 'Custom dataset',
    pls_slc_tbl_left: 'Please select a chart from the left',
    add_db_table: 'Add a database dataset',
    add_api_table: 'Add an API dataset',
    pls_slc_data_source: 'Please select a Datasource',
    table: 'Table',
    edit: 'Edit',
    create_view: 'Create a view',
    data_preview: 'Data preview',
    dimension: 'Dimension',
    quota: 'Indicator',
    dimension_abb: 'Dim',
    quota_abb: 'Ind',
    column_quota: 'Column indicator',
    line_quota: 'Line indicator',
    time_dimension_or_quota: 'Time dimension or indicator',
    aggregate_time: 'Aggregate time latitude',
    title: 'Title',
    show: 'Show',
    chart_type: 'Chart type',
    shape_attr: 'Graphic attributes',
    module_style: 'Component style',
    result_filter: 'Filter',
    chart_mix_label_only_left: 'Only valid for bar chart settings',
    x_axis: 'Horizontal axis',
    y_axis: 'Vertical axis',
    chart: 'Chart',
    close: 'Close',
    summary: 'Summary method',
    fast_calc: 'Quick calculation',
    sum: 'Sum',
    count: 'Count',
    avg: 'Average',
    max: 'Maximum value',
    min: 'Minimum value',
    stddev_pop: 'Standard deviation',
    var_pop: 'Variance',
    quick_calc: 'Quick calculation',
    show_name_set: 'Edit display name',
    show_name: 'Display name',
    backdrop_blur: 'Background blur',
    color: 'Color',
    color_case: 'Color scheme',
    pls_slc_color_case: 'Please select a color scheme',
    color_default: 'Default',
    color_retro: 'Retro',
    color_future: 'Future',
    color_gradual: 'Gradient',
    color_business: 'Business',
    color_gentle: 'Soft',
    color_elegant: 'Elegant',
    color_technology: 'Technology',
    color_simple: 'Simple',
    not_alpha: 'Opacity',
    column_width_ratio: 'Column Width Ratio',
    area_border_color: 'Map border',
    area_base_color: 'Map area fill',
    size: 'Size',
    bar_width: 'Bar width',
    bar_gap: 'Bar gap',
    adapt: 'Adaptive',
    line_width: 'Line width',
    line_type: 'Line type',
    line_symbol: 'Bend point',
    line_symbol_size: 'Point size',
    line_type_solid: 'Solid line',
    line_type_dashed: 'Dashed line',
    line_symbol_circle: 'Circle',
    line_symbol_emptyCircle: 'Hollow circle',
    line_symbol_rect: 'Rectangle',
    line_symbol_roundRect: 'Rounded rectangle',
    line_symbol_triangle: 'Triangle',
    line_symbol_diamond: 'Diamond',
    line_symbol_pin: 'Nail',
    line_symbol_arrow: 'Arrow',
    line_symbol_none: 'None',
    line_area: 'Area',
    pie_inner_radius: 'Inner diameter',
    pie_outer_radius: 'Outer diameter',
    funnel_width: 'Width',
    line_smooth: 'Smooth line',
    title_style: 'Title style',
    text_fontsize: 'Font size',
    text_color: 'Font color',
    text_h_position: 'Horizontal position',
    text_v_position: 'Vertical position',
    text_position: 'Position',
    text_pos_left: 'Left',
    text_pos_center: 'Center',
    text_pos_right: 'Right',
    text_pos_top: 'Top',
    text_pos_bottom: 'Bottom',
    text_italic: 'Font italic',
    italic: 'Italic',
    orient: 'Direction',
    horizontal: 'Horizontal',
    vertical: 'Vertical',
    legend: 'Legend',
    legend_num: 'Legend number',
    shape: 'Shape',
    polygon: 'Polygon',
    circle: 'Circle',
    label: 'Label',
    label_position: 'Label position',
    label_bg: 'Label background',
    label_shadow: 'Label shadow',
    label_shadow_color: 'Shadow color',
    label_reserve_decimal_count: 'Retain decimals',
    content_formatter: 'Content format',
    inside: 'Inside',
    tooltip: 'Prompt',
    tooltip_item: 'Data item',
    tooltip_axis: 'Coordinate axis',
    formatter_plc: 'When the content format is empty, display the default format',
    xAxis: 'Horizontal axis',
    yAxis: 'Vertical axis',
    yAxisLeft: 'Left vertical axis',
    yAxisRight: 'Right vertical axis',
    position: 'Position',
    rotate: 'Angle',
    name: 'Name',
    icon: 'Icon',
    trigger_position: 'Trigger position',
    asc: 'Ascending order',
    desc: 'Descending',
    sort: 'Sort',
    default: 'Default',
    filter: 'Filter',
    none: 'None',
    background: 'Background',
    border: 'Corner',
    border_width: 'Border width',
    border_radius: 'Rounded corners',
    alpha: 'Transparency',
    add_filter: 'Add filter',
    no_limit: 'Unlimited',
    filter_eq: 'Equal to',
    filter_not_eq: 'Not equal to',
    filter_lt: 'Less than',
    filter_le: 'Less than or equal to',
    filter_gt: 'Greater than',
    filter_ge: 'Greater than or equal to',
    filter_null: 'Empty',
    filter_not_null: 'Not empty',
    filter_empty: 'Empty string',
    filter_not_empty: 'Not empty string',
    filter_include: 'Include',
    filter_not_include: 'Not included',
    rose_type: 'Rose diagram mode',
    radius_mode: 'Radius',
    area_mode: 'Area',
    rose_radius: 'Rounded corners',
    view_name: 'Chart title',
    belong_group: 'Belonging group',
    select_group: 'Select group',
    name_can_not_empty: 'Name cannot be empty',
    template_can_not_empty: 'Please import template',
    custom_count: 'Number of records',
    table_title_fontsize: 'Header font size',
    table_item_fontsize: 'Table font size',
    table_header_bg: 'Header Bg',
    table_header_row_bg: 'Header&Row Bg',
    table_item_bg: 'Table Bg',
    table_header_font_color: 'Header font',
    table_item_font_color: 'Table font',
    table_show_index: 'Show serial number',
    table_header_sort: 'Turn on header sorting',
    table_show_row_tooltip: 'Turn on row header tooltip',
    table_show_col_tooltip: 'Turn on column header tooltip',
    table_show_cell_tooltip: 'Turn on cell tooltip',
    table_show_header_tooltip: 'Turn on header tooltip',
    table_summary: 'Total',
    table_show_summary: 'Show total',
    table_summary_label: 'Total label',
    table_header_show_horizon_border: 'Header horizontal border',
    table_header_show_vertical_border: 'Header vertical border',
    table_cell_show_horizon_border: 'Horizontal cell border',
    table_cell_show_vertical_border: 'Vertical cell border',
    table_col_freeze_tip: 'First n col',
    table_row_freeze_tip: 'First n row',
    table_freeze: 'Freeze',
    stripe: 'Zebra stripe',
    start_angle: 'Starting angle',
    end_angle: 'Ending angle',
    style_priority: 'Style priority',
    dashboard: 'Dashboard',
    dimension_color: 'Name color',
    quota_color: 'Value color',
    dimension_font_size: 'Name font size',
    quota_font_size: 'Value font size',
    space_split: 'Name/value interval',
    only_one_quota: 'Only supports 1 indicator',
    only_one_result: 'Only show the first calculation result',
    dimension_show: 'Name display',
    quota_show: 'Value display',
    title_limit: 'Title cannot be greater than 50 characters',
    filter_condition: 'Filter condition',
    filter_field_can_null: 'Filter field required',
    preview_100_data: 'Preview the first 100 records',
    chart_table_normal: 'Summary table',
    chart_table_info: 'Details table',
    chart_card: 'Indicator card',
    chart_bar: 'Basic bar chart',
    chart_bar_stack: 'Stacked bar chart',
    chart_percentage_bar_stack: 'Percentage bar chart',
    chart_bar_horizontal: 'Basic bar chart',
    chart_bar_stack_horizontal: 'Stacked bar chart',
    chart_percentage_bar_stack_horizontal: 'Percentage bar chart',
    chart_bar_range: 'Interval bar chart',
    chart_bidirectional_bar: 'Symmetric bar chart',
    chart_progress_bar: 'Progress bar',
    chart_line: 'Basic line chart',
    chart_area_stack: 'Stacked line chart',
    chart_pie: 'Pie chart',
    chart_pie_donut: 'Ring chart',
    chart_pie_rose: 'Rose chart',
    chart_pie_donut_rose: 'Rose ring chart',
    chart_funnel: 'Funnel chart',
    chart_sankey: 'Sankey chart',
    chart_radar: 'Radar chart',
    chart_gauge: 'Dashboard',
    chart_map: 'Map',
    chart_bubble_map: 'Bubble map',
    dateStyle: 'Date display',
    datePattern: 'Date format',
    y: 'Year',
    y_M: 'Year Month',
    y_Q: 'Year Quarter',
    y_W: 'Year Week',
    y_M_d: 'Year Month Day',
    M_d: 'Month Day',
    M: 'Month',
    d: 'Day',
    H: 'Hours',
    H_m: 'Hours Minutes',
    H_m_s: 'Hours Minutes Seconds',
    y_M_d_H: 'Year Month Day Hours',
    y_M_d_H_m: 'Year Month Day Hours Minutes',
    y_M_d_H_m_s: 'Year Month Day Hours Minutes Seconds',
    date_sub: 'yyyy-MM-dd',
    date_split: 'yyyy/MM/dd',
    chartName: 'New Chart',
    chart_show_error: 'Cannot display normally',
    chart_error_tips:
      'Abnormal data acquisition, if you have any questions, please contact the administrator, ',
    chart_show_error_info: 'Click to show error info',
    title_cannot_empty: 'Title cannot be empty',
    table_title_height: 'Header row height',
    table_item_height: 'Table row height',
    text: 'Text',
    axis_show: 'Axis display',
    axis_nameShow: 'Axis name display',
    axis_color: 'Axis color',
    axis_width: 'Axis width',
    axis_type: 'Axis type',
    grid_show: 'Grid display',
    grid_color: 'Grid color',
    grid_width: 'Grid width',
    grid_type: 'Grid type',
    axis_type_solid: 'Solid',
    axis_type_dashed: 'Dotted',
    axis_type_dotted: 'Dot',
    axis_label_show: 'Label display',
    axis_label_color: 'Label color',
    axis_label_fontsize: 'Label size',
    text_style: 'Font style',
    bolder: 'Bold',
    change_ds: 'Change Dataset',
    change_ds_tip:
      'Tip: Changing data sets will cause fields to change, and charts need to be recreated',
    axis_name_color: 'Name color',
    axis_name_fontsize: 'Name font',
    pie_label_line_show: 'Guide line',
    outside: 'Outside',
    center: 'Center',
    split: 'Axis',
    axis_line: 'Axis',
    axis_label: 'Axis label',
    label_fontsize: 'Label size',
    split_line: 'Split line',
    split_color: 'Split color',
    shadow: 'Shadow',
    condition: 'Filter value',
    filter_value_can_null: 'Filter value cannot be null',
    filter_like: 'Contains',
    filter_not_like: 'Does not contain',
    filter_in: 'Belongs to',
    filter_not_in: 'Does not belong to',
    color_light: 'Bright',
    color_classical: 'Classic',
    color_fresh: 'Fresh',
    color_energy: 'Energy',
    color_red: 'Fiery red',
    color_fast: 'Fast',
    color_spiritual: 'Spiritual',
    chart_details: 'Chart details',
    export: 'Export',
    details: 'Details',
    image: 'Image',
    export_details: 'Export details',
    chart_data: 'Data',
    chart_style: 'Style',
    drag_block_type_axis: 'Category axis',
    drag_block_type_axis_left: 'Left subcategory',
    drag_block_type_axis_right: 'Right subcategory',
    drag_block_type_axis_start: 'Source',
    drag_block_type_axis_end: 'Destination',
    drag_block_value_axis: 'Value axis',
    drag_block_value_start: 'Start value',
    drag_block_value_end: 'End value',
    drag_block_value_axis_left: 'Left value axis',
    drag_block_value_axis_right: 'Right value axis',
    drag_block_table_data_column: 'Data column',
    drag_block_pie_angle: 'Sector angle',
    drag_block_pie_label: 'Sector label',
    drag_block_pie_radius: 'Sector radius',
    drag_block_gauge_angel: 'Pointer angle',
    drag_block_label_value: 'Value',
    drag_block_funnel_width: 'Funnel layer width',
    drag_block_funnel_split: 'Funnel layer',
    drag_block_radar_length: 'Branch length',
    drag_block_radar_label: 'Branch label',
    map_range: 'Map range',
    select_map_range: 'Please select the map range',
    area: 'Region',
    stack_item: 'Stack item',
    placeholder_field: 'Drag the field here',
    axis_label_rotate: 'Label angle',
    chart_scatter_bubble: 'Bubble chart',
    chart_scatter: 'Scatter chart',
    bubble_size: 'Bubble size',
    chart_treemap: 'Rectangular treemap',
    drill: 'Drill',
    drag_block_treemap_label: 'Block label',
    drag_block_treemap_size: 'Block size',
    bubble_symbol: 'Graphics',
    gap_width: 'Gap',
    width: 'Width',
    height: 'Height',
    system_case: 'System solution',
    custom_case: 'Custom',
    last_layer: 'Currently the last level',
    radar_size: 'Size',
    chart_mix: 'Column and line combination chart',
    chart_mix_group_column: 'Grouped column and line combination chart',
    chart_mix_stack_column: 'Stacked column line combination chart',
    chart_mix_dual_line: 'Dual line combination chart',
    axis_value: 'Axis value',
    axis_value_min: 'Minimum value',
    axis_value_max: 'Maximum value',
    axis_value_split: 'Interval',
    axis_auto: 'Automatic',
    table_info_switch: 'Details table switching will clear the dimension',
    drag_block_value_axis_main: 'Main axis value',
    drag_block_value_axis_ext: 'Secondary axis value',
    yAxis_main: 'Main vertical axis',
    yAxis_ext: 'Secondary vertical axis',
    total: 'Total',
    items: 'Items',
    chart_liquid: 'Water wave chart',
    chart_indicator: 'Indicator card',
    drag_block_progress: 'Progress indicator',
    liquid_max: 'Target value',
    liquid_outline_border: 'Border thickness',
    liquid_outline_distance: 'Border interval',
    liquid_wave_length: 'Water wave length',
    liquid_wave_count: 'Water wave number',
    liquid_shape: 'Shape',
    liquid_shape_circle: 'Circle',
    liquid_shape_diamond: 'Diamond',
    liquid_shape_triangle: 'Triangle',
    liquid_shape_pin: 'Balloon',
    liquid_shape_rect: 'Rectangle',
    dimension_or_quota: 'Dimension or quotation',
    axis_value_split_count: 'Number of scales',
    axis_value_split_space: 'Scale spacing',
    chart_waterfall: 'Waterfall chart',
    pie_inner_radius_percent: 'Inner diameter ratio',
    pie_outer_radius_size: 'Outer diameter size',
    table_page_size: 'Paging',
    table_page_size_unit: 'Articles/page',
    result_count: 'Result display',
    result_mode_all: 'All',
    result_mode_custom: 'Custom',
    chart_word_cloud: 'Word cloud',
    drag_block_word_cloud_label: 'Word label',
    drag_block_word_cloud_size: 'Word size',
    splitCount_less_100: 'Scale range 0-100',
    change_chart_type: 'Change type',
    chart_type_table: 'Table',
    chart_type_quota: 'Indicator',
    chart_type_trend: 'Line/surface ',
    chart_type_compare: 'Column/bar ',
    chart_type_distribute: 'Distribution ',
    chart_type_relation: 'Relationship ',
    chart_type_dual_axes: 'Dual axis ',
    chart_type_space: 'Map',
    preview: 'Previous step',
    next: 'Next',
    select_dataset: 'Select a dataset',
    select_chart_type: 'Select a chart type',
    recover: 'Reset',
    yoy_label: 'Year-on-year/Month-on-month',
    yoy_setting: 'Same-on-month/Month-on-month settings',
    pls_select_field: 'Please select a field',
    compare_date: 'Comparison date',
    compare_type: 'Comparison type',
    compare_data: 'Data settings',
    year_yoy: 'Year-on-year',
    month_yoy: 'Month-on-month',
    quarter_yoy: 'Quarter-on-year',
    week_yoy: 'Weekly-on-year',
    day_yoy: 'Daily-on-year',
    year_mom: 'Yearly-on-month',
    month_mom: 'Monthly-on-month',
    quarter_mom: 'Quarterly-on-month',
    week_mom: 'Weekly-on-month',
    day_mom: 'Daily-on-month',
    data_pre: 'Specific value',
    data_sub: 'Comparison difference',
    data_percent: 'Difference percentage',
    compare_calc_expression: 'Calculation formula',
    compare_calc_day_pre: "Yesterday's Data",
    compare_calc_day_sub: "Today's Data - Yesterday's Data",
    compare_calc_day_percent: "(Today's Data / Yesterday's Data - 1) * 100%",
    compare_calc_month_pre: "Same Day Last Month's Data",
    compare_calc_month_pre_m: "Last Month's Data",
    compare_calc_month_sub: "Today's Data - Same Day Last Month's Data",
    compare_calc_month_sub_m: "Current Month's Data - Last Month's Data",
    compare_calc_month_percent: "(Today's Data / Same Day Last Month's Data - 1) * 100%",
    compare_calc_month_percent_m: "(Current Month's Data / Last Month's Data - 1) * 100%",
    compare_calc_year_pre: "Same Day Last Year's Data",
    compare_calc_year_pre_m: "Same Month Last Year's Data",
    compare_calc_year_pre_y: "Last Year's Data",
    compare_calc_year_sub: "Today's Data - Same Day Last Year's Data",
    compare_calc_year_sub_m: "Current Year's Monthly Data - Same Month Last Year's Data",
    compare_calc_year_sub_y: "Current Year's Data - Last Year's Data",
    compare_calc_year_percent: "(Today's Data / Same Day Last Year's Data - 1) * 100%",
    compare_calc_year_percent_m:
      "(Current Year's Monthly Data / Same Month Last Year's Data - 1) * 100%",
    compare_calc_year_percent_y: "(Current Year's Data / Last Year's Data - 1) * 100%",
    compare_calc_tip:
      'When filtering is required for comparison dates, please use the filter component to apply the filter; using chart filters, dashboard drilling, and linking functions may lead to inconsistent results.',
    and: 'And',
    or: 'Or',
    logic_exp: 'Logical condition',
    enum_exp: 'Field enumeration value',
    pls_slc: 'Please select',
    filter_exp: 'Filter value',
    filter_type: 'Filter method',
    filter_value_can_not_str: 'Numeric type field filter value cannot contain text',
    enum_value_can_not_null: 'Field enumeration value cannot be empty',
    table_config: 'Table configuration',
    table_column_width_config: 'Column width adjustment',
    table_column_adapt: 'Adaptive',
    table_column_fixed: 'Fixed column width',
    table_column_custom: 'Custom',
    chart_table_pivot: 'Pivot table',
    chart_table_heatmap: 'Heat map',
    table_pivot_row: 'Data row',
    field_error_tips:
      'The original field of the data set corresponding to this field has changed (including dimensions, indicators, field types, fields deleted, etc.), it is recommended to re-edit',
    mark_field_error: 'The data set has changed, the current field does not exist, please reselect',
    table_border_color: 'Border color',
    table_header_align: 'Header alignment',
    table_item_align: 'Table alignment',
    table_align_left: 'Left alignment',
    table_align_center: 'Center',
    table_align_right: 'Right alignment',
    table_scroll_bar_color: 'Scroll bar color',
    table_pager_style: 'Pager style',
    page_pager_simple: 'Simplified',
    page_pager_general: 'General',
    draw_back: 'Retract',
    senior: 'Advanced',
    senior_cfg: 'Advanced settings',
    function_cfg: 'Function settings',
    analyse_cfg: 'Analysis warning',
    slider: 'Thumbnail axis',
    slider_range: 'Default range',
    slider_bg: 'Background',
    slider_fill_bg: 'Selected background',
    slider_text_color: 'Font color',
    chart_no_senior:
      'There is no advanced configuration for the current chart type, please stay tuned',
    chart_no_properties: 'There is no style configuration for the current chart type',
    assist_line: 'Assist line',
    field_fixed: 'Fixed value',
    line_type_dotted: 'Dot',
    value_can_not_empty: 'Value cannot be empty',
    value_error: 'Value must be a numeric value',
    threshold: 'Conditional style',
    threshold_range: 'Threshold range',
    gauge_threshold_format_error: 'Format error',
    total_cfg: 'Total configuration',
    col_cfg: 'Column summary',
    row_cfg: 'Row summary',
    total_show: 'Total',
    total_position: 'Position',
    total_label: 'Alias',
    sub_total_show: 'Subtotal',
    total_pos_top: 'Top',
    total_pos_bottom: 'Bottom',
    total_pos_left: 'Left',
    total_pos_right: 'Right',
    chart_label: 'Text card',
    drag_block_label: 'Label',
    count_distinct: 'Double count',
    table_page_mode: 'Paging mode',
    page_mode_page: 'Page turn',
    page_mode_pull: 'Pull down',
    exp_can_not_empty: 'Condition cannot be empty',
    value_formatter: 'Number format',
    value_formatter_type: 'Format type',
    value_formatter_auto: 'Automatic',
    value_formatter_value: 'Number',
    value_formatter_percent: 'Percentage',
    value_formatter_unit: 'Unit',
    value_formatter_unit_language: 'Language',
    value_formatter_unit_language_ch: 'Chinese',
    value_formatter_unit_language_en: 'English',
    value_formatter_decimal_count: 'Number of decimal places',
    value_formatter_suffix: 'Unit suffix',
    show_gap: 'Show interval value',
    indicator_suffix_placeholder: 'Please enter 1-10 characters',
    indicator_suffix: 'Suffix',
    indicator_value: 'Indicator value',
    value_formatter_thousand_separator: 'Thousands separator',
    value_formatter_example: 'Example',
    unit_none: 'None',
    unit_thousand: 'Thousand',
    unit_ten_thousand: 'Ten thousand',
    unit_million: 'Million',
    unit_hundred_million: 'Hundred million',
    formatter_decimal_count_error: 'Please enter an integer between 0 and 10',
    gauge_threshold_compare_error: 'The threshold range needs to increase step by step',
    tick_count: 'Number of tick intervals',
    custom_sort: 'Custom',
    custom_sort_tip:
      'Custom sorting has the highest priority and only supports single field customization',
    clean_custom_sort: 'Clear custom sorting',
    ds_field_edit: 'Dataset field management',
    chart_field_edit: 'Chart field management',
    copy_field: 'Copy field',
    calc_field: 'Calculated field',
    form_type: 'Category',
    scroll_cfg: 'Scroll settings',
    scroll: 'Scroll',
    open: 'Open',
    row: 'Number of rows',
    interval: 'Interval',
    max_more_than_mix: 'Maximum value must be greater than minimum value',
    field: 'Field',
    textColor: 'Text',
    backgroundColor: 'Background',
    rowBackgroundColor: 'Row Background',
    colBackgroundColor: 'Column Background',
    cornerBackgroundColor: 'Corner Background',
    field_can_not_empty: 'Field cannot be empty',
    conditions_can_not_empty:
      'Field conditions cannot be empty. If there are no conditions, please delete the field directly',
    remark: 'Remark',
    remark_placeholder: 'Remark limit 512 characters',
    remark_show: 'Show remark',
    remark_edit: 'Edit remark',
    remark_bg_color: 'Background fill',
    quota_font_family: 'Value font',
    quota_text_style: 'Value style',
    quota_letter_space: 'Value letter spacing',
    dimension_font_family: 'Name font',
    dimension_text_style: 'Name style',
    dimension_letter_space: 'Name letter spacing',
    name_value_spacing: 'Name/value spacing',
    font_family: 'Font',
    letter_space: 'Letter spacing',
    font_shadow: 'Font shadow',
    chart_area: 'Area chart',
    fix: 'Fixed value',
    dynamic: 'Dynamic value',
    gauge_size_field_delete: 'Field in dynamic value has changed, please re-edit',
    chart_group: 'Subcategory',
    chart_bar_group: 'Grouped bar chart',
    chart_bar_group_stack: 'Grouped stacked bar chart',
    field_dynamic: 'Dynamic value',
    aggregation: 'Aggregation method',
    filter_between: 'Between',
    field_not_empty: 'Field cannot be empty',
    summary_not_empty: 'Aggregation method cannot be empty',
    reserve_zero: 'Round',
    reserve_one: 'One',
    reserve_two: 'Two',
    proportion: 'Proportion',
    label_content: 'Label display',
    percent: 'Proportion',
    table_index_desc: 'Table header name',
    total_sort: 'Total sort',
    total_sort_none: 'None',
    total_sort_asc: 'Ascending',
    total_sort_desc: 'Descending',
    total_sort_field: 'Sort field',
    empty_data_strategy: 'Empty value handling',
    empty_data_field_ctrl: 'Field settings',
    break_line: 'Keep empty',
    set_zero: 'Set to 0',
    ignore_data: 'Hide empty values',
    sub_dimension_tip:
      'This field is required and the field in the category axis should not be used. If this field is not needed, please select the basic chart for display, otherwise the display effect will not be ideal. ',
    drill_dimension_tip: 'Drill fields only support fields in the data set',
    table_scroll_tip: 'Details table is only effective when the paging mode is "Drop-down". ',
    table_threshold_tip:
      'Tip: Do not select fields repeatedly. If the same field is configured repeatedly, only the last field configuration will take effect',
    table_column_width_tip:
      'Fixed column width is not always effective. <br/>Container width takes precedence over column width, that is, (table container width / number of columns > specified column width), then column width takes precedence over (container width / number of columns). ',
    reference_field_tip:
      'The reference field starts with "[" and ends with "]". <br/>Do not modify the reference content, otherwise the reference will fail. <br/>If you enter content in the same format as the reference field, it will be treated as a reference field. ',
    scatter_tip:
      'When this indicator takes effect, the bubble size attribute in the style size will be invalid',
    place_name_mapping: 'Place name mapping',
    axis_tip:
      'Minimum value, maximum value, and interval are all numeric types; if not filled in, this item will be considered automatic. <br/>Please make sure that the filled value can be calculated correctly, otherwise the axis value will not be displayed normally. ',
    format_tip: `The template variables are {a}, {b}, {c}, {d}, which represent the series name, data name, data value, etc. <br>
      When the trigger position is 'coordinate axis', there will be multiple series of data. At this time, the index of the series can be represented by {a0}, {a1}, {a2} followed by an index. <br>
      The meanings of {a}, {b}, {c}, {d} in different chart types are different. The variables {a}, {b}, {c}, {d} represent the data meanings in different chart types: <br><br>
      Line (area) chart, column (bar) chart, dashboard: {a} (series name), {b} (category value), {c} (value) <br>
      Pie chart, funnel chart: {a} (series name), {b} (data item name), {c} (value), {d} (percentage) <br>
      Map: {a} (series name), {b} (area name), {c} (combined value), {d} (none) <br>
      Scatter chart (bubble) chart: {a} (series name), {b} (data name), {c} (value array), {d} (none)`,
    h_position: 'Horizontal position',
    v_position: 'Vertical position',
    p_left: 'Left alignment',
    p_right: 'Right alignment',
    p_top: 'Top alignment',
    p_bottom: 'Bottom alignment',
    p_center: 'Center',
    table_auto_break_line: 'Automatic line break',
    table_break_line_tip:
      'Turn on automatic line break, the table header row height setting will be invalid',
    table_break_line_max_lines: 'Maximum number of lines',
    step: 'Step length (px)',
    no_function:
      'Functions do not yet support direct references, please enter manually in the field expression. ',
    chart_flow_map: 'Flow map',
    chart_heat_map: 'Heat map',
    start_point: 'Starting point',
    end_point: 'Ending point',
    line: 'Line',
    map_style: 'Map style',
    map_style_url: 'Map style URL',
    map_pitch: 'Pitch',
    map_rotation: 'Rotation',
    map_style_normal: 'Standard',
    map_style_light: 'Bright',
    map_style_dark: 'Dark',
    map_style_fresh: 'Grass green',
    map_style_grey: 'Gray',
    map_style_blue: 'Indigo blue',
    map_style_darkblue: 'Polar night blue',
    map_line_type: 'Type',
    type: 'Type',
    map_line_width: 'Line width',
    map_line_height: 'Line height',
    map_line_linear: 'Gradient',
    map_line_animate: 'Animation',
    heatmap_classics: 'Classic heatmap',
    heatmap3D: '3D heatmap',
    heatMapIntensity: 'Heat intensity',
    heatMapRadius: 'Heat point radius',
    map_line_animate_duration: 'Animation interval',
    map_line_animate_interval: 'Trail interval',
    map_line_animate_trail_length: 'Trail length',
    map_line_type_line: 'Straight line',
    map_line_type_arc: 'Arc',
    map_line_type_arc_3d: '3D Arc',
    map_line_type_great_circle: 'Great circle',
    map_line_color_source_color: 'Starting color',
    map_line_color_target_color: 'End color',
    map_line_theta_offset: 'Radians',
    refresh_frequency: 'Refresh frequency',
    enable_refresh_view: 'Enable refresh',
    enable_view_loading: 'Chart loading prompt',
    minute: 'Minutes',
    switch_chart: 'Switch chart',
    update_chart_data: 'Update chart data',
    second: 'Seconds',
    more_settings: 'More settings',
    basic_style: 'Basic style',
    table_header: 'Header',
    table_cell: 'Cell',
    table_total: 'Total',
    slc_logic: 'Select logical relationship',
    add_addition: 'Add condition',
    logic_and: 'All',
    logic_or: 'Any',
    conform_below: 'Conform to the following',
    addition: 'Condition',
    drill_field_error: 'The drill-down start field must be in the dimension',
    error_not_number: 'Drag of non-numeric indicators is not supported',
    error_q_2_d: 'Drag of indicators to dimensions is not supported',
    error_d_2_q: 'Drag of dimensions to indicators is not supported',
    error_d_not_coordinates:
      'Drag of dimensions that are not of coordinates types is not supported',
    error_d_not_time_2_q: 'Drag of non-time dimensions is not supported',
    error_bar_range_axis_type_not_equal:
      'The start value and end value must be set to the same type',
    only_input_number: 'Please enter a correct value',
    value_min_max_invalid: 'The minimum value must be less than the maximum value',
    add_assist_line: 'Add auxiliary line',
    assist_line_tip:
      'The auxiliary line value format follows the label formatting configuration of the vertical axis/horizontal axis, please configure it in the style. ',
    add_threshold: 'Add threshold',
    add_condition: 'Add condition',
    chart_quadrant: 'Quadrant chart',
    quadrant: 'Quadrant',
    font_size: 'Font size',
    word_size_range: 'Font size range',
    word_spacing: 'Text spacing',
    radiusColumnBar: 'Column',
    rightAngle: 'Right angle',
    roundAngle: 'Rounded angle',
    topRoundAngle: 'Top rounded angle',
    table_layout_mode: 'Display form',
    table_layout_grid: 'Tile display',
    table_layout_tree: 'Tree display',
    top_n_desc: 'Merge data',
    top_n_input_1: 'Show Top',
    top_n_input_2: ', Merge the rest',
    top_n_label: 'Other item names',
    progress_target: 'Target value',
    progress_current: 'Actual value',
    gauge_axis_label: 'Display scale',
    gauge_percentage_tick: 'Percentage scale',
    add_style: 'Add style',
    map_symbol_marker: 'Marker',
    map_symbol_pentagon: 'Pentagon',
    map_symbol_hexagon: 'Hexagon',
    map_symbol_octagon: 'Octagon',
    map_symbol_hexagram: 'Diamond',
    tip: 'Tip',
    hide: 'Hide',
    show_label: 'Show label',
    security_code: 'Security key',
    auto_fit: 'Adaptive zoom',
    zoom_level: 'Zoom level',
    central_point: 'Center point',
    full_display: 'Full display',
    show_hover_style: 'Show mouse hover style',
    table_header_group: 'Header grouping',
    table_header_group_config: 'Header grouping config',
    cancel_group: 'Cancel grouping',
    cancel_all_group: 'Cancel all grouping',
    group_name: 'Group name',
    merge_group: 'Merge group',
    table_header_group_config_tip:
      'Field additions, deletions, positional changes, and explicit and implicit modifications can cause grouping to become invalid.',
    group_name_edit_tip: 'Group names are 1-20 characters in length',
    group_name_error_tip: 'Please input valid group name',
    merge_cells: 'Merge cells',
    length_limit: 'Length limit',
    radar_point: 'Enable auxiliary points',
    radar_point_size: 'Size',
    radar_area_color: 'Enable area',
    table_freeze_tip: 'After merging cells, column and row freezing is not supported',
    merge_cells_tips:
      'After merging cells, row and column freezing, automatic line wrapping, and zebra pattern will become invalid, the serial number of the current page will start from 1',
    merge_cells_break_line_tip: 'After merging cells, automatic line wrapping is not supported',
    font_family_ya_hei: 'Microsoft YaHei',
    font_family_song_ti: 'SimSun',
    font_family_kai_ti: 'KaiTi',
    font_family_hei_ti: 'SimHei',
    gauge_condition_style_tips: `Condition style settings, determine dashboard interval colors, leave blank to disable thresholds, range (0-100), incremental levels<br>Example: input 30,70; this means: divided into 3 segments, namely [0,30], [30,70], [70,100]`,
    light_theme: 'Light Theme',
    dark_theme: 'Dark Theme',
    export_excel: 'Excel',
    export_excel_formatter: 'Excel(with Formatting)',
    export_raw_details: 'Raw Details',
    field_is_empty_export_error: 'No fields available, unable to export',
    chart_symbolic_map: 'Symbolic map',
    symbolic: 'Symbolic',
    symbolic_shape: 'Symbolic Shape',
    symbolic_upload_hint: 'Supports SVG, JPG, JPEG, PNG files within 1MB',
    symbolic_range: 'Range',
    symbolic_error_icon: 'Please select the correct icon file!',
    symbolic_error_size: 'The file size cannot exceed 1MB!',
    symbolic_error_range: 'The second range value must be greater than the first range value',
    chart_stock_line: 'K line',
    liquid_condition_style_tips: `Condition style settings, determine water wave chart interval colors, leave blank to disable thresholds, range (0-100), incremental levels<br>Example: input 30,70; this means: divided into 3 segments, namely [0,30], [30,70], [70,100]`,
    conversion_rate: 'Conversion rate',
    show_extremum: 'Show extremum',
    left_axis: 'Left axis',
    right_axis: 'Right axis',
    no_other_configurable_properties: 'No other configurable properties',
    custom_label_content_tip:
      "Can read field values in the form of ${'{'}fieldName{'}'} (does not support line breaks) ",
    number_of_scales_tip: 'Expected number of axis ticks, not the final result',
    assist_line_settings: 'Auxiliary line settings',
    invalid_field: 'Invalid field',
    k_line_yaxis_tip: 'Open Price-Close Price-Low Price-High Price',
    carousel_enable: 'Enable Carousel',
    carousel_stay_time: 'Stay Duration (seconds)',
    carousel_interval: 'Carousel Interval (seconds)',
    custom_tooltip_content_tip:
      "Can read field values in the form of ${'{'}fieldName{'}'} (Support HTML) ",
    legend_range_division: 'Legend range division',
    legend_equal_range: 'Equal range',
    legend_custom_range: 'Custom range',
    start_coordinates: 'Start coordinates',
    end_coordinates: 'End coordinates',
    start_name: 'Start name',
    end_name: 'End name',
    flow_map_line_width: 'Line Width',
    flow_map_line_width_tip:
      'When this metric is active, the line width property configured in the style will be overridden',
    symbolic_map_coordinates: 'Coordinates',
    symbolic_map_bubble_size_tip:
      'When this metric is active, the size property configured in the base style will be overridden. Additionally, you can configure a size range in the base style',
    point_text: 'Point Text',
    point_bubble_color: 'Point Bubble Color',
    point_bubble_size: 'Point Bubble Size',
    animation_type: 'Animation Type',
    water_wave: 'Water Wave',
    animation_speed: 'Animation Speed',
    wave_rings: 'Wave Rings',
    symbolic_map_symbol_shape: 'Symbol Shape',
    symbolic_map_symbol_shape_tip:
      'When Customizing, Supports SVG, JPG, JPEG, and PNG files up to 1MB',
    size_range: 'Size Range',
    x_axis_constant_line: 'X-axis Constant Line',
    y_axis_constant_line: 'Y-axis Constant Line',
    sort_priority: 'Sort Priority Setting',
    sort_priority_tip: 'Top-down, sorting priority from highest to lowest',
    chart_circle_packing: 'Circle packing chart',
    circle_packing_name: 'Circle name',
    circle_packing_value: 'Circle size',
    circle_packing_border_color: 'Border color',
    circle_packing_border_width: 'Border width',
    circle_packing_padding: 'Circle padding',
    increase: 'Increase',
    decrease: 'Decrease',
    accumulate: 'Accumulate',
    table_cross_bg_tip: 'After merging cells, the zebra pattern is not supported',
    pivot_export_invalid_field:
      'The row dimension or indicator dimension is empty and can not be exported !',
    pivot_export_invalid_col_exceed:
      'Table can not be exported cause the number of columns exceeds the maximum limit!',
    expand_all: 'Expand all',
    level_label: 'Level {num}',
    default_expand_level: 'Default expand level',
    no_data_or_not_positive: 'No data available, or all data are not positive, unable to plot',
    map_type: 'Map Provider',
    map_type_gaode: 'Gaode Map',
    map_type_tianditu: 'Tianditu',
    map_type_baidu: 'Baidu Map',
    map_type_tencent: 'Tencent Map',
    bullet_chart: 'Bullet Chart',
    range_bg: 'Range Background',
    legend_name: 'Legend Name',
    threshold_value: 'Threshold Value',
    range_num: 'Number of Range',
    show_range_bg: 'Show Range Background',
    last_item: 'Last item',
    legend_sort: 'Legend Sort',
    quota_position: 'Quota Position',
    quota_position_col: 'Column',
    quota_position_row: 'Row',
    quota_col_label: 'Quota Column Label',
    table_grand_total_label: 'Total Alias',
    table_field_total_label: 'Field Alias',
    table_row_header_freeze: 'Row Header Freeze'
  },
  dataset: {
    field_value: 'Field Value',
    scope_edit: 'Only effective when editing',
    scope_all: 'Globally effective when previewing datasets',
    spend_time: 'Time spent',
    sql: 'SQL statement',
    sql_result: 'Running result',
    parse_filed: 'Parsing field',
    field_rename: 'Field rename',
    params_work:
      'Only effective when editing: parameter value is only effective when editing the dataset; Globally effective: effective in dataset viewing, previewing, and charts that use the dataset. ',
    select_year: 'Select year',
    sql_variable_limit_1: '1. SQL variables can only be used in WHERE conditions',
    sql_variable_limit_2:
      "2. select * from table where $DE_PARAM{'{'} name = substring('$[PARAM1]',1,5){'}'} and $DE_PARAM{'{'} name in ($[PARAM2]) {'}'}",
    select_month: 'Select month',
    select_date: 'Select date',
    select_time: 'Select time',
    time_year: 'Date-year',
    time_year_month: 'Date-year-month-day',
    time_all: 'Date-year-month-day-hour-minute-second',
    dataset_sync: '(Data synchronization in progress...)',
    sheet_warn: 'There are multiple Sheet pages, the first one is extracted by default',
    datalist: 'Dataset',
    name: 'Dataset name',
    add_group: 'Add group',
    add_scene: 'Add scene',
    group: 'Group',
    scene: 'Scene',
    delete: 'Delete',
    move_to: 'Move to',
    rename: 'Rename',
    tips: 'Tips',
    confirm_delete: 'Confirm deletion',
    confirm_delete_msg:
      'Dataset deletion will affect the custom datasets, associated datasets, and dashboards related to it. Confirm deletion? ',
    delete_success: 'Delete successfully',
    confirm: 'Confirm',
    cancel: 'Cancel',
    search: 'Search',
    back: 'Back',
    add_table: 'Add dataset',
    process: 'Progress',
    update: 'Update',
    db_data: 'Database dataset',
    sql_data: 'SQL dataset',
    excel_data: 'Excel dataset',
    custom_data: 'Custom dataset',
    pls_slc_tbl_left: 'Please select a table from the left',
    add_db_table: 'Add database dataset',
    add_api_table: 'Add API dataset',
    pls_slc_data_source: 'Please select a Datasource',
    table: 'Table',
    edit: 'Edit',
    create_view: 'Create a chart',
    data_preview: 'Data preview',
    field_type: 'Field type',
    field_name: 'Field name',
    field_origin_name: 'Original name',
    field_check: 'Check',
    update_info: 'Update information',
    update_records: 'Update records',
    join_view: 'Data association',
    text: 'Text',
    time: 'Time',
    value: 'Value',
    mode: 'Mode',
    direct_connect: 'Direct connection',
    sync_data: 'Scheduled synchronization',
    update_setting: 'Update settings',
    sync_now: 'Update now',
    add_task: 'Add task',
    task_name: 'Task name',
    task_id: 'Task ID',
    start_time: 'Start time',
    end_time: 'End time',
    status: 'Status',
    error: 'Failed',
    completed: 'Success',
    underway: 'Executing',
    task_update: 'Update settings',
    update_type: 'Update method',
    all_scope: 'Full update',
    add_scope: 'Incremental update',
    select_data_time: 'Select date and time',
    execute_rate: 'Execution frequency',
    execute_once: 'Immediate execution',
    simple_cron: 'Simple repetition',
    cron_config: 'Expression setting',
    no_limit: 'Unlimited',
    set_end_time: 'Set end time',
    operate: 'Operation',
    save_success: 'Save successfully',
    close: 'Close',
    required: 'Required',
    input_content: 'Please enter content',
    add_sql_table: 'Add SQL Dataset',
    preview: 'Preview',
    pls_input_name: 'Please enter a name',
    connect_mode: 'Connection mode',
    incremental_update_type: 'Incremental update method',
    incremental_add: 'Incremental add',
    incremental_delete: 'Incremental delete',
    last_update_time: 'Last update time',
    current_update_time: 'Current update time',
    param: 'Parameter',
    edit_sql: 'Edit SQL Dataset',
    showRow: 'Show row',
    add_excel_table: 'Add Excel Dataset',
    add_custom_table: 'Add custom Dataset',
    upload_file: 'Upload file',
    detail: 'Details',
    type: 'Type',
    create_by: 'Creator',
    create_time: 'Creation time',
    preview_show: 'Show',
    preview_item: 'Items',
    preview_total: 'Total',
    pls_input_less_5: 'Please enter a positive integer within 5 digits',
    field_edit: 'Edit field',
    table_already_add_to: 'The table has been added to',
    uploading: 'Uploading...',
    add_union: 'Create a new association',
    union_setting: 'Association settings',
    pls_slc_union_field: 'Please select the association field',
    pls_slc_union_table: 'Please select the association table',
    source_table: 'Associated table',
    source_field: 'Associated field',
    target_table: 'Associated table',
    target_field: 'Associated field',
    union_relation: 'Associated relationship',
    pls_setting_union_success: 'Please set the association relationship correctly',
    invalid_dataset: 'Kettle is not running, invalid dataset',
    check_all: 'Select all',
    can_not_union_self: 'Associated table cannot be the same as the associated table',
    float: 'Decimal',
    edit_custom_table: 'Edit custom dataset',
    edit_field: 'Edit fields',
    preview_100_data: 'Show the first 100 rows of data',
    invalid_table_check:
      'For non-directly connected datasets, please complete data synchronization first',
    parse_error:
      'Excel parsing failed, please check the format, fields and other information. Specific reference: https://dataease.io/docs/user_manual/dataset_configuration/dataset_Excel',
    origin_field_type: 'Field origin type',
    edit_excel_table: 'Edit Excel dataset',
    edit_excel: 'Edit Excel',
    excel_replace: 'Replace',
    excel_add: 'Append',
    dataset_group: 'Dataset grouping',
    m1: 'Move',
    m2: 'Move to',
    char_can_not_more_50: 'Dataset name cannot exceed 50 characters',
    task_add_title: 'Add task',
    task_edit_title: 'Edit task',
    sync_latter: 'Synchronize later',
    task: {
      list: 'Task list',
      record: 'Execution record',
      create: 'New task',
      name: 'Task name',
      last_exec_time: 'Last execution time',
      next_exec_time: 'Next execution time',
      last_exec_status: 'Last execution result',
      task_status: 'Task status',
      dataset: 'Dataset',
      search_by_name: 'Search by name',
      underway: 'Waiting for execution',
      stopped: 'Execution ended',
      exec: 'Executing',
      pending: 'Pause',
      confirm_exec: 'Manually trigger execution? ',
      change_success: 'State switch successful',
      excel_replace_msg:
        'This may affect custom datasets, associated datasets, dashboards, etc. Are you sure you want to replace it? ',
      effect_ext_field: 'Will affect the calculated field'
    },
    field_group_type: 'Category',
    location: 'Geographic location',
    left_join: 'Left join',
    right_join: 'Right join',
    inner_join: 'Inner join',
    full_join: 'Full join',
    can_not_union_diff_datasource:
      'The associated dataset must be consistent with the Datasource of the current dataset',
    operator: 'Operation',
    d_q_trans: 'Dimension/metric conversion',
    add_calc_field: 'Create a new calculated field',
    input_name: 'Please enter a name',
    field_exp: 'Field expression',
    data_type: 'Data type',
    click_ref_field: 'Click to reference field',
    click_ref_function: 'Click to reference function',
    field_manage: 'Field management',
    edit_calc_field: 'Edit calculated field',
    calc_field: 'Calculate fields',
    show_sql: 'Show SQL',
    ple_select_excel: 'Please re-upload the Excel to be imported.',
    merge: 'Merge',
    no_merge: 'Do not merge',
    merge_msg:
      'There are fields that are consistent in the data table. Do you want to merge them into one Dataset?',
    merge_title: 'Merge data',
    field_name_less_50: 'Field name cannot exceed 50 characters',
    field_name_less_2_64: '2-64 characters',
    excel_info_1: '1. There cannot be merged cells in the Excel file;',
    excel_info_2:
      '2. The first line of the Excel file is the title row, which cannot be empty and cannot be a date type;',
    excel_info_3: '3. Please make sure that the file size is within 500M. ',
    sync_field: 'Synchronize field',
    confirm_sync_field: 'Confirm synchronization',
    confirm_sync_field_tips: 'Synchronize field may cause edited field to change, please confirm',
    sync_success: 'Synchronize successfully',
    sync_success_1:
      'Synchronize successfully, please re-execute data synchronization operation on current Dataset',
    row_permission: {
      type: 'Type',
      name: 'Name',
      condition: 'Condition',
      value: 'Value',
      add: 'Add row permission',
      edit: 'Edit row permission',
      please_select_field: 'Please select field',
      please_select_auth_type: 'Please select authorization type',
      please_select_auth_id: 'Please select authorization target',
      row_permission_not_empty: 'Row permission cannot be empty',
      search_by_filed_name: 'Search by field name',
      auth_type: 'Authorization type',
      auth_obj: 'Authorization object'
    },
    column_permission: {
      add: 'Add column permission',
      edit: 'Edit column permission',
      please_select_field: 'Please select field',
      please_select_auth_type: 'Please select authorization type',
      please_select_auth_id: 'Please select authorization target',
      column_permission_not_empty: 'Column permission cannot be empty',
      auth_type: 'Authorization type',
      auth_obj: 'Authorization object',
      enable: 'Enable',
      disable: 'Disable',
      prohibit: 'Disable',
      desensitization: 'Desensitization',
      desensitization_rule: 'Desensitization rule',
      m: 'M equals',
      n: 'N equals',
      mgtn: 'M cannot be greater than N'
    },
    row_permissions: 'Row permissions',
    column_permissions: 'Column permissions',
    row_column_permissions: 'Row and column permissions',
    union_data: 'Associated Dataset',
    add_union_table: 'Add associated Dataset',
    edit_union: 'Edit associated Dataset',
    union: 'Associate',
    edit_union_relation: 'Edit associated relationship',
    add_union_relation: 'Create a new associated relationship',
    field_select: 'Field selection',
    add_union_field: 'Add associated field',
    union_error: 'Associated relationship and associated field cannot be empty',
    union_repeat: 'The current Dataset has been associated, please do not associate again',
    preview_result: 'Preview results',
    sql_ds_union_error: 'SQL Dataset in direct connection mode, does not support association',
    api_data: 'API Dataset',
    copy: 'Copy',
    sync_log: 'Synchronize logs',
    field_edit_name: 'Field name',
    input_edit_name: 'Please enter the field name',
    edit_search: 'Search by name',
    na: 'None',
    date_format: 'Time format, default: year-month-day hour:minute:second',
    export_dataset: 'Dataset export',
    filename: 'File name',
    export_filter: 'Filter conditions',
    pls_input_filename: 'Please enter the file name',
    calc_tips: {
      tip1: 'The expression syntax should follow the calcite syntax. ',
      tip1_1:
        'The expression syntax should follow the database syntax corresponding to the Datasource. ',
      tip2: 'Aggregation operations are only valid in charts. Displayed as "-" during preview',
      tip3: 'The reference field starts with "[" and ends with "]"',
      tip4: 'Do not modify the reference content, otherwise the reference will fail',
      tip5: 'If you enter content in the same format as the reference field, it will be treated as the reference field',
      tip6: 'Please use the functions supported by Calcite to edit the expression',
      tip7: 'Please use the database function corresponding to the current Datasource to edit the expression',
      tip8: 'For Calcite functions, please refer to the documentation:'
    },
    batch_manage: 'Batch management',
    origin_name: 'Physical field name',
    origin_type: 'Physical field type',
    field_diff: 'The selected field types are inconsistent and do not support conversion',
    create_grouping_field: 'Create a new grouping field',
    editing_grouping_field: 'Editing Grouping Fields',
    grouping_field: 'Grouping field',
    grouping_settings: 'Grouping settings',
    ungrouped_value: 'Ungrouped value',
    please_enter_number: 'Please enter a number'
  },
  deDataset: {
    search_by_name: 'Search by name',
    new_folder: 'Create a new folder',
    search_fields: 'Search fields',
    show_rows: 'Show number of rows',
    display: 'Show',
    row: 'Row',
    restricted_objects: 'Restricted objects',
    select_data_source: 'Select Datasource',
    by_table_name: 'Search by table name',
    run_a_query: 'Run query',
    running_results: 'Run results',
    parameter_type: 'Parameter type',
    run_failed: 'Run failed',
    select_data_table: 'Select data table',
    in_the_file: 'Merged cells cannot exist in the file',
    or_date_type:
      'The first line of the file is the title line, cannot be empty, and cannot be a date type',
    is_within_500m: 'Make sure the Excel file size is within 500M',
    upload_data: 'Upload data',
    excel_data_first: 'Please upload Excel data first',
    is_currently_available: 'No data table is currently available',
    sure_to_synchronize:
      'Synchronizing fields may cause changes to edited fields. Are you sure you want to synchronize? ',
    folder_name: 'Folder name',
    folder: 'Folder to which it belongs',
    edit_folder: 'Edit folder',
    name_already_exists: 'Folder name already exists',
    data_preview: 'Data preview',
    original_name: 'Original name',
    database: 'Database',
    selected: 'Selected:',
    table: 'Table',
    no_dataset_click: 'No dataset yet, click',
    create: 'New',
    new_folder_first: 'Please create a new folder first',
    on_the_left: 'Please select a dataset on the left',
    expression_syntax_error: 'Field expression syntax error',
    create_dashboard: 'Create dashboard',
    cannot_be_empty: 'SQL cannot be empty',
    data_reference: 'Data reference',
    want_to_replace:
      'Replacement may affect custom datasets, associated datasets, dashboards, etc. Do you want to replace? ',
    replace_the_data: 'Are you sure you want to replace the data? ',
    append_successfully: 'Append successfully',
    already_exists: 'Dataset name already exists',
    edit_dataset: 'Edit dataset',
    convert_to_indicator: 'Convert to indicator',
    convert_to_dimension: 'Convert to dimension',
    left_to_edit: 'Select the data table on the left to edit',
    cannot_be_duplicate: 'Dataset names cannot be duplicated',
    set_saved_successfully: 'Dataset saved successfully',
    to_start_using:
      'Browse the contents of your databases, tables and columns. Select a database to get started. ',
    to_run_query: 'Click to run the query',
    the_running_results: 'You can view the running results',
    item: 'Item',
    logic_filter: 'Conditional filter',
    enum_filter: 'Enumeration filter',
    description: 'Field notes'
  },
  about: {
    auth_to: 'Authorized to',
    invalid_license: 'License is invalid',
    update_license: 'Update License',
    expiration_time: 'Expiration time',
    expirationed: '(Expired)',
    auth_num: 'Authorized quantity',
    version: 'Version',
    version_num: 'Version number',
    standard: 'Community Edition',
    enterprise: 'Enterprise Edition',
    Professional: 'Professional Edition',
    Embedded: 'Embedded Edition',
    support: 'Get technical support',
    update_success: 'Update successful, please log in again',
    serial_no: 'Serial number',
    remark: 'Remark',
    back_community: 'Revert to Community Edition',
    confirm_tips: 'Are you sure you want to restore to the community edition? '
  },
  cron: {
    second: 'Seconds',
    minute: 'Minutes',
    hour: 'Hours',
    day: 'Day',
    minute_default: 'Minutes (Execution time: 0 seconds)',
    hour_default: 'Hours (Execution time: 0 minutes and 0 seconds)',
    day_default: 'Day (Execution time: 0 hours, 0 minutes, 0 seconds)',
    month: 'Month',
    week: 'Week',
    year: 'Year',
    d_w_cant_not_set: 'Date and week cannot be "unspecified" at the same time',
    d_w_must_one_set: 'Date and week must be "unspecified"',
    every_day: 'Daily',
    cycle: 'Cycle',
    not_set: 'Unspecified',
    from: 'From',
    to: 'To',
    repeat: 'Cycle',
    day_begin: 'Starting from the day, every',
    day_exec: 'Execute once a day',
    work_day: 'Working day',
    this_month: 'This month',
    day_near_work_day: 'Number, the nearest working day',
    this_week_last_day: 'The last day of this month',
    set: 'Specified',
    every_hour: 'Every hour',
    hour_begin: 'Start at the hour, every',
    hour_exec: 'Execute once every hour',
    every_month: 'Every month',
    month_begin: 'Start at the month, every',
    month_exec: 'Execute once every month',
    every: 'Every',
    every_begin: 'Start, every',
    every_exec: 'Execute once',
    every_week: 'Every week',
    week_start: 'From the week',
    week_end: 'To the week',
    every_year: 'Every year',
    week_tips: 'Note: 1-7 correspond to Sunday-Saturday respectively',
    minute_limit: 'Minutes cannot be less than 1 and greater than 59',
    hour_limit: 'Hours cannot be less than 1 and greater than 23',
    day_limit: 'Days cannot be less than 1 and greater than 31'
  },
  commons: {
    result_count: 'result',
    clear_filter: 'Clear conditions',
    language: 'Language',
    help_center: 'Help Center',
    assistant: 'Assistant',
    test_connect: 'Test connection',
    consanguinity: 'Blood relationship',
    collapse_navigation: 'Collapse navigation',
    operate_cancelled: 'Operation canceled',
    bind: 'Bind ',
    unbind: 'Unbind',
    unlock: 'Unlock',
    unlock_success: 'Unlock successfully',
    uninstall: 'Uninstall',
    parameter_effect: 'Parameter value is only effective when editing the dataset',
    no_result: 'No relevant content found',
    manage_member: 'Manage members',
    confirm_remove_cancel: 'Are you sure you want to delete this role?',
    user_confirm_remove_cancel: 'Are you sure you want to remove this user from the role?',
    default_value: 'Default value',
    params_value: 'Parameter value',
    input_role_name: 'Please enter the role name',
    publish: 'Publish',
    unpublished: 'Unpublish',
    default_pwd: 'Initial password',
    stop: 'Stop',
    first_login_tips: 'You are using the initial password, remember to change it',
    roger_that: 'Got it',
    donot_noti: "Don't prompt again",
    apply: 'Apply',
    search: 'Search',
    folder: 'Directory',
    no_target_permission: 'No permission',
    success: 'Success',
    switch_lang: 'Switch language successfully',
    close: 'Close',
    icon: 'Icon',
    all: 'All',
    enable: 'Enable',
    disable: 'Disable',
    yes: 'Yes',
    no: 'No',
    reset: 'Reset',
    catalogue: 'Catalog',
    button: 'Button',
    gender: 'Gender',
    man: 'Male',
    woman: 'Female',
    keep_secret: 'Secret',
    nick_name: 'Name',
    confirmPassword: 'Confirm password',
    upload: 'Upload',
    cover: 'Overwrite',
    not_cover: 'Not overwrite',
    import_mode: 'Import mode',
    import_module: 'Import module',
    please_fill_in_the_template: 'Please fill in the template content',
    cut_back_old_version: 'Cut back to the old version',
    cut_back_new_version: 'Cut back to the new version',
    comment: 'Comment',
    examples: 'Examples',
    help_documentation: 'Help documentation',
    api_help_documentation: 'API documentation',
    delete_cancelled: 'Cancelled',
    workspace: 'Workspace',
    organization: 'Organization',
    menu: 'Menu',
    setting: 'Settings',
    project: 'Project',
    about_us: 'About',
    current_project: 'Current project',
    name: 'Name',
    description: 'Description',
    annotation: 'Annotation',
    clear: 'Clear',
    save: 'Save',
    otherSave: 'Save as',
    update: 'Update',
    save_success: 'Save successfully',
    delete_success: 'Delete successfully',
    copy_success: 'Copy successfully',
    modify_success: 'Modify successfully',
    delete_cancel: 'Delete cancelled',
    confirm: 'Confirm',
    cancel: 'Cancel',
    prompt: 'Prompt',
    operating: 'Action',
    input_limit: 'Length is {0} to {1} characters',
    login: 'Login',
    welcome: 'One-stop open source data analysis platform',
    username: 'Name',
    password: 'Password',
    input_username: 'Please enter your user name',
    input_password: 'Please enter your password',
    test: 'Test',
    create_time: 'Creation time',
    update_time: 'Update time',
    add: 'Add',
    member: 'Member',
    email: 'Email',
    phone: 'Phone',
    mobile_phone: 'Please enter your mobile number',
    mobile_phone_number: 'Mobile number',
    role: 'Role',
    personal_info: 'Personal Info',
    user_center: 'User Center',
    api_keys: 'API Keys',
    quota: 'Quota Management',
    status: 'Status',
    show_all: 'Show all',
    show: 'Show',
    report: 'Report',
    user: 'User',
    system: 'System',
    personal_setting: 'Personal settings',
    test_resource_pool: 'Test resource pool',
    system_setting: 'System Settings',
    input_content: 'Please enter content',
    create: 'Create',
    edit: 'Edit',
    copy: 'Copy',
    refresh: 'Refresh',
    remark: 'Remark',
    delete: 'Delete',
    reduction: 'Restore',
    not_filled: 'Not filled',
    please_select: 'Please select ',
    search_by_name: 'Search by name',
    personal_information: 'Personal Info',
    exit_system: 'Exit System',
    verification: 'Verification',
    title: 'Title',
    custom: 'Custom',
    select_date: 'Select date',
    months_1: 'January',
    months_2: 'February',
    months_3: 'March',
    months_4: 'April',
    months_5: 'May',
    months_6: 'June',
    months_7: 'July',
    months_8: 'August',
    months_9: 'September',
    months_10: 'October',
    months_11: 'November',
    months_12: 'December',
    weeks_0: 'Sunday',
    weeks_1: 'Monday',
    weeks_2: 'Tuesday',
    weeks_3: 'Wednesday',
    weeks_4: 'Thursday',
    weeks_5: 'Friday',
    weeks_6: 'Saturday',
    system_parameter_setting: 'System Parameters',
    connection_successful: 'Connection successful',
    connection_failed: 'Connection failed',
    save_failed: 'Save failed',
    host_cannot_be_empty: 'Host cannot be empty',
    port_cannot_be_empty: 'Port number cannot be empty',
    account_cannot_be_empty: 'Account cannot be empty',
    remove: 'Remove',
    remove_cancel: 'Remove Cancel',
    remove_success: 'Remove Success',
    tips: 'Authentication information has expired, please log in again',
    not_performed_yet: 'Not yet executed',
    incorrect_input: 'Incorrect input',
    delete_confirm: 'Please enter the following content to confirm deletion:',
    login_username: 'ID or Email',
    input_login_username: 'Please enter user ID or Email',
    input_name: 'Please enter a name',
    please_upload: 'Please upload a file',
    please_fill_path: 'Please fill in the ur path',
    formatErr: 'Format error',
    please_save: 'Please save first',
    reference_documentation: 'Reference document',
    id: 'ID',
    millisecond: 'Millisecond',
    cannot_be_null: 'Cannot be empty',
    required: 'Required',
    already_exists: 'Name cannot be repeated',
    modifier: 'Modifier',
    validate: 'Validation',
    batch_add: 'Batch add',
    tag_tip: 'Enter to add tags',
    search_keywords: 'Enter keyword search',
    table: {
      select_tip: '{0} data items have been selected'
    },
    date: {
      select_date: 'Select date',
      start_date: 'Start date',
      end_date: 'End date',
      select_date_time: 'Select date time',
      start_date_time: 'Start date time',
      end_date_time: 'End date time',
      range_separator: 'To',
      data_time_error: 'Start date cannot be greater than end date',
      one_hour: 'One hour',
      one_day: 'One day',
      one_week: 'One week',
      one_month: 'One month',
      permanent: 'Permanent',
      one_year: 'One year',
      six_months: 'Half a year',
      three_months: 'Three months',
      of_range_1_59: 'Minutes out of range [1-59]',
      of_range_1_23: 'Hours out of range [1-23]'
    },
    adv_search: {
      title: 'Advanced search',
      combine: 'Combined query',
      test: 'Belonging test',
      project: 'Belonging project',
      search: 'Query',
      reset: 'Reset',
      and: 'All',
      or: 'Any one',
      operators: {
        is_empty: 'Empty',
        is_not_empty: 'Not empty',
        like: 'Contains',
        not_like: 'Does not contain',
        in: 'Belongs to',
        not_in: 'Does not belong to',
        gt: 'Greater than',
        ge: 'Greater than or equal to',
        lt: 'Less than',
        le: 'Less than or equal to',
        equals: 'Equals',
        not_equals: 'Not equal to',
        between: 'Between',
        current_user: 'Is the current user'
      },
      message_box: {
        alert: 'Warning',
        confirm: 'Confirm'
      }
    },
    monitor: 'Monitor',
    image: 'Image',
    tag: 'Tag',
    module: {
      select_module: 'Select module',
      default_module: 'Default module'
    },
    datasource: 'Datasource',
    char_can_not_more_50: 'Cannot exceed 50 characters',
    char_2_64: '2-64 characters',
    char_1_64: '1-64 characters',
    share_success: 'Share successfully',
    input_id: 'Please enter ID',
    input_pwd: 'Please enter password',
    message_box: {
      alert: 'Warning',
      confirm: 'Confirm',
      ok: 'Confirm',
      cancel: 'Cancel'
    },
    ukey_title: 'API Keys',
    thumbnail: 'Thumbnail',
    confirm_delete: 'Confirm deletion',
    delete_this_dashboard: 'Confirm to delete this dashboard?',
    delete_this_folder: 'Confirm to delete this folder?',
    confirm_stop: 'Confirm to stop',
    stop_success: 'Stop successful',
    treeselect: {
      no_children_text: 'No child nodes',
      no_options_text: 'No available options',
      no_results_text: 'No matching results'
    },
    char_count_limit: 'Cannot exceed {count} characters'
  },
  sql_variable: {
    variable_mgm: 'Parameter settings'
  },
  v_query: {
    display_sort: 'Display fields and sort fields are inconsistent, custom sorting is not possible',
    custom_sort: 'Custom sorting',
    msg_center: 'Message center',
    to_be_filled: 'To be filled',
    the_minimum_value:
      'The maximum value of the numerical interval must be greater than or equal to the minimum value',
    before_querying:
      'The query condition is required, please set the option value before querying! ',
    here_or_click: 'Drag the field on the right here or click',
    add_query_condition: 'Add query condition',
    set_filter_condition: 'Set filter condition',
    delete_condition: 'Delete condition',
    last_3_months: 'Last 3 months',
    last_6_months: 'Last 6 months',
    last_12_months: 'Last 12 months',
    last_3_days: 'Last 3 days',
    month_to_date: 'Month to date',
    year_to_date: 'Year to date',
    exact_match: 'Exact',
    fuzzy_match: 'Fuzzy',
    option_type: 'Option type',
    time_filter_range: 'Set time filter range',
    configured: 'Configured',
    is_not_supported: 'After binding parameters, empty data is not supported',
    contains_empty_data: 'The option value contains empty data',
    unnamed: 'Unnamed',
    cannot_be_empty: 'The query condition or field cannot be empty',
    the_first_level: 'No need to configure the cascaded fields at the first level',
    configure_cascaded_fields:
      'Use the same Dataset as the previous level, no need to configure the cascaded fields',
    condition_cascade_configuration: 'Query condition cascade configuration',
    not_reverse_cascade: '(Only the upper level can cascade the lower level, not reverse cascade)',
    must_be_met:
      "Based on  the current component's condition, if cascade configuration is required, the following conditions must be met:",
    select_data_set:
      '1. Display type: text and number drop-down component; 2. Option value source: select Dataset',
    add_cascade_configuration: 'Add cascade configuration',
    add_cascade_condition: 'Add cascade condition',
    query_condition_level: 'Level',
    select_query_condition: 'Please select the query condition',
    select_cascaded_field: 'Please select the cascaded field',
    level_1: 'Level {msg}',
    to_modify_it:
      'Modification of the dataset will cause the cascade configuration to become invalid, so the corresponding cascade relationship will be cleared. Are you sure to modify it? ',
    be_linked_first: 'Please check the charts and fields that need to be linked first',
    cannot_be_performed:
      'The selected field types are inconsistent and query configuration cannot be performed',
    numerical_parameter_configuration:
      'The numerical parameter configuration must configure the maximum and minimum values',
    format_is_inconsistent: 'The time format is inconsistent',
    cannot_be_empty_de: 'The query condition is required and the default value cannot be empty',
    the_start_time: 'The end time must be greater than the start time!',
    and_end_time: 'The time parameter configuration must configure the start time and end time',
    cannot_be_empty_time: 'The default time cannot be empty!',
    range_please_reset: 'The default value exceeds the date filter range, please reset it! ',
    cannot_be_empty_input: 'Manual input - option value cannot be empty',
    option_value_field: 'Please select the Dataset and option value field',
    the_data_set: 'Please select the option value field of the Dataset',
    cannot_be_empty_name: 'Field name cannot be empty',
    query_condition_setting: 'Query condition setting',
    query_condition: 'Query condition',
    chart_and_field: 'Select the associated chart&field',
    be_switched_to:
      'Note: Automatic mode supports automatic association of fields with the same Dataset, which can be switched to',
    to_automatic_again:
      'Custom mode. After switching to custom mode, it cannot be switched to automatic again! ',
    as_query_conditions: 'Anonymous field, cannot be set as a query condition',
    query_condition_configuration: 'Query condition configuration',
    required_items: 'Required items',
    display_type: 'Display type',
    text_drop_down: 'Text drop-down',
    text_search: 'Text search',
    drop_down_tree: 'Drop-down tree',
    number_drop_down: 'Number drop-down',
    number_range: 'Number range',
    of_option_values: 'Number of option values',
    tree_structure_design: 'Structure design',
    the_tree_structure: 'Click to design the tree structure',
    time_granularity: 'Time granularity',
    the_time_granularity: 'Please select the time granularity',
    option_value_source: 'Option value source',
    manual_input: 'Manual input',
    query_field: 'Query field',
    display_field: 'Display field',
    the_sorting_field: 'Please select the sorting field',
    condition_type: 'Condition',
    single_condition: 'Single',
    with_condition: 'And',
    or_condition: 'Or',
    hide_condition_switch: 'Hide condition switch',
    cannot_be_displayed:
      'The chart uses a different Dataset, Unable to display configuration items',
    component_cascade_configuration: 'Query component cascade configuration',
    time_type: 'Time type',
    start_at: 'Start at',
    end_at: 'End at',
    time_interval: 'Time interval',
    interval_type: 'Interval type',
    query_time_window: 'Dynamic query time window',
    maximum_single_query: 'Maximum single query',
    empty_data: 'Empty data',
    time_selection: 'Time selection',
    select_a_field: 'Level field cannot be empty, please select a field!',
    add_level: 'Add level',
    tree_query_field: 'Drop-down tree query field',
    query_condition_width: 'Query condition width',
    custom_condition_style: 'Custom condition style'
  },
  panel: {
    column_name: 'Field name'
  },
  visualization: {
    support_query: 'Only query components can be added',
    publish_update_tips: 'Update available',
    filter_freeze_tips:
      'A pinned query component already exists. Confirm switching to this component?',
    query_position: 'Query Component Position',
    default: 'Default',
    to_top: 'Pin to Top',
    publish_recover: 'Revert Publish',
    publish_tips1: 'Visible after publication',
    publish_tips2: 'Available after publication {0}',
    cancel_publish_tips: 'Successfully unpublished',
    resource_not_published: 'Resource not published',
    re_publish: 'Republish',
    published_success: 'Published successfully',
    cancel_publish: 'Cancel Publish',
    publish: 'Publish',
    freeze_top: 'Position frozen at the top',
    indicator_linkage: 'Indicator card linkage only carries chart filtering parameters',
    gap_size: 'Gap Size',
    small: 'Small',
    middle: 'Medium',
    large: 'Large',
    no_details: 'No Data',
    sync_pc_design: 'Synchronize PC Design',
    title_background: 'Title Background',
    active_title_background: 'Active Title Background',
    reuse_active_title_background: 'Reuse Active Title Background',
    inactive_title_background: 'Inactive Title Background',
    no_hidden_components: 'No Hidden Components',
    hidden_components: 'Hidden Components',
    dashboard_adaptor: 'Zoom Mode',
    scale_keep_height_and_width: 'Canvas Ratio',
    scale_with_width: 'Component Ratio',
    multi_selected: '{0} components selected',
    number1: '1',
    number2: '2',
    number3: '3',
    number4: '4',
    number5: '5',
    number6: '6',
    number7: '7',
    jump_null_tips:
      'The field [{0}] has empty configuration. Please complete the configuration first!',
    jump_no_banding_tips: 'The current chart has no bound query conditions.',
    set_as_tips: 'Set as',
    rich_text_tips: 'Double click to enter text',
    save_conflict_tips: 'has been updated by others, overwrite and save?',
    text_decoration: 'underline',
    select_target_resource: 'Please select the target resource',
    target_dashboard_dataV: 'Target Dashboard/Screen',
    dashboard_dataV: 'Dashboard/Data Screen',
    effective_during_link: 'Public link active',
    condition_style_set: 'Condition Style Settings',
    cell_merge_tips:
      'After merging cells, row/column freezing and automatic line wrapping will be disabled.',
    image: 'Image',
    drill_set_tips: 'Drill-down has been set',
    input_calc_data: 'Enter calculation data',
    excel_with_format: 'Excel (with format)',
    show_data_info: 'View Data',
    sort: 'Sort',
    add_query_filter: 'Add Query Condition',
    edit_query_filter: 'Edit Query Condition',
    jump_set_tips: 'Redirection has been set',
    tips_world: 'Tip Words',
    query_name_space2: 'Spacing between name and selection box',
    button_color: 'Button Color',
    button_text: 'Button Text',
    show_button: 'Show Button',
    query_tips:
      'If the query button is displayed, the chart query will be triggered only after clicking the button. If not displayed, the query is triggered immediately after selecting the query conditions.',
    custom_query_bg_color: 'Custom Query Background Color',
    query_condition_space: 'Query Condition Spacing',
    query_condition_height: 'Query condition height',
    query_condition_name: 'Query Condition Name',
    condition_left: 'Left Side',
    condition_top: 'Top Side',
    custom_bg_color: 'Custom Component Background',
    background_img: 'Background Image',
    back_parent: 'Back to Parent',
    ext_fullscreen: 'Exit Fullscreen',
    no_edit_auth: 'No edit permissions for the target resource, please contact the administrator!',
    select_target_dashboard_tips: 'Please select the target dashboard',
    select_target_screen_tips: 'Please select the target data screen',
    cur_dashboard: 'Current dashboard',
    cur_screen: 'Current data screen',
    target_dashboard: 'Target dashboard',
    target_screen: 'Target data screen',
    component_id: 'Component ID',
    view_id: 'Chart ID',
    resource_create_tips: 'Select components from the top toolbar and add them here to create.',
    component_select_tips: 'Please elect a component...',
    border_style_dotted: 'Dotted',
    app_export: 'App Export',
    app_name: 'App Name',
    app_version: 'App Version',
    app_required_version: 'Minimum DataEase Version',
    description: 'Description',
    new_dataset: 'New Dataset',
    new_datasource: 'New Datasource',
    select_dataset: 'Select Dataset',
    select_datasource: 'Select Datasource',
    picture_group: 'Picture Group',
    new: 'New',
    new_folder: 'New Folder',
    new_screen: 'New Data Screen',
    new_dashboard: 'New Dashboard',
    new_from_template: 'New from Template',
    folder: 'Folder',
    copy: 'Copy',
    move_to: 'Move to',
    rename: 'Rename',
    name: 'Name',
    name_repeat: 'Name Repeat',
    input_name_tips: 'Please enter the {0} name',
    select_target_folder: 'Please select the target folder',
    select_target_tips: 'Cannot select itself, please select another folder',
    input_tips: 'Please enter the name',
    position: 'Position',
    ds_group_name: 'Dataset Group Name',
    ds_group_position: 'Dataset Group Position',
    datasource_info: 'Datasource Information',
    app_datasource: 'Application Datasource',
    sys_datasource: 'System Datasource',
    select_folder: 'Please select the associated folder',
    belong_folder: 'Belonging Folder',
    no_content: 'No relevant content found',
    confirm: 'Confirm',
    cancel: 'Cancel',
    select_ds_group_folder: 'Please select the folder for the dataset group',
    app_no_datasource_tips: 'There are unconfigured datasources',
    dataset: 'Dataset',
    delete: 'Delete',
    delete_success: 'Delete successful',
    save_success: 'Save successful',
    change_save_tips: 'The current changes have not been saved, are you sure you want to exit?',
    mobile_config: 'Mobile Configuration',
    visualization_component: 'Visualization Component',
    component_style: 'Component Style',
    whole_style: 'Overall Style',
    time_asc: 'Ascending by Time',
    time_desc: 'Descending by Time',
    name_asc: 'Ascending by Name',
    name_desc: 'Descending by Name',
    delete_tips:
      'After deletion, all resources under this folder will be deleted, please operate with caution.',
    delete_warn: 'Are you sure you want to delete this {0}?',
    save_app: 'Save Application',
    base_info: 'Basic Information',
    close: 'Close',
    adapt_new_subject: 'Adapt to New Theme',
    export_tips:
      'The {0} in the current dashboard belongs to a template chart and cannot be exported. Please set the dataset first!',
    preview_select_tips: 'Please select a dashboard on the left',
    have_none_resource: 'No dashboards available',
    attribute: 'Attribute',
    dashboard_configuration: 'Dashboard Configuration',
    batch_style_set: 'Batch Style Setting',
    pic_import_tips: 'Supports JPG, PNG, GIF, SVG, size not exceeding {0}',
    pic_size_error: 'The image size cannot exceed 15MB',
    re_upload: 'Re-upload',
    screen_configuration: 'Screen Configuration',
    mobile_ios_tips: 'May not display on IOS',
    layer_management: 'Layer Management',
    hold_canvas_tips: 'Hold spacebar to drag the canvas',
    keep_subject: 'Keep Original Style',
    select_component: 'Select Component',
    no_available_component: 'No available components currently',
    no_selected_component: 'Currently selected component',
    no_params_tips: 'Parameters cannot be empty',
    cancel_store: 'Cancel Favorite',
    creator: 'Creator',
    fullscreen: 'Fullscreen',
    edit: 'Edit',
    refresh_data: 'Refresh Data',
    export_as: 'Export As',
    select_screen_tips: 'Please select a data screen on the left',
    no_screen: 'No data screens available',
    query: 'Query',
    carousel: 'Carousel',
    carousel_time: 'Carousel Time(s)',
    carousel_tips: 'Carousel takes effect after exiting edit mode',
    carousel_tips2: 'Carousel will be disabled after enabling conditional styling',
    background: 'Background',
    tab_title: 'Tab Title',
    style: 'Style',
    event: 'Event',
    board: 'Border',
    color: 'Color',
    board_width: 'Border Width',
    board_radius: 'Border Radius',
    enable_event_binding: 'Enable Event Binding',
    event_binding_tips:
      'Event bindings will take effect after exiting edit mode. When rich text have event binding enabled, their internal click events will be disabled.',
    input_url_tips: 'Please enter the redirect URL',
    edit_title: 'Edit Title',
    custom_sort: 'Custom Sort',
    show_date: 'Show Date',
    show_time: 'Show Time',
    show_week: 'Show Week',
    link_info: 'Link Information',
    pic_upload_tips: 'Please upload an image...',
    pic_group: 'Image Group',
    pic_upload_tips2: 'Supports JPG, PNG, GIF, SVG',
    pic_adaptor_type: 'Image Adaptation Method',
    pop_area_tips:
      'Click or drag the query component to this position, click preview to view the pop-up area',
    view: 'View',
    query_component: 'Query',
    media: 'Media',
    more: 'More',
    source_material: 'Material',
    text_html: 'Text',
    external_parameter_settings: 'External Parameter Settings',
    screen_config: 'Screen Configuration',
    screen_adaptor: 'Scaling Method',
    screen_adaptor_width_first: 'Width First',
    screen_adaptor_height_first: 'Height First',
    screen_adaptor_full: 'Full Screen',
    screen_adaptor_keep: 'No Scaling',
    effective_during_preview: 'Effective during preview',
    base_config: 'Base Configuration',
    color_config: 'Color Configuration',
    refresh_config: 'Refresh Configuration',
    advanced_style_settings: 'Advanced Style Settings',
    size: 'Size',
    font_family_select: 'Dashboard Font Selection',
    screen_font_family_select: 'Data Screen Font Selection',
    button_tips: 'Display floating buttons',
    display_auxiliary_grid: 'Display Auxiliary Grid',
    show_pop_button: 'Show Pop-up Area Query Button',
    show_zoom_button: 'Show floating buttons',
    keep_ratio: 'Keep Aspect Ratio',
    rotation_3d: '3D Rotation',
    keep_size: 'Adjust size & keeping inner',
    no_save_tips: 'There are unsaved {0}',
    no_save_tips2: 'There are unsaved changes, restore now?',
    locate_tips: 'Locate to Center',
    new_tab: 'New Tab',
    pop_area: 'Pop Up Area',
    refresh_view: 'Refresh View',
    view_group: 'Group',
    video: 'Video',
    stream_media: 'Stream Media',
    web: 'Web',
    time_component: 'Time Component',
    picture: 'Picture',
    icon: 'Icon',
    rect_shape: 'Rectangle',
    circle_shape: 'Circle',
    triangle: 'Triangle',
    tabs: 'Tabs',
    scroll_text: 'Marquee',
    component_input_tips: 'Double-click to edit text',
    screen_area: 'Screen Area',
    rich_text: 'Rich Text',
    date_time: 'Date & Time',
    board_name: 'Border {0}',
    graphic: 'Graphic',
    selected_tips: '{0} items selected',
    params_list: 'Parameter List',
    params: 'Parameters',
    no_setting_params_name_tip: 'Parameter name not configured',
    select_params_connect_component: 'Select parameter-associated component',
    connection_condition: 'Connection Condition',
    connection_params_fields: 'Connected Fields or Parameters',
    fields: 'Fields',
    select_all: 'Select All',
    select_params_connect_view: 'Select associated chart',
    setting_params_tips: 'Please configure the parameters',
    setting_params: 'Parameter Configuration',
    required: 'Required',
    default_value: 'Default Value',
    default_value_tips1: 'Please use JSON array format Example:',
    default_value_tips2: 'Single value ["name1"], Multiple values ["name1","name2"]',
    default_value_tips3: 'Please enter parameters, e.g.: ["name1"]',
    time_year_widget: 'Year Filter Widget',
    time_month_widget: 'Month Filter Widget',
    time_date_widget: 'Date Filter Widget',
    time_date_range_widget: 'Date Range Filter Widget',
    text_select_widget: 'Text Dropdown Filter Widget',
    text_select_grid_widget: 'Text List Filter Widget',
    text_input_widget: 'Text Search Filter Widget',
    text_select_tree_widget: 'Dropdown Tree Filter Widget',
    number_select_widget: 'Number Dropdown Filter Widget',
    number_select_grid_widget: 'Number List Filter Widget',
    number_range_widget: 'Number Range Filter Widget',
    format_error: 'Format Error',
    params_setting_check_message: 'The default value format for parameter {0} is incorrect!',
    params_setting_check_message_tips:
      'There are unconfigured parameter names or duplicate parameter names!',
    already_setting: 'Already Set',
    bubble_dynamic_effect: 'Bubble Dynamic Effect',
    save_page_tips: 'Please save the current page first',
    selected_view: 'Selected View',
    used_dataset: 'Used Dataset',
    to_select_view: 'Select View',
    show_selected_only: 'Show Selected Only',
    same_dataset: 'Same Dataset',
    diff_dataset: 'Different Dataset',
    no_available_view: 'No available views currently',
    linkage_setting_tips1: 'Configure the field association relationship between charts',
    current_chart_source_field: 'Current Chart Source Field',
    add_linkage_dependency_fields: 'Add Linkage Dependency Fields',
    select_linkage_tips: 'Please first select the charts that need linkage',
    linkage_option_tips1:
      'If the linkage dimension has been configured for drilling, clicking the dimension will',
    linkage_option1: 'Pop up a floating box for the user to choose linkage or drilling',
    linkage_option2: 'Simultaneously Trigger Linkage and Drill-down',
    window_size: 'Window Size',
    window_size_large: 'Large',
    window_size_middle: 'Medium',
    window_size_small: 'Small',
    target: 'Target',
    linkage_view: 'Linked View',
    with_filter_params: 'With Filter Parameters',
    source_field: 'Source Field',
    source_filter: 'Source Filter',
    link_target_tips1:
      'The target dashboard has no external parameters, so conditional queries cannot be carried out. If needed,',
    link_target_tips2: 'Please go to the settings for external parameters.',
    link_outer_params: 'Linked External Parameters',
    indicator_name: 'Indicator Name',
    component_size: 'Size',
    component_annotation: 'Annotation',
    alignment: 'Alignment',
    left_justifying: 'Left Justified',
    right_justifying: 'Right Justified',
    top_justifying: 'Top Justified',
    bottom_justifying: 'Bottom Justified',
    horizontally_centered: 'Horizontally Centered',
    vertically_centered: 'Vertically Centered',
    cancel_group: 'Cancel Group',
    move_to_screen_show: 'Move to Screen Display Area',
    move_to_pop_area: 'Move to Screen Popup Area',
    hidden: 'Hidden',
    cancel_hidden: 'Cancel Hidden',
    template_view_tips: 'This is currently a template chart. Please replace the dataset...',
    download: 'Download',
    refresh: 'Refresh',
    head_font_color: 'Header Font Color',
    head_font_active_color: 'Active Font Color',
    head_border_color: 'Header Border Color',
    head_border_active_color: 'Active Border Color',
    underline_height: 'Underline Height',
    background_color: 'Background Color',
    active_font_size: 'Active Font Size',
    scroll_speed: 'Scroll Speed',
    out_params_no_select: 'External parameters do not need to be selected',
    filter_no_select: 'Filter components do not need to be selected',
    forbidden_copy: 'Copying of the current component is not allowed',
    url_check_error: 'Redirect error, invalid URL',
    view_style: 'Chart Style',
    view_color_setting: 'Chart Color Setting',
    border_color_setting: 'Border Color Setting',
    unpublished_tips:
      'After canceling publication, this dashboard cannot be viewed. Are you sure you want to cancel publication?',
    position_adjust_component: 'Position Adjustment',
    enable_carousel: 'Enable Carousel',
    switch_time: 'Switch Time',
    position_adjust: 'Position Adjustment',
    space_top: 'Top',
    space_left: 'Left',
    space_width: 'Width',
    space_height: 'Height',
    down: 'Download',
    mobile_style_setting: 'Style Setting',
    mobile_style_setting_tips: 'Customize mobile background',
    text: 'Text',
    board_background: 'Background',
    title_color: 'Title Color',
    input_style: 'Input Box Style (Color)',
    overall_setting: 'Overall Configuration',
    panel_background: 'Dashboard Background',
    component_color: 'Component Color',
    chart_title: 'Chart Title',
    filter_component: 'Query',
    enable_refresh_view: 'Enable Refresh',
    enable_view_loading: 'Chart Loading Prompt',
    image_size_tips: 'Please ensure images are no larger than 15MB',
    image_add_tips: 'Only images can be inserted',
    watermark: 'Watermark',
    panel_get_data_error:
      'Failed to retrieve dashboard information. The dashboard may have been deleted. Please check the dashboard status.',
    panel_no_save_tips: 'There are unsaved dashboards.',
    panel_cache_use_tips:
      'We detected that a dashboard failed to save properly last time. Would you like to use the unsaved version?',
    template_name_tips: 'Dashboard name is required.',
    panel_background_item: 'Custom Dashboard Background',
    panel_background_image_tips: 'Supports JPG, PNG, GIF, SVG',
    reUpload: 'Re-upload',
    create_by: 'Created By',
    create_time: 'Creation Time',
    update_by: 'Last Modified By',
    update_time: 'Last Modified Time',
    target_url: 'Target URL',
    target_url_tips: 'Click on fields to concatenate URLs or parameters.',
    select_world: 'Click to Reference Field',
    template_market: 'Template Market',
    template_preview: 'Preview Template',
    apply: 'Apply',
    apply_this_template: 'Apply This Template',
    market_network_tips:
      'To view templates from the template market, your server must be connected to the template market ({0}). Please check your network connection...',
    enter_name_tips: 'Please enter the dashboard name.',
    apply_template: 'App Template',
    style_template: 'Style Template',
    all_type: 'All Categories',
    enter_template_name_tips: 'Search template name',
    pic_adaptation: 'Image Adaptation',
    pic_equiratio: 'Equal Ratio Adaptation',
    pic_original: 'Original Size',
    pic_size: 'Image Size',
    web_addr: 'Web Address',
    stream_media_info: 'Streaming Media Information',
    video_info: 'Video Information',
    title_position: 'Title Position',
    tab_inner_style: 'Tab Inner Style',
    data_format: 'Date Format',
    border_color: 'Border Color',
    theme_change_warn: 'Theme Change',
    theme_change_tips:
      'Changing the theme will overwrite related chart theme properties. It is recommended to back up your settings before proceeding. Do you want to continue?',
    theme_color_change_warn: 'Theme Color Change',
    theme_color_change_tips: 'Changing the theme color will overwrite existing chart properties.',
    theme_color: 'Theme Color',
    theme_color_dark: 'Dark',
    theme_color_light: 'Light',
    refresh_frequency: 'Refresh Frequency',
    card_color_matching: 'Card Color Matching',
    table_color_matching: 'Table Color Matching',
    level: 'Level',
    enlarge: 'Enlarge',
    panel_style: 'Dashboard Style',
    multiplexing: 'Reuse',
    panel_off: 'Dashboard is Offline',
    batch_opt: 'Batch Operations',
    cancel_batch_opt: 'Exit Batch Operations',
    edit_leave_tips: 'Do you want to leave the editing interface without saving?',
    hyperlinks: 'Hyperlinks',
    is_live: 'Is Live',
    yes: 'Yes',
    no: 'No',
    live_tips: 'Prefer HTTPS links',
    stream_media_add_tips: 'Please add streaming media information...',
    stream_mobile_tips: 'May not be displayed on IOS devices',
    json_params_error: 'Failed to parse third-party parameters. Please check the parameter format.',
    inner_padding: 'Inner Padding',
    board_radio: 'Corners',
    web_set_tips: 'Some websites may not allow embedding and will not display.',
    repeat_params: 'Duplicate parameter names exist.',
    enable_outer_param_set: 'Enable External Parameter Settings',
    select_param: 'Please select a parameter...',
    add_param_link_field: 'Add Parameter Linked Field',
    add_param: 'Add Parameter',
    enable_param: 'Enable Parameter',
    param_name: 'Parameter Name',
    outer_param_set: 'External Parameter Settings',
    outer_param_decode_error:
      'External parameter parsing error. Please encode parameters as specified.',
    input_param_name: 'Please enter the parameter name.',
    params_setting: 'External Parameter Settings',
    edit_web_tips: 'Webpage content cannot be edited in edit mode.',
    no_auth_role: 'No Shared Roles',
    auth_role: 'Shared Roles',
    picture_limit: 'Only images can be inserted.',
    drag_here: 'Please drag fields from the left to here.',
    copy_link_passwd: 'Copy Link and Password',
    copy_link: 'Copy Link',
    copy_short_link: 'Copy Short Link',
    copy_short_link_passwd: 'Copy Short Link and Password',
    passwd_protect: 'Password Protection',
    auto_pwd: 'Auto-generate Password',
    link: 'Link',
    over_time: 'Expiration Date',
    link_expire: 'The link has expired!',
    link_share: 'Link Sharing',
    link_share_desc: 'After enabling link sharing, anyone can access the dashboard via this link.',
    share: 'Share',
    remove_share_confirm: 'Are you sure you want to remove all shares for this dashboard?',
    share_in: 'Shared With Me',
    share_out: 'My Shares',
    who_share: 'Shared By',
    when_share: 'Share Date',
    share_to: 'Share To',
    share_to_some: 'Share [{some}] With',
    org: 'Organization',
    role: 'Role',
    user: 'User',
    datalist: 'Chart List',
    group: 'Category',
    panel: 'Dashboard',
    panel_list: 'Dashboards',
    groupAdd: 'New Category',
    panelAdd: 'New Dashboard',
    import: 'Import Template',
    tips: 'Tips',
    confirm_delete: 'Confirm Deletion',
    search: 'Search',
    back: 'Back',
    module: 'Component',
    filter_module: 'Filter Component',
    select_by_module: 'Select by Component',
    sys_template: 'System Template',
    user_template: 'User Template',
    add_category: 'Add Category',
    add_app_category: 'Add Application Category',
    filter_keywords: 'Filter by Keywords',
    dashboard_theme: 'Dashboard Theme',
    table: 'Table',
    gap: 'With Gap',
    no_gap: 'No Gap',
    component_gap: 'Component Gap',
    refresh_time: 'Refresh Time',
    minute: 'Minute',
    second: 'Second',
    photo: 'Photo',
    default_panel: 'Default Dashboard',
    create_public_links: 'Create Public Links',
    to_default: 'Save as Default',
    to_default_panel: 'Save as Default Dashboard',
    store: 'Favorite',
    save_to_panel: 'Save as Template',
    export_to_panel: 'Export as Template',
    export_to_pdf: 'Export to PDF',
    export_to_img: 'Export to Image',
    export_to_app: 'Export to App',
    preview: 'Preview',
    fullscreen_preview: 'Fullscreen',
    new_tab_preview: 'New Tab',
    select_panel_from_left: 'Please select a dashboard from the left',
    template_name: 'Template Name',
    template: 'Template',
    category: 'Category',
    all_org: 'All Organizations',
    custom: 'Custom',
    import_template: 'Import Template',
    copy_template: 'Reuse Template',
    upload_template: 'Upload Template',
    belong_to_category: 'Belonging Category',
    pls_select_belong_to_category: 'Please select a category',
    template_name_cannot_be_empty: 'Template name cannot be empty',
    select_by_table: 'Select by Table',
    data_list: 'Data List',
    component_list: 'Component List',
    custom_scope: 'Control Scope',
    binding_parameters: 'Parameters',
    multiple_choice: 'Multiple Choice',
    single_choice: 'Single Choice',
    field: 'Field',
    unshared_people: 'Unshared People',
    shared_people: 'Shared People',
    error_data: 'Error retrieving data, please contact the administrator',
    canvas_size: 'Canvas Size',
    canvas_scale: 'Canvas Scale',
    clean_canvas: 'Clear Canvas',
    insert_picture: 'Insert Picture',
    redo: 'Redo',
    undo: 'Undo',
    panelNull: 'This is an empty dashboard, you can enrich its content through editing',
    paste: 'Paste',
    cut: 'Cut',
    lock: 'Lock',
    unlock: 'Unlock',
    top_component: 'Move to Top Layer',
    bottom_component: 'Move to Bottom Layer',
    up_component: 'Move Up One Layer',
    down_component: 'Move Down One Layer',
    linkage_setting: 'Linkage Setting',
    add_tab: 'Add Tab',
    open_aided_design: 'Open Component Aided Design',
    close_aided_design: 'Close Component Aided Design',
    open_style_design: 'Open Style Design',
    close_style_design: 'Close Style Design',
    matrix_design: 'Matrix Design',
    left: 'X Coordinate',
    top: 'Y Coordinate',
    height: 'Height',
    width: 'Width',
    backgroundColor: 'Background Color',
    borderStyle: 'Border Style',
    borderWidth: 'Border Width',
    borderColor: 'Border Color',
    borderRadius: 'Border Radius',
    font_size: 'Font Size',
    fontWeight: 'Font Weight',
    lineHeight: 'Line Height',
    letter_spacing: 'Letter Spacing',
    padding: 'Padding',
    margin: 'Margin',
    textAlign: 'Text Alignment',
    opacity: 'Opacity',
    background_opacity: 'Background Opacity',
    verticalAlign: 'Vertical Alignment',
    text_align_left: 'Left Align',
    text_align_center: 'Center Align',
    text_align_right: 'Right Align',
    vertical_align_top: 'Top Align',
    vertical_align_middle: 'Middle Align',
    vertical_align_bottom: 'Bottom Align',
    border_style_solid: 'Solid',
    border_style_dashed: 'Dashed',
    other_module: 'Other',
    content: 'Content',
    default_panel_name: 'Default Dashboard Name',
    source_panel_name: 'Original Dashboard Name',
    content_style: 'Content Style',
    canvas_self_adaption: 'Canvas Self-Adaptation',
    panel_save_tips: 'The dashboard has been modified, do you want to save?',
    panel_save_warn_tips: 'If not saved, your changes to the dashboard will be lost!',
    do_not_save: 'Do Not Save',
    save: 'Save',
    drill: 'Drill Down',
    linkage: 'Linkage',
    linkage_and_drill: 'Linkage and Drill Down',
    jump: 'Jump',
    cancel_linkage: 'Cancel Linkage',
    switch_picture: 'Switch Picture',
    select_field: 'Select Chart Field',
    remove_all_linkage: 'Remove All Linkages',
    exit_un_march_linkage_field: 'Linkage Field cannot be empty',
    details: 'Details',
    setting: 'Setting',
    no_drill_field: 'Missing Linkage Field',
    matrix: 'Matrix',
    suspension: 'Suspension',
    new_element_distribution: 'Element Distribution Method',
    aided_grid: 'Aided Design Grid',
    aided_grid_open: 'Open',
    aided_grid_close: 'Close',
    export_pdf_page: 'PDF Page Break',
    export_pdf_page_remark: 'Only effective for API-exported dashboard PDF pagination',
    subject_no_edit: 'System theme cannot be edited',
    subject_name_not_null: 'Theme name must be 1-20 characters',
    is_enable: 'Enable',
    open_mode: 'Open Mode',
    new_window: 'Open in New Window',
    now_window: 'Current Window',
    pop_window: 'Popup Window',
    hyperLinks: 'Target URL',
    link_open_tips: 'Dashboard links can only be opened in non-edit mode',
    data_loading: 'Loading data...',
    export_loading: 'Exporting...',
    export_pdf: 'Export to PDF',
    jump_set: 'Jump Setting',
    enable_jump: 'Enable Jump',
    column_name: 'Field Name',
    enable_column: 'Enable Field',
    open_model: 'Open Mode',
    link_type: 'Jump Type',
    link_outer: 'External Link',
    link_panel: 'Dashboard',
    select_jump_panel: 'Select Linked Dashboard',
    link_view: 'Linked Chart',
    link_view_field: 'Linked Chart Field',
    add_jump_field: 'Add Jump Linked Dependency Field',
    input_jump_link: 'Please enter the jump link',
    select_dimension: 'Please select a dimension...',
    select_dimension_hint: 'Please first select the fields that need to be jumped to',
    please_select: 'Please select ',
    video_type: 'Video Type',
    online_video: 'Online Video',
    streaming_media: 'Stream Media',
    auto_play: 'Auto Play',
    video_tips: 'Prefer HTTPS links; currently supports mp4, webm formats',
    play_frequency: 'Play Frequency',
    play_once: 'Play Once',
    play_circle: 'Loop Play',
    video_links: 'Video Links',
    web_url: 'Web URL',
    video_add_tips: 'Please configure video information...',
    link_add_tips_pre: 'Please configure web information..',
    web_add_tips_suf: 'Add web information...',
    panel_view_result_show: 'Chart Result',
    panel_view_result_tips:
      'Selecting {0} will overwrite the number of chart results shown, with a value range of 1-10000',
    timeout_refresh: 'Request timed out, please refresh later...',
    mobile_layout: 'Mobile Layout',
    component_hidden: 'Hidden Components',
    public_link_tips:
      'Currently in public link mode, the target dashboard has not set up public links, unable to jump',
    input_title: 'Please enter the title',
    show_title: 'Title',
    default_settings: 'Default Settings',
    choose_background: 'Choose Component Background',
    choose_background_tips: "The component's own background will overwrite the current setting",
    setting_background: 'Set Background',
    setting_jump: 'Jump Setting',
    select_view: 'Please select a chart',
    visual: 'Virtualization',
    prohibit_multiple: 'Prohibit multiple fields in the same dataset',
    be_empty_dir: 'This is an empty directory!',
    fold: 'Fold',
    expand: 'Expand',
    pdf_export: 'PDF Export',
    switch_pdf_template: 'Switch PDF Template',
    pdf_template_with_params: 'Default Template (with dashboard description)',
    pdf_template_only_pic: 'Default Template (screenshot only)',
    panel_name: 'Dashboard Name',
    export_user: 'Exported By',
    export_time: 'Export Time',
    you_can_type_here: 'You can type other content here'
  },
  template_manage: {
    name_already_exists_type: 'Category name already exists',
    the_same_category: 'The template name already exists in the same category',
    name: 'Template management',
    rename: 'Rename',
    edit_template: 'Edit template',
    import_template: 'Import template',
    template_name: 'Template name',
    enter_template_name_hint: ' Please enter template name',
    keywords: 'Search keywords',
    catalog_name: 'Category name',
    search_result: 'Search results',
    search_result_unit: 'Pcs',
    selected_count: '{0} items selected',
    select_all_count: 'All {0} items selected',
    add_catalog: 'Add category',
    edit_catalog: 'Edit category',
    select_catalog: 'Select category',
    no_selectable_catalog: 'No selectable categories',
    please_select_catalog: 'Please select a category',
    no_template: 'No template',
    not_found: 'No relevant template found',
    delete_failed_hint: 'Unable to delete category',
    delete_failed_tip:
      'Please remove all templates under this category before deleting the category',
    delete_failed_confirm: 'Got it',
    delete_hint: 'Are you sure you want to delete this template?',
    delete_batch_hint: 'Are you sure you want to delete {0} templates?',
    add_success: 'Added successfully',
    edit_success: 'Modified successfully',
    import_success: 'Imported successfully',
    cover_success: 'Overwrite successfully',
    cover_exists_hint:
      'The same template name exists in the current category. Do you want to overwrite it?',
    template_size_hint: 'The template size must be less than 35MB',
    hint: 'Hint',
    relevant_content_found: 'No relevant content found',
    no_catalog: 'No category currently',
    delete_catalog_hint: 'Are you sure you want to delete this category?',
    delete_catalog_tip: 'Deleted content cannot be restored. Do you want to continue?',
    illegal_name_hint: 'Illegal name, please change it! ',
    exists_name_hint:
      'The current name already exists in the template management, please modify it',
    get_download_link_hint:
      'The template download link was not obtained, please contact the template market official',
    search_result_count: 'The search results are {0}',
    template_center: 'Template Center',
    preview: 'Preview'
  },
  work_branch: {
    new_empty: 'New Empty',
    new_folder: 'New Folder',
    back_to_work_branch: 'Return to Workbench',
    recommended_dashboard: 'Recommended Dashboard',
    template_market_official:
      'For template download link, please contact the official template market',
    create_quickly: 'Quick Create',
    permission_to_create: 'Lack of creation permission',
    new_using_template: 'Use Template',
    template_center: 'Template Center',
    view_all: 'View All',
    relevant_templates_found: 'No relevant templates found',
    last_edited_by: 'Last Edited By',
    last_edit_time: 'Last Edit Time',
    big_data_screen: 'Data Screen',
    big_screen: 'Data Screen',
    dashboard: 'Dashboard',
    data_set: 'Dataset',
    data_source: 'Datasource',
    recently_used: 'Recently Used',
    my_collection: 'My Favorites',
    relevant_content_found: 'No relevant content found',
    no_content_yet: 'No content yet',
    no_favorites_yet: 'No favorites yet',
    permission_denied: 'Permission denied',
    search_keyword: 'Search Keyword',
    new_page_preview: 'New Page',
    cancel_favorites: 'Cancel Favorite',
    open_dataset: 'Open Dataset',
    administrator_for_authorization:
      'No business menu permissions, please contact the administrator for authorization',
    public_link_share: 'Public Link Sharing',
    share_time_limit: 'Must be greater than the current time',
    ticket_setting: 'Ticket Setting',
    cannot_share_link:
      'Global sharing has been disabled. Sharing is temporarily unavailable. Please contact the administrator!',
    open_link_hint: 'After enabling, users can access via this link',
    uuid_checker: 'Only supports 8-16 alphanumeric characters. Please re-enter!',
    error_password_hint: 'Password format is incorrect. Please re-enter!',
    error_link_hint: 'Link format is incorrect. Please re-enter!',
    password_null_hint: 'Password cannot be empty. Please re-enter!',
    password_hint:
      "Password must be a 4-10 character string containing numbers, letters, and special characters [!{'@'}#$%^&*()_+]",
    max_ticket_count: 'Supports creating up to 5 tickets',
    last: 'Previous',
    next: 'Next',
    recommend: 'Recommend',
    recent: 'Recent',
    all_types: 'All Types',
    all_source: 'All Sources'
  },
  link_ticket: {
    require: 'Required',
    back: 'Return to the public link settings page',
    refresh: 'Refresh',
    time_tips:
      'Unit: minutes, range: [0-1440], 0 means no time limit, starting from the first access using the ticket',
    arg_val_tips: 'Please enter parameter values',
    arg_format_tips:
      'Please use JSON array format, example single valued [argVal], multi valued [argVal1, argVal2]',
    param_error: 'Ticket parameter error!',
    exp_error: 'Ticket has expired!',
    disable_error: 'Sharing feature has been disabled, please contact administrator!',
    pe_require_error: 'Expiration password is required, current link is invalid!',
    iframe_error:
      'Only embedded version and enterprise version support iframe embedding of public links!',
    link_error: 'Link does not exist!',
    link_exp_error: 'Link has expired!'
  },
  pblink: {
    key_pwd: 'Please enter the password to open the link',
    input_placeholder: 'Please enter 4 to 10 digits or letters',
    pwd_error: 'Wrong password',
    pwd_format_error: 'Please enter 4 to 10 digits or letters',
    sure_bt: 'OK',
    back_parent: 'Back to the previous level'
  },
  plugin: {
    'flag-all': 'All',
    'flag-ds': 'Datasource plug-in',
    'flag-view': 'Chart plug-in',
    'flag-df': 'Data reporting plug-in'
  },
  online_map: {
    geometry: 'Geographic information',
    onlinemap: 'Online map',
    empty_desc: 'Please enter information on the left and save'
  },
  setting_basic: {
    default_open_tips: 'Interface for creating and editing resources like dashboards and screen',
    third_platform_settings: 'Third-party platform settings',
    autoCreateUser: 'Third party automatically creates users',
    dsIntervalTime: 'Datasource detection time interval',
    dsExecuteTime: 'Datasource detection frequency',
    frontTimeOut: 'Request timeout (seconds)',
    logLiveTime: 'Operation log retention time (days)',
    thresholdLogLiveTime: 'Threshold alarm record retention time (days)',
    exportFileLiveTime: 'Background export file retention time (days)',
    platformOid: 'Third-party platform user organization',
    platformRid: 'Third-party platform user role',
    pwdStrategy: 'Enable password policy',
    dip: 'Disable initial password',
    pvp: 'Password validity period',
    defaultLogin: 'Default login method',
    shareDisable: 'Disable sharing',
    sharePeRequire: 'Sharing validity period password required',
    defaultSort: 'Default resource sorting method',
    defaultOpen: 'Page opening method',
    loginLimit: 'Limit login',
    loginLimitRate: 'Limit login failure times (times)',
    loginLimitTime: 'Limit login failure time (minutes)',
    share_disable_tips: 'Dashboard and data screen sharing are invalid after turning on'
  },
  resource_sort: {
    time_asc: 'In ascending order by creation time',
    time_desc: 'In descending order by creation time',
    name_asc: 'In ascending order by name',
    name_desc: 'In descending order by name'
  },
  open_opt: {
    new_page: 'New page opens',
    local_page: 'Current page opens'
  },
  setting_email: {
    title: 'Mail settings',
    host: 'SMTP host',
    port: 'SMTP port',
    account: 'SMTP account',
    pwd: 'SMTP password',
    reci: 'Test recipient',
    ssl: 'SSL',
    tsl: 'TSL'
  },
  sync_manage: {
    title: 'Synchronous management',
    ds_search_placeholder: 'Search name, description'
  },
  sync_datasource: {
    title: 'Data Connection Management',
    source_ds: 'Source Data Source',
    target_ds: 'Target Data Source',
    add_source_ds: '@:common.add @:sync_datasource.source_ds',
    add_target_ds: '@:common.add @:sync_datasource.target_ds',
    name: 'Name',
    desc: 'Description',
    type: 'Type',
    status: 'Status',
    create_time: 'Creation Time',
    update_time: 'Update Time',
    operation: 'Operation',
    edit: 'Edit',
    delete: 'Delete',
    confirm_batch_delete_target_ds: 'Are you sure you want to delete {0} target Datasources? ',
    confirm_batch_delete_source_ds: 'Are you sure you want to delete {0} source Datasources? ',
    recently_created: 'Recently created',
    has_running_task_msg:
      'After the task in progress is completed, the configuration before the modification will continue to be used for synchronization, and the task needs to be manually saved again. ',
    edit_datasource: 'Edit Datasource',
    add_datasource: 'Create a new Datasource',
    config_info: 'Configuration information',
    ds_type: 'Datasource type',
    valid: 'Valid',
    invalid: 'Invalid',
    start_time: 'Start time',
    end_time: 'End time',
    ds_delete_confirm: 'Are you sure you want to delete this Datasource? ',
    datasource: 'Datasource',
    select_folder: 'Please select a folder',
    sync_ds: 'Synchronize Datasource',
    sync_to_datasource: 'Will be synchronized to the Datasource list for data preparation',
    input_ds_name: 'Please enter the Datasource name',
    folder: 'The folder to which it belongs',
    cancel: 'Cancel',
    save: 'Save',
    next: 'Next step',
    prev: 'Previous step',
    validate: 'Validation',
    validate_success: 'Validation success',
    select_type: 'Please select the Datasource type',
    extra_params: 'Additional JDBC connection string',
    remark: 'Remarks',
    input_name: 'Please enter a name',
    input_limit_2_25: '{0}-{1} characters',
    input_limit_2_50: '2-50 characters',
    input_limit_2_64: '2-64 characters',
    input_limit_1_64: '1-64 characters',
    data_source_configuration: 'Datasource configuration',
    data_source_table: 'Datasource table',
    auth_method: 'Authentication method',
    passwd: 'Username and password',
    kerbers_info:
      'Please make sure krb5.Conf and Keytab Key have been added to the path: /opt/dataease2.0/conf',
    client_principal: 'Client Principal',
    keytab_Key_path: 'Keytab Key Path',
    data_base: 'Database name',
    user_name: 'Username',
    password: 'Password',
    host: 'Host name/IP address',
    doris_host: 'Doris address',
    query_port: 'Query Port',
    http_port: 'HTTP port',
    port: 'Port',
    datasource_url: 'Address',
    please_input_datasource_url:
      'Please enter the Elasticsearch address, such as: http://es_host:es_port',
    please_input_data_base: 'Please enter the database name',
    please_input_jdbc_url: 'Please enterJDBC connection',
    please_select_oracle_type: 'Select connection type',
    please_input_user_name: 'Please enter username',
    please_input_password: 'Please enter password',
    please_input_host: 'Please enter host',
    please_input_url: 'Please enter URL address',
    please_input_port: 'Please enter port',
    please_input_be_port: 'Please enter BE HTTP port',
    please_input_be_ip: 'Please enter BE IP address',
    please_input_fe_port: 'Please enter FE HTTP port',
    modify: 'Edit Datasource',
    validate_failed: 'Validation failed',
    oracle_connection_type: 'Service name/SID',
    oracle_sid: 'SID',
    oracle_service_name: 'Service name',
    get_schema: 'Get Schema',
    schema: 'Schema',
    charset: 'Character set',
    targetCharset: 'Target character set',
    please_choose_schema: 'Please select database Schema',
    please_choose_charset: 'Please select database character set',
    please_choose_targetCharset: 'Please select target character set',
    edit_datasource_msg:
      'Modifying the Datasource information may make the Dataset under the Datasource unavailable. Confirm the modification? ',
    repeat_datasource_msg: 'Datasource information with the same configuration already exists, ',
    in_valid: 'Invalid Datasource',
    initial_pool_size: 'Initial number of connections',
    min_pool_size: 'Minimum number of connections',
    max_pool_size: 'Maximum number of connections',
    max_idle_time: 'Maximum idle time (seconds)',
    bucket_num: 'Bucket number',
    replication_num: 'Number of replicas',
    please_input_bucket_num: 'Please enter the number of Buckets',
    please_input_replication_num: 'Please enter the number of replicas',
    acquire_increment: 'Increment number',
    connect_timeout: 'Connection timeout (seconds)',
    please_input_initial_pool_size: 'Please enter the initial number of connections',
    please_input_min_pool_size: 'Please enter the minimum number of connections',
    please_input_max_pool_size: 'Please enter the maximum number of connections',
    please_input_max_idle_time: 'Please enter the maximum idle time (seconds)',
    please_input_acquire_increment: 'Please enter the increment number',
    please_input_query_timeout: 'Please enter the query timeout',
    please_input_connect_timeout: 'Please enter the connection timeout (seconds)',
    no_less_then_0: 'Parameters in advanced settings cannot be less than zero',
    port_no_less_then_0: 'Port cannot be less than zero',
    priority: 'Advanced settings',
    jdbcUrl: 'JDBC connection',
    _ip_address: 'Please enter the host name/IP address',
    display_name: 'Display name',
    connection_mode: 'Connection mode',
    please_select: 'Please select ',
    query_timeout: 'Query timeout',
    description: 'Description',
    tips: 'Tips',
    replication: 'BACKEND Replicas',
    replication_tip: 'Number of BE Nodes'
  },
  sync_summary: {
    summary: 'Overview',
    data_source_number: 'Number of Datasources',
    task_number: 'Number of tasks',
    execution_count: 'Number of executions',
    execution_results_in_the_past_7_days: 'Executions in the past 7 days',
    sync_status_distribution: 'Sync status distribution'
  },
  sync_task: {
    title: 'Task Management',
    task_list: 'Task list',
    log_list: 'Task log',
    add_task: 'Add task',
    name: 'Name',
    desc: 'Description',
    status: 'Status',
    create_time: 'Creation time',
    update_time: 'Update time',
    operation: 'Operation',
    edit: 'Edit',
    delete: 'Delete',
    start: 'Enable',
    stop: 'Stop',
    terminated: 'Terminate synchronization',
    running_one: 'Execute once',
    trigger_last_time: 'Last execution time',
    trigger_next_time: 'Next execution time',
    status_success: 'Success',
    status_running: 'Synchronizing',
    status_failed: 'Failed',
    status_stopped: 'Task stopped',
    status_waiting: 'Waiting for sync',
    status_done: 'Task ended',
    status_terminated: 'Terminated',
    status_connection_lost: 'Connection lost',
    log: 'Log',
    show_log: 'View log',
    last_execute_result: 'Last execution result',
    execute_result: 'Execution result',
    task_status: 'Task status',
    sync: 'Synchronize',
    target_table: 'Target table',
    batch_del: 'Batch delete',
    selection_info: '{0} items selected',
    clear_button: 'Clear',
    task_text: 'Task',
    hour: 'Hour',
    day: 'Day',
    week: 'Week',
    month: 'Month',
    year: 'Year',
    minute: 'Minute',
    second: 'Second',
    hour_minute_second: 'Hour:Minute:Second',
    please_enter_task_name: 'Please enter the task name',
    input_limit_255: 'Length cannot exceed 255 characters',
    please_enter: 'Please enter',
    please_cron: 'Please enter a valid Cron expression:',
    please_choose: 'Please select',
    please_choose_start_time: 'Please select the start time',
    please_choose_end_time: 'Please select the end time',
    end_time_must_be_later_than_start_time: 'The end time must be greater than the start time! ',
    please_choose_database_type: 'Please select the database type',
    please_choose_database: 'Please select the database',
    please_choose_table: 'Please select the table',
    please_choose_incremental_field: 'Please select the incremental field',
    please_enter_table_name: 'Please enter the table name',
    input_limit_64: 'The length cannot exceed 64 characters',
    must_be_met_the_table_name:
      'Must start with a letter and can only contain letters, numbers, and underscores',
    please_choose_partition_type: 'Please select the partition type',
    please_enter_end_offset: 'Please enter the end offset',
    please_choose_partition_interval_unit: 'Please select the unit of the partition interval',
    please_enter_partition_column_value: 'Please enter the partition column value',
    input_limit_4096: 'The length cannot exceed 4096 characters',
    please_enter_starting_value: 'Please enter the starting value',
    please_enter_end_value: 'Please enter the ending value',
    please_enter_numerical_range_interval: 'Please enter the numerical partition interval',
    please_choose_time_range: 'Please select the time range',
    edit_success: 'Modification successful',
    add_success: 'Added successfully',
    target_database_status_is_abnormal: 'Target database status abnormal',
    edit_task: 'Edit task',
    basic_information: 'Basic information',
    source_database: 'Source database',
    target_database: 'Target database',
    task_time_out_time: 'Task timeout (seconds)',
    effective_if_greater_than_0: 'Unit seconds, effective when greater than 0',
    retry_attempts_on_failure: 'Number of failed retries',
    sync_frequency: 'Synchronization frequency',
    sync_immediately: 'Immediate synchronization',
    sync_cron: 'Expression setting',
    sync_fixed_frequency: 'Fixed frequency',
    cron_expression: 'Cron expression',
    each: 'Every',
    sync_once: 'Synchronize once',
    confirm: 'Confirm',
    msg_get_database_table_failed: 'Failed to get database table',
    msg_source_database_status_is_abnormal: 'Source database status is abnormal',
    database: 'Database',
    database_type: 'Type',
    query_method: 'Query method',
    please_choose_data_extraction_method: 'Please choose the data extraction method',
    table: 'Table',
    sql_tip_1:
      'This method does not always return the precision and length set by the user when obtaining the length or precision of the column type, but it can still be used as a reference value to determine the maximum display length of each column in the result set. ',
    sql_tip_2:
      'If you need to obtain more accurate column type length precision, please use the library table method, or set the length precision in the field mapping in the next step. ',
    please_enter_sql: 'Please enter the query SQL',
    msg_confirm_delete_field: 'Are you sure you want to delete this field? ',
    source_field: 'Source field',
    field_type: 'Type',
    field_length: 'Length',
    field_precision: 'Precision',
    field_key: 'Key',
    field_index: 'Index',
    field_comment: 'Comment',
    confirm_delete_field: 'Are you sure you want to delete the {0} field? ',
    msg_field_list_empty_tip:
      'Field list cannot be empty, and data with empty name or field type of UNKNOWN cannot exist, please check',
    next_week: 'Next week',
    next_month: 'Next month',
    next_three_month: 'Next three months',
    must_be_start_less_end: 'The end value of the range must be greater than the start value',
    must_be_partition_interval_greater_than_0: 'Partition interval must be greater than 0',
    must_be_partition_interval_less_end_start_difference:
      'Partition interval must be less than the difference between the end value and the start value',
    date: 'Date',
    list: 'Column',
    number: 'Value',
    define_mapping_field: 'Define mapping field',
    target_database_type: 'Target database type',
    delete_field: 'Delete field',
    add_field: 'Add field',
    edit_field: 'Edit field',
    add_all_field: 'Add all fields',
    fault_tolerance_rate: 'Fault tolerance rate',
    fault_tolerance_rate_tip:
      '0～1, the default is 0, which means that when there is an error data in the synchronization batch data, the import task of the entire batch will fail. ',
    incremental_sync: 'Incremental synchronization',
    incremental_sync_tip_1: 'Full: Full coverage synchronization',
    incremental_sync_tip_2:
      'Incremental: Incremental synchronization based on incremental fields, incremental fields must be integer or time types',
    incremental_field: 'Incremental field',
    enable_partition: 'Enable partitioning',
    enable_partition_tip: 'Enable partitioning, the field list must not have empty values',
    partition_type: 'Partition type',
    partition_field: 'Partition field',
    on: 'Enable',
    off: 'Close',
    picker_to: 'To',
    picker_start: 'Start',
    picker_end: 'End',
    end_offset: 'End offset',
    number_range: 'Number range',
    partition_interval: 'Partition interval',
    partition_column_value: 'Partition column value',
    partition_column_value_placeholder: 'Partition format is: p1:"v1","v2","v3";p2:"v1","v2"',
    partition_interval_unit: 'Partition interval unit',
    input_limit: 'Length cannot exceed {0} characters',
    cannot_begin_with_number: 'Field name cannot start with a number',
    duplicate_field_tip:
      'Duplicate field [{0}], the same source field cannot be mapped multiple times',
    duplicate_name_error: 'Duplicate name [{0}]',
    confirm_batch_delete: 'Confirm batch deletion task',
    op_success: 'Operation successful',
    search_input_name_desc_placeholder: 'Search name, description',
    confirm_delete_msg: 'Are you sure you want to delete? ',
    target_table_info: 'Target table information',
    confirm_clear_msg: 'Are you sure you want to clear {0}?',
    clear: 'Clear',
    op_success_refresh: 'Execution successful, please refresh later',
    execute_time: 'Execution time',
    clear_log: 'Clear log',
    search_input_name_id_placeholder: 'Search name, ID',
    log_id: 'Log ID',
    op: 'Operation',
    view_execute_log: 'View execution log',
    submit_true: 'Confirm',
    please_choose_clear_method: 'Please select a clearing method',
    last_1_days_log: 'Log from 1 day ago',
    last_1_weeks_log: 'Log from 1 week ago',
    last_1_months_log: 'Log from 1 month ago',
    last_3_months_log: 'Log from 3 months ago',
    last_6_months_log: 'Log from 6 months ago',
    last_1_years_log: 'Log from 1 year ago',
    execute_log: 'Execution log',
    done: 'Done',
    connection_lost: 'Disconnected',
    task_name: 'Task name',
    es_params_label: 'Query Parameters',
    es_params_tip: 'Please follow the Elasticsearch query syntax',
    dynamic_partition_enable: 'Dynamic Partitioning',
    time_end: 'End',
    es_query_param_formatter_error:
      'Query parameter format error, please enter the correct JSON format, please check',
    show_task_id: 'View Task ID'
  },
  watermark: {
    support_params: 'Currently supported parameters:',
    enable: 'Enable',
    excel_enable: 'Enable watermark for exporting data files',
    enable_panel_custom:
      ' Allow dashboards or data screens to turn on or off watermarks individually',
    content: 'Content',
    custom_content: 'Custom formula',
    account: 'Account',
    nick_name: 'Nickname',
    ip: 'IP',
    now: 'Current time',
    watermark_color: 'Color',
    watermark_font_size: 'Font size',
    watermark_space: 'Watermark spacing',
    horizontal: 'Horizontal',
    vertical: 'Vertical',
    reset: 'Reset',
    preview: 'Preview',
    save: 'Save'
  },
  appearance: {
    give_up: 'Give up update',
    save_apply: 'Save and apply'
  },
  report: {
    title: 'Scheduled Report',
    task_name: 'Task name',
    last_exec_time: 'Last execution time',
    last_exec_result: 'Last execution result',
    task_status: 'Task status',
    next_exec_time: 'Next execution time',
    creator: 'Creator',
    create_time: 'Creation time',
    status_wait: 'Waiting to send',
    status_stop: 'Task stopped',
    status_finish: 'Task finished',
    status_send: 'Sending',
    search_tips: 'Search by task name',
    report_title: 'Task list',
    instance_title: 'Task log',
    add_task: 'Add task',
    lark_groups: 'Lark group',
    larksuite_groups: 'Larksuite group',
    send_setting: 'Send settings',
    retrying_settings: 'Send failed retry settings',
    start_time: 'Start time',
    end_time: 'End time',
    once_a_day: 'Every day',
    once_a_week: 'Every week',
    once_a_month: 'Every month',
    hour: 'Hour',
    day: 'Day',
    week: 'Week',
    month: 'Month',
    week_mon: 'Monday',
    week_tue: 'Tuesday',
    week_wed: 'Wednesday',
    week_thu: 'Thursday',
    week_fri: 'Friday',
    week_sat: 'Saturday',
    week_sun: 'Sunday',
    every_exec: 'Execute once',
    date: 'Date',
    last_status_running: 'Running',
    last_status_fail: 'Failed',
    last_status_success: 'Success',
    batch_confirm: 'Confirm to batch delete tasks?',
    fire_now_tips: 'You can manually execute again after 5s of manual execution!',
    task_running_tips: 'Task is running!',
    start_success: 'Initiated successfully, executing',
    form: {
      title: 'Report Title',
      content: 'Report Content',
      send_content: 'Send Content',
      filter: 'Set Query Component Parameters',
      water_mask: 'Watermark Settings',
      show_water_mask: 'Show Watermark',
      format: 'Format',
      view_data: 'Chart Data',
      pixel: 'Dashboard Resolution',
      reci_setting: 'Receiving Settings',
      retrying: 'Retry on Failure',
      retrying_rate: 'Retry Interval (minutes)',
      please_input_positive_int: 'Please input a positive integer for {0}',
      rate: 'Sending frequency'
    },
    filter: {
      title: 'Set Query Component Default Values',
      reset: 'Restore Default Values',
      reset_all: 'Restore All Query Condition Default Values',
      empty_tips:
        'Filter component {0} conditions are required, please fill in the conditions first!'
    }
  },
  variable: {
    give_up: 's',
    save_apply: 'Save and apply'
  },
  data_fill: {
    data_fill: 'Data filling',
    fill_in_the_task: 'Filling task',
    data_fill_name: 'Data filling name',
    p_data_fill_name: 'Please enter the data filling name',
    save_df_success: 'Save data filling successfully',
    permission: 'Filling permission',
    enable: 'Enable',
    enable_hint:
      'After data filling is enabled, the form data can be stored in the Datasource. Once enabled, it is not allowed to be closed later. ',
    new_folder: 'New folder',
    form_manage: 'Form management',
    my_job: 'My data filling task',
    short_name: 'Task',
    disable_data_fill_hint:
      'After closing data filling, the form data will fail to submit. Are you sure you want to close it? ',
    enable_data_fill_hint:
      'After enabling, it is allowed to create a new table in the Datasource database and store the form data in the table',
    todo: 'To be filled',
    finished: 'Filled',
    expired: 'Expired',
    all: 'All',
    required_select: 'Required',
    condition: 'Filter value',
    add_condition: 'Add condition',
    disable_edit: 'Disable editing',
    enable_edit: 'Allow editing',
    select_component: 'Please select a component',
    set_condition: 'Set condition',
    move_to: 'Move to',
    rename: 'Rename',
    delete: 'Delete',
    move_success: 'Move successfully',
    rename_success: 'Rename successfully',
    create_success: 'Create successfully',
    create_form: 'Create a new form',
    create_folder: 'Create a new folder',
    order_by_create_time_asc: 'In ascending order by creation time',
    order_by_create_time_desc: 'In descending order by creation time',
    order_by_name_asc: 'In ascending order by name',
    order_by_name_desc: 'In descending order by name',
    delete_folder_hint:
      'After deletion, all resources under this folder will be deleted. Please operate with caution.',
    confirm_delete_folder: 'Are you sure you want to delete this folder?',
    confirm_delete_form: 'Are you sure you want to delete this form?',
    confirm_delete_multiple_data: 'Are you sure you want to delete {0} pieces of data?',
    confirm_delete_data: 'Are you sure you want to delete the data?',
    no_form: 'No form yet',
    on_the_left: 'Please select a form on the left',
    exporting: 'Backend export in progress, can go to',
    progress_to_download: 'Check progress and download',
    clear_selection: 'Clear selection',
    truncate_table: 'Truncate table',
    truncate: 'Truncate',
    confirm_truncate_table: 'Confirm to truncate table?',
    add_search_condition: 'Add search condition',
    form: {
      set_enableDefaultTime: 'Default time',
      currentTime: 'Current time',
      defaultTime: 'Fixed time',
      please_select_valid_column: 'Please select valid column',
      create_type: 'Create type',
      create_new_table: 'Create new table',
      bind_exists_table: 'Bind exists table',
      create_new_column: 'Create column',
      select_exists_column: 'Bind column',
      add_detail_columns: 'Add column details',
      detail_columns: 'Column details',
      display_name: 'Display Name',
      show_more_detail: 'Show more',
      confirm_to_mark_as_complete: 'Confirm to mark as complete?',
      mobile_number_format_is_incorrect: 'Mobile number format is incorrect',
      email_format_is_incorrect: 'Email format is incorrect',
      name: 'Name',
      rename: 'Rename',
      untitled: 'Untitled form',
      create_new_form: 'Create a new form',
      copy_new_form: 'Copy form',
      edit_form: 'Edit form',
      title: 'Title',
      no_form: 'No form yet, click',
      form_list_name: 'Fill in form',
      create_form: 'Create a new form',
      please_select: 'Please select ',
      component: 'Component',
      component_setting: 'Component setting',
      hint: 'Hint word',
      input_limit_50: 'No more than 50 characters',
      input_limit_max: 'No more than {0} characters',
      input_limit_min: 'No less than {0} characters',
      option: 'Option',
      form_setting: 'Form setting',
      confirm_delete: 'Confirm deletion? (Will not delete the created database table)',
      list: 'Form data',
      record: 'Submission record',
      task_manage: 'Task management',
      form_name: 'Form name',
      commit_type: 'Form submission method',
      commit_type_append: 'Data append',
      commit_type_update: 'Data update',
      commit_rule: 'Update conditions',
      commit_rule_add: 'Add update rules',
      commit_rule_settings: 'Update rule settings',
      commit_rule_set: 'Set',
      folder: 'Folder',
      datasource: 'Datasource',
      table: 'Database table',
      creator: 'Creator',
      createTime: 'Creation time',
      operation: 'Operation',
      operator: 'Operator',
      operate_time: 'Operation time',
      modify: 'Modify',
      show: 'View',
      delete: 'Delete',
      show_data: 'View data',
      text: 'Normal text',
      number: 'Number',
      tel: 'Mobile phone number',
      email: 'Email',
      duplicate_error: 'Duplicate',
      value_not_exists: 'Value does not exist',
      range_separator: 'Separation character',
      start_hint_word: 'Start hint word',
      end_hint_word: 'End hint word',
      input_type: 'Format type',
      date_type: 'Display granularity',
      check: 'Check',
      set_required: 'Set as required',
      set_unique: 'Do not allow duplicate values',
      set_multiple: 'Allow multiple selections',
      use_datetime: 'Use date and time',
      custom: 'Custom',
      use_datasource: 'Bind Datasource',
      bind_column: 'Bind field',
      bind_complete: 'Already bound',
      option_value: 'Option value',
      add_option: 'Add option value',
      form_name_cannot_none: 'Form name cannot be empty',
      form_update_rule_none: 'Please configure update rules',
      form_components_cannot_null: 'Please add form components',
      option_list_cannot_empty: 'Option value cannot be empty',
      option_list_datasource_cannot_empty:
        'Option value binding Datasource configuration cannot be empty',
      component_setting_error: 'Component setting error',
      table_name: 'Database table name',
      form_column: 'Form field',
      column_name: 'Database table field name',
      column_type: 'Database field type',
      create_index: 'Create index',
      add_index: 'Add new index',
      index_name: 'Index name',
      create_index_hint:
        'MySQL 8.0 or MariaDB 10.8.0 and below do not support index descending sort',
      index_column: 'Index field',
      order: 'Sort',
      order_asc: 'Ascending',
      order_desc: 'Descending',
      order_none: 'Default sort',
      add_column: 'Add new field',
      please_insert_start: 'Please enter the start time',
      please_insert_end: 'Please enter the end time',
      save_form: 'Save form',
      default: 'Default',
      default_built_in: 'Built-in database',
      lt_check: 'Value needs to be less than {0}: {1}',
      gt_check: 'Value needs to be greater than {0}: {1}',
      le_check: 'Value needs to be less than or equal to {0}: {1}',
      ge_check: 'Value needs to be greater than or equal to {0}: {1}',
      status: 'Reporting status',
      status_0: 'Not reported',
      status_1: 'Completed'
    },
    database: {
      nvarchar: 'String',
      text: 'Long text',
      number: 'Integer',
      decimal: 'Decimal number',
      datetime: 'Date'
    },
    data: {
      send_status: 'Task Assignment Status',
      df_task_status: 'Task Filling Status',
      data_not_exists: 'Data does not exist',
      cannot_select_all: 'Cannot select all',
      commit_time: 'Commit time',
      confirm_delete: 'Confirm deletion?',
      add_data: 'Add data',
      batch_upload: 'Batch upload',
      download: 'Download',
      download_template: 'Download template',
      insert_data: 'Insert data',
      batch_insert_data: ' Batch insert data',
      batch_insert_data_with_count: 'Batch insert data，total count: {0}',
      update_data: 'Update data',
      delete_data: 'Delete data',
      recent_committer: 'Recent committer',
      recent_commit_time: 'Latest submission time',
      start: 'Start',
      end: 'End',
      id_is: 'Data with ID [',
      data_not_found: '] does not exist'
    },
    task: {
      commit_operate_type: 'Commit type',
      committer: 'Committer',
      time_check_5_minute_later_than_current:
        'Cannot be less than 5 minutes after the current time',
      time_check_later_than_current: 'Cannot be less than the current time',
      time_check_earlier_than_end: 'Cannot be greater than the end time',
      time_check_later_than_start: 'Cannot be less than the start time',
      confirm_exit_without_save:
        'The current changes have not been saved, are you sure you want to exit?',
      deliver_now: 'Deliver immediately',
      deliver_scheduled: 'Scheduled delivery',
      logic_filter: 'Conditional filtering',
      enum_filter: 'Enumeration filtering',
      cannot_be_all_disabled: 'All components cannot be disabled',
      template_hint_title: 'Setting instructions are as follows',
      template_hint_1:
        'When the component is set to prohibit editing, users are not allowed to modify when filling out the form',
      template_hint_2:
        'When the component is set to allow editing, users are allowed to modify when filling out the form',
      finish_rate_hint:
        'Filling completion rate = number of reported data items/number of sent reports*100%',
      distribute_frequency: 'Sending frequency',
      one_time: 'Only send once',
      interval: 'Regularly send',
      execute_now: 'Execute now',
      end_time: 'Task end time',
      please_select_end_time: 'Please select task end time',
      end_time_error: 'End time must greater than current',
      distribute_setting: 'Sending settings',
      task_distribute_setting: 'Task sending settings',
      receive_object: 'Receiving object',
      receive_fit_column: 'Receiving object matching field',
      form_template_setting: 'Form template settings',
      template_setting: 'Template settings',
      form_filter_setting: 'Form filter settings',
      filter_setting: 'Filter settings',
      component: 'GroupTitle',
      receiver: 'Recipient',
      receiver_not_null: 'Recipient cannot be empty! ',
      commit_type: 'Data submission method',
      person: 'People',
      select_receiver: 'Select recipient',
      exec_logs: 'Execution log',
      assign_num: 'Number of people assigned',
      finished_user_num: 'Number of people completed',
      unfinished_user_num: 'Number of people not completed',
      finished_rate: 'Completion rate',
      confirm_batch_delete: 'Confirm batch deletion tasks',
      name: 'Name',
      creator: 'Creator',
      create_time: 'Creation time',
      rate_type: 'Task delivery mode',
      task_status: 'Task status',
      task_progress: 'Finished / Total',
      task_name: 'Task name',
      add_task: 'Add task',
      task_remain_time: 'Task validity period',
      task_sender: 'Task sender',
      start_filling: 'Fill in now',
      task_distribute_time: 'Task distribution time',
      task_expiration_time: 'Task expiration time',
      task_finished_time: 'Task completion time',
      task_end_time: 'Task deadline',
      edit_data: 'Edit data',
      show_data: 'View data',
      confirm_enable:
        'Confirm to start the task? (A single task will create a new task to be distributed)',
      confirm_disable: 'Confirm to stop the task? ',
      edit_task: 'Edit task',
      create_task: 'Create new task',
      edit: 'Edit',
      stop: 'Stop',
      start: 'Start',
      delete: 'Delete',
      no_time_limit: 'Unlimited time',
      todo: 'To do',
      finished: 'Submitted',
      expired: 'Expired',
      running: 'In progress',
      assigned_task: 'Assigned task',
      task_finish_in: 'Assigned in task',
      task_finish_in_suffix: 'Finished report within',
      open_sub_task: 'View assigned tasks'
    },
    search_by_commit_name: 'Search by operator name'
  },
  threshold: {
    drawer_title: 'Set threshold alarm',
    table_name: 'Threshold alarm name',
    status: 'Data status',
    base_setting: 'Basic settings',
    threshold_setting: 'Alarm settings',
    name: 'Alarm name',
    grid_title: 'Alarm Management',
    grid: 'Alarm list',
    record: 'Detection record',
    module_name: 'Threshold alarm',
    setting: 'Threshold alarm setting',
    no_view_tip: 'Please save before setting threshold alarm',
    selected_view: 'Selected chart:',
    please_enter_name: 'Please enter alert name',
    detection_time: 'Detection Time',
    rules: 'Alarm rules',
    rules_invalid: 'Alarm rules invalid',
    once_a_hour: 'Every hour',
    once_a_day: 'Every day',
    once_a_week: 'Every week',
    once_a_month: 'Every month',
    email: 'Email',
    wecom: 'WeCom',
    dingtalk: 'DingTalk',
    lark: 'Lark',
    larksuite: 'Larksuite',
    notification_setting: 'Alarm notification',
    notification_method: 'Notification method',
    notification_user: 'Notifier',
    notification_email: 'Email notification',
    please_enter_email: 'Please enter email, press Enter to confirm',
    please_choose_lark_group: 'Please select lark group',
    notification_content: 'Notification content',
    default_msg: 'Default message',
    custom_msg: 'Custom message',
    msg_title: 'Message title',
    msg_content: 'Message content',
    repeat_send: 'Repeat send',
    recipient: 'Recipient',
    choose_recipient: 'Select recipient',
    trigger_alarm: 'Trigger alarm',
    abnormal_alarm: 'Abnormal alarm',
    choose_recipient_tip: 'Selected {0} people, {1} roles',
    notification_methods_cannot_be_empty: 'Notification methods cannot be empty at the same time',
    recipient_setting: 'Recipient setting',
    attention_quota_tip: 'The indicators you are following',
    pay_attention_in_time: '. Please pay attention in time.',
    msg_preview: 'Message preview',
    average: 'Average value',
    next_time: 'Next ',
    end_of_year: 'End of year',
    ago: 'Ago',
    later: 'Later'
  },
  relation: {
    no_permission: 'No view permission',
    datasource: 'Datasource',
    dataset: 'Dataset',
    dashboard: 'Dashboard',
    dataV: 'Data screen',
    analysis: 'Bloodline Analysis',
    resource_type: 'Resource type',
    pls_choose: 'Please choose',
    choose_resource: 'Select resource',
    list_chart: 'List view',
    mind_map: 'Mind map',
    index: 'Serial number',
    datasource_name: 'Datasource name',
    dataset_name: 'Datasource set name',
    dashboard_name: 'Dashboard name',
    dataV_name: 'Data dashboard name',
    retract: 'Collapse',
    expand: 'Expand',
    node_info: 'Node details',
    node_name: 'Node name',
    creator: 'Creator',
    last_update_time: 'Last update time',
    dependent: 'Resource dependency',
    new_page: 'New page opens'
  },
  copilot: {
    talking_analysis: 'Copilot conversation analysis',
    hello: 'Hello, I am Copilot conversation analysis',
    click_talk: 'Click once to open the visual chart answer mode~',
    know: 'I know',
    ds_prefix: 'The current dataset is [',
    ds_suffix: '], switching datasets will clear the current session. ',
    confirm: 'Are you sure you want to switch the dataset? ',
    choose_dataset: 'Select a dataset',
    pls_choose_dataset: 'Please select a dataset',
    chart: 'Chart',
    line: 'Line chart',
    bar: 'Bar chart',
    pie: 'Pie chart',
    sorry:
      "Sorry, I can't answer this question based on the known information.Please rephrase your question or provide more information~",
    hello1: "Hello, I'm Copilot,I'm happy to serve you~",
    answer: 'Answering',
    example: 'You can ask me: Pie chart of sales share of each sales department in 2020',
    switch_chart: 'Switch chart type',
    switch_table: 'Switch to detailed table',
    download: 'Download'
  },
  userCenter: {
    enable: 'Enable',
    invalid: 'Invalid',
    binding_settings: 'Binding settings',
    wechat: 'Enterprise WeChat',
    wechat_desc:
      'After binding, you can log in by scanning the QR code through WeChat for Enterprise',
    dingtalk: 'DingTalk',
    dingtalk_desc: 'After binding, you can log in by scanning the QR code through DingTalk',
    lark: 'Lark',
    lark_desc: 'After binding, you can log in by scanning the QR code through Lark',
    international_lark: 'International Lark',
    international_lark_desc:
      'After binding, you can log in by scanning the QR code through International Lark',
    bind: 'Bind',
    unbind_success: 'Unbind successfully',
    confirm_unbind_dingtalk: 'Are you sure you want to unbind DingTalk? ',
    pls_use: 'Please use ',
    bind_use_qr: 'Scan QR code to bind',
    pls_use_dingtalk: 'Please use DingTalk to scan QR code to log in',
    api_limit_5: 'Supports creation of up to 5 ApiKeys',
    tips: 'Tips',
    create: 'Create',
    click_to_hind: 'Click to hide',
    click_to_show: 'Click to show',
    view_api: 'View API',
    enable_success: 'Enabled successfully',
    disabled_success: 'Disabled successfully',
    delete_api_key: 'Are you sure you want to delete this API key? ',
    api_key_desc:
      'API Key is your key to access DataEase API, which has full permissions for your account. Please keep it safe! Do not disclose API Key to external channels in any way to avoid security threats caused by others. '
  },
  free: {
    title: 'Free Resource Management',
    no_data: 'No Orphaned Resources',
    sync: 'Migrate',
    quick: 'One-click',
    batch: 'Batch',
    resource: 'Resource',
    view_association: 'View Bloodline Relationship',
    quick_sync_tips:
      'All dashboards, data screens, datasets, and data sources will be migrated to the [Migrated Resources] folder.',
    batch_sync_tips:
      "1. Dashboards, data screens, datasets, and data sources related to the selected resources will also be migrated to the corresponding resource's [Migrated Resources] folder;",
    batch_sync_tips1:
      '2. The migrated folder will also migrate the subfolders and resources under that folder.',
    quick_del_confirm: 'Are you sure to delete all orphaned resources?',
    quick_del_tips: 'Resources cannot be revoked after deletion.',
    quick_sync_confirm: 'Are you sure to migrate all orphaned resources?',
    quick_sync_confirm_tips:
      'Resources cannot be revoked after migration, please proceed with caution.',
    batch_sync_confirm: 'Are you sure to migrate {0} items and their related orphaned resources?',
    single_sync_confirm: 'Are you sure to migrate this resource?',
    batch_del_confirm: 'Are you sure to delete {0} resources?',
    batch_del_confirm_tips:
      'Resources cannot be revoked after deletion, please proceed with caution.',
    del_tips_dataset:
      'Deleting the dataset will cause related datasets to become invalid, are you sure to delete?',
    del_tips_datasource:
      'Datasets are currently using these data sources, and they will become unusable after deletion, are you sure to delete?',
    single_del_confirm: 'Are you sure to delete this {0}?',
    single_del_tips_dataset:
      'This dataset has the following bloodline relationships, and deleting it will cause related visualizations to become invalid, are you sure to delete?',
    single_del_tips_datasource:
      'There are {0} datasets currently using this data source, and they will become unusable after deletion, are you sure to delete?',
    folder: 'Folder',
    del_folder_tips:
      'After deletion, all resources under this folder will be deleted, please proceed with caution.',
    sync_to_org: 'Migrate to Target Organization',
    sync_org_placeholder: 'Please select the target organization',
    relation_picture: 'Bloodline Relationship Diagram',
    save_error: "Prohibit operations on the 'Migration Resources' directory"
  },
  security: {
    title: 'Security Settings'
  },
  setting_mfa: {
    title: 'MFA Settings',
    status: 'Global MFA Authentication Enabled',
    platformEnable: 'Third-party Authentication Enables MFA',
    exp: 'MFA Verification Expiry',
    otpName: 'Name After Scanning OTP',
    rate: 'OTP Delay Valid Count',
    status_0: 'Disabled',
    status_1: 'All Users',
    status_2: 'System Administrators Only',
    platform_tips: 'Third-party login methods include: OIDC, CAS',
    exp_tips:
      'Unit: seconds, currently only effective when checking account password verification MFA',
    user_enable: 'MFA Multi-factor Authentication',
    code_input_msg: 'Please enter {0} digits',
    bind_ready: 'Bound',
    bind_unready: 'Unbound',
    bind_title: 'Bind MFA Multi-factor Authentication',
    enable_switch_tips: 'The administrator has set all users to enable MFA authentication',
    reset_key_tips: 'Reset MFA',
    step_1: 'Install Application',
    step_2: 'Bind MFA Authenticator',
    unbind_confirm: 'Are you sure to unbind MFA multi-factor authentication?',
    mfa_code: 'MFA Verification Code',
    install_app: 'Install the app',
    install_1:
      '1. Download and install the MFA Authenticator app on your mobile device or WeChat mini program',
    install_2:
      '2. After installation, click next to enter the binding page (if already installed, go directly to the next step)',
    phone_download: 'Download on mobile',
    scan_qr_tips:
      'Use the MFA Authenticator app to scan the QR code below to obtain a 6-digit verification code',
    code_miss_tips:
      'If you cannot provide an MFA verification code, please contact the administrator!'
  },
  threshold_warn: {
    all: 'All',
    normal: 'Normal',
    abnormal: 'Abnormal',
    batch_del_confirm: 'Are you sure to delete {0} alert tasks?',
    search_placeholder: 'Search by alert name',
    chart_name: 'Chart Name',
    warn_status: 'Alert Status'
  },
  webhook: {
    title: 'Webhook Management',
    add: 'Add Webhook',
    search_placeholder: 'Search by name',
    content_type: 'Content Type',
    del_confirm: 'Are you sure you want to delete this Webhook?',
    batch_del_confirm: 'Are you sure you want to delete {0} Webhooks?'
  }
}
