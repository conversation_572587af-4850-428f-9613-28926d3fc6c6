import { sin, cos, toPercent } from '@/utils/translate'
import { imgUrlTrans } from '@/utils/imgUtils'
import { isBase64Image } from '@/utils/imageBase64Utils'
import { hexColorToRGBA } from '@/views/chart/components/js/util'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import { isMainCanvas, isTabCanvas } from '@/utils/canvasUtils'
const dvMainStore = dvMainStoreWithOut()
export function getShapeStyle(style) {
  const result = {}
  ;['width', 'height', 'top', 'left', 'rotate'].forEach(attr => {
    if (attr != 'rotate') {
      result[attr] = style[attr] + 'px'
    } else {
      result['transform'] = 'rotate(' + style[attr] + 'deg)'
    }
  })

  return result
}

export function getShapeItemStyle(
  item,
  { dvModel, cellWidth, cellHeight, curGap, showPosition = 'preview' }
) {
  let result = {}
  if (dvModel === 'dashboard' && !item['isPlayer']) {
    result = {
      padding: curGap + 'px!important',
      width: cellWidth * item.sizeX + 'px',
      height: cellHeight * item.sizeY + 'px',
      left: cellWidth * (item.x - 1) + 'px',
      top: cellHeight * (item.y - 1) + 'px'
    }
  } else if (dvModel === 'dataV' && isTabCanvas(item.canvasId) && showPosition === 'preview') {
    result = {
      padding: curGap + 'px!important',
      width: toPercent(item.groupStyle.width),
      height: toPercent(item.groupStyle.height),
      top: toPercent(item.groupStyle.top),
      left: toPercent(item.groupStyle.left)
    }
  } else {
    result = {
      padding: curGap + 'px!important',
      width: item.style.width + 'px',
      height: item.style.height + 'px',
      left: item.style.left + 'px',
      top: item.style.top + 'px'
    }
  }

  return result
}

export function syncShapeItemStyle(item, cellWidth, cellHeight) {
  item.style.left = cellWidth * (item.x - 1)
  item.style.top = cellHeight * (item.y - 1)
  item.style.width = cellWidth * item.sizeX
  item.style.height = cellHeight * item.sizeY
}

const needUnit = [
  'fontSize',
  'width',
  'height',
  'top',
  'left',
  'borderWidth',
  'letterSpacing',
  'borderRadius'
]

export function getSVGStyle(style, filter = []) {
  const result = {}

  ;[
    'opacity',
    'width',
    'height',
    'top',
    'left',
    'rotate',
    'fontSize',
    'fontWeight',
    'lineHeight',
    'letterSpacing',
    'textAlign',
    'color'
  ].forEach(key => {
    if (!filter.includes(key)) {
      if (key != 'rotate') {
        if (style[key] !== '') {
          result[key] = style[key]

          if (needUnit.includes(key)) {
            result[key] += 'px'
          }
        }
      } else {
        result['transform'] = key + '(' + style[key] + 'deg)'
      }
    }
  })

  return result
}

export function getItemAllStyle(item, filter = []) {
  const style = item.style
  const commonBackground = item.commonBackground
  const result = {}
  Object.keys(style).forEach(key => {
    if (!filter.includes(key)) {
      if (key != 'rotate') {
        if (style[key] !== '') {
          result[key] = style[key]

          if (needUnit.includes(key)) {
            result[key] += 'px'
          }
        }
      } else {
        result['transform'] = key + '(' + style[key] + 'deg)'
      }
    }

    if (commonBackground) {
      //附加背景样式
      let colorRGBA = ''
      if (commonBackground.backgroundColorSelect) {
        colorRGBA = hexColorToRGBA(commonBackground.backgroundColor, commonBackground.alpha)
      }
      if (commonBackground.backgroundImageEnable) {
        if (
          commonBackground.backgroundType === 'outerImage' &&
          typeof commonBackground.outerImage === 'string'
        ) {
          // 支持base64和传统URL
          const imageUrl = isBase64Image(commonBackground.outerImage)
            ? commonBackground.outerImage
            : imgUrlTrans(commonBackground.outerImage)
          result['background'] = `url(${imageUrl}) no-repeat ${colorRGBA}`
        } else {
          result['background-color'] = colorRGBA
        }
      } else {
        result['background-color'] = colorRGBA
      }
    }
  })

  return result
}

export function getStyle(style, filter = []) {
  const result = {}
  Object.keys(style).forEach(key => {
    if (!filter.includes(key)) {
      if (key != 'rotate') {
        if (style[key] !== '') {
          result[key] = style[key]

          if (needUnit.includes(key)) {
            result[key] += 'px'
          }
        }
      } else {
        result['transform'] = key + '(' + style[key] + 'deg)'
      }
    }
  })
  return result
}

// 获取一个组件旋转 rotate 后的样式
export function getComponentRotatedStyle(style) {
  style = { ...style }
  if (style.rotate != 0) {
    const newWidth = style.width * cos(style.rotate) + style.height * sin(style.rotate)
    const diffX = (style.width - newWidth) / 2 // 旋转后范围变小是正值，变大是负值
    style.left += diffX
    style.right = style.left + newWidth

    const newHeight = style.height * cos(style.rotate) + style.width * sin(style.rotate)
    const diffY = (newHeight - style.height) / 2 // 始终是正
    style.top -= diffY
    style.bottom = style.top + newHeight

    style.width = newWidth
    style.height = newHeight
  } else {
    style.bottom = style.top + style.height
    style.right = style.left + style.width
  }

  return style
}

export function getCanvasStyle(canvasStyleData, canvasId = 'canvas-main') {
  const {
    backgroundColorSelect,
    background,
    backgroundColor,
    backgroundImageEnable,
    fontSize,
    mobileSetting,
    fontFamily
  } = canvasStyleData
  const style = { fontSize: fontSize + 'px', color: canvasStyleData.color }
  if (isMainCanvas(canvasId)) {
    // 仪表板默认色#f5f6f7 大屏默认配色 #1a1a1a
    let colorRGBA = dvMainStore.dvInfo.type === 'dashboard' ? '#f5f6f7' : '#1a1a1a'
    if (backgroundColorSelect && backgroundColor) {
      colorRGBA = backgroundColor
    }
    if (backgroundImageEnable) {
      // 支持base64和传统URL
      const imageUrl = isBase64Image(background) ? background : imgUrlTrans(background)
      style['background'] = `url(${imageUrl}) no-repeat ${colorRGBA}`
    } else {
      style['background-color'] = colorRGBA
    }

    if (dvMainStore.mobileInPc && mobileSetting?.customSetting) {
      const { backgroundColorSelect, color, backgroundImageEnable, background } = mobileSetting
      if (backgroundColorSelect && backgroundImageEnable && typeof background === 'string') {
        style['background'] = `url(${imgUrlTrans(background)}) no-repeat ${color}`
      } else if (backgroundColorSelect) {
        style['background-color'] = color
      } else if (backgroundImageEnable) {
        style['background'] = `url(${imgUrlTrans(background)}) no-repeat`
      }
    }
    style['font-family'] = fontFamily + '!important'
  }

  return style
}

export function createGroupStyle(groupComponent) {
  const parentStyle = groupComponent.style
  groupComponent.propValue.forEach(component => {
    // 分组计算逻辑
    // 1.groupStyle记录left top width height 在出现分组缩放的时候进行等比例变更（缩放来源有两种a.整个大屏的缩放 b.分组尺寸的调整）
    // 2.component 内部进行位移或者尺寸的变更 要同步到这个比例中
    const style = { ...component.style }
    component.groupStyle.left = (style.left - parentStyle.left) / parentStyle.width
    component.groupStyle.top = (style.top - parentStyle.top) / parentStyle.height
    component.groupStyle.width = style.width / parentStyle.width
    component.groupStyle.height = style.height / parentStyle.height

    component.style.left = component.style.left - parentStyle.left
    component.style.top = component.style.top - parentStyle.top
  })
}

function dataVTabSizeStyleAdaptor(tabComponent) {
  const parentStyleAdaptor = { ...tabComponent.style }
  const offset = parentStyleAdaptor.showTabTitle ? 46 : 0
  const domId =
    dvMainStore.editMode === 'edit'
      ? 'component' + tabComponent.id
      : 'enlarge-inner-content' + tabComponent.id
  const tabDom = document.getElementById(domId)
  if (tabDom) {
    parentStyleAdaptor.height = tabDom.clientHeight - offset
    parentStyleAdaptor.width = tabDom.clientWidth
  } else {
    parentStyleAdaptor.height = parentStyleAdaptor.height - offset
  }

  // 检查propValue是否为数组，避免对非Tab组件调用forEach
  if (Array.isArray(tabComponent.propValue)) {
    tabComponent.propValue.forEach(tabItem => {
      if (tabItem.componentData && Array.isArray(tabItem.componentData)) {
        tabItem.componentData.forEach(tabComponent => {
          groupItemStyleAdaptor(tabComponent, parentStyleAdaptor)
          if (['Group'].includes(tabComponent.component)) {
            groupSizeStyleAdaptor(tabComponent)
          }
        })
      }
    })
  }
}

export function groupItemStyleAdaptor(component, parentStyle) {
  // 分组还原逻辑
  // 当发上分组缩放是，要将内部组件按照比例转换
  const styleScale = component.groupStyle
  component.style.left = parentStyle.width * styleScale.left
  component.style.top = parentStyle.height * styleScale.top
  component.style.width = parentStyle.width * styleScale.width
  component.style.height = parentStyle.height * styleScale.height
}

export function groupStyleRevertBatch(groupComponent, parentStyle) {
  if (groupComponent.component === 'DeTabs') {
    groupComponent.propValue.forEach(tabItem => {
      tabItem.componentData.forEach(tabComponent => {
        groupStyleRevert(tabComponent, parentStyle)
      })
    })
  }
  if (groupComponent.component === 'CardContainer') {
    if (groupComponent.propValue.componentData) {
      groupComponent.propValue.componentData.forEach(cardComponent => {
        groupStyleRevert(cardComponent, parentStyle)
      })
    }
  }
}

export function tabInnerStyleRevert(tabOuterComponent) {
  if (tabOuterComponent.component === 'DeTabs') {
    const parentStyle = {
      width: tabOuterComponent.style.width,
      height: tabOuterComponent.style.height - (tabOuterComponent.style.showTabTitle ? 46 : 0)
    }
    tabOuterComponent.propValue.forEach(tabItem => {
      tabItem.componentData.forEach(tabComponent => {
        groupStyleRevert(tabComponent, parentStyle)
      })
    })
  }
  if (tabOuterComponent.component === 'CardContainer') {
    const headerOffset = tabOuterComponent.propValue.showHeader ? 50 : 0
    const parentStyle = {
      width: tabOuterComponent.style.width,
      height: tabOuterComponent.style.height - headerOffset
    }
    if (tabOuterComponent.propValue.componentData) {
      tabOuterComponent.propValue.componentData.forEach(cardComponent => {
        groupStyleRevert(cardComponent, parentStyle)
      })
    }
  }
}

export function groupStyleRevert(innerComponent, parentStyle) {
  const innerStyle = { ...innerComponent.style }
  innerComponent.groupStyle.left = innerStyle.left / parentStyle.width
  innerComponent.groupStyle.top = innerStyle.top / parentStyle.height
  innerComponent.groupStyle.width = innerStyle.width / parentStyle.width
  innerComponent.groupStyle.height = innerStyle.height / parentStyle.height
}

export function groupSizeStyleAdaptor(groupComponent) {
  if (groupComponent.component === 'Group') {
    const parentStyle = groupComponent.style
    groupComponent.propValue.forEach(component => {
      groupItemStyleAdaptor(component, parentStyle)
    })
  } else if (groupComponent.component === 'DeTabs' ||
             (groupComponent.component === 'UserView' && groupComponent.innerType === 'tab')) {
    // 只对Tab组件调用dataVTabSizeStyleAdaptor
    dataVTabSizeStyleAdaptor(groupComponent)
  }
  // 对于其他组件（如卡片容器），不需要特殊处理
}

export function dataVTabComponentAdd(innerComponent, parentComponent) {
  //do dataVTabComponentAdd
  innerComponent.style.top = 0
  innerComponent.style.left = 0
  const parentStyleAdaptor = { ...parentComponent.style }
  // 去掉tab头部高度
  parentStyleAdaptor.height = parentStyleAdaptor.height - (parentComponent.showTabTitle ? 46 : 0)
  groupStyleRevert(innerComponent, parentStyleAdaptor)
}
