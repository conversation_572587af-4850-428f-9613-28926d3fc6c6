/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AppExportForm: typeof import('./src/components/de-app/AppExportForm.vue')['default']
    Area: typeof import('./src/components/data-visualization/canvas/Area.vue')['default']
    BackgroundImageUpload: typeof import('./src/components/visualization/common/BackgroundImageUpload.vue')['default']
    BackgroundItem: typeof import('./src/components/visualization/component-background/BackgroundItem.vue')['default']
    BackgroundItemOverall: typeof import('./src/components/visualization/component-background/BackgroundItemOverall.vue')['default']
    BackgroundOverallCommon: typeof import('./src/components/visualization/component-background/BackgroundOverallCommon.vue')['default']
    Board: typeof import('./src/components/de-board/Board.vue')['default']
    BoardItem: typeof import('./src/components/visualization/component-background/BoardItem.vue')['default']
    BorderOptionPrefix: typeof import('./src/components/visualization/component-background/BorderOptionPrefix.vue')['default']
    BusinessFormDialog: typeof import('./src/components/common/BusinessFormDialog.vue')['default']
    BusinessFormExample: typeof import('./src/components/common/BusinessFormExample.vue')['default']
    BusinessTableExample: typeof import('./src/components/common/BusinessTableExample.vue')['default']
    ButtonPublicComponentTest: typeof import('./src/components/common/ButtonPublicComponentTest.vue')['default']
    CanvasAttr: typeof import('./src/components/data-visualization/CanvasAttr.vue')['default']
    CanvasBackground: typeof import('./src/components/visualization/component-background/CanvasBackground.vue')['default']
    CanvasBaseSetting: typeof import('./src/components/visualization/CanvasBaseSetting.vue')['default']
    CanvasCacheDialog: typeof import('./src/components/visualization/CanvasCacheDialog.vue')['default']
    CanvasCore: typeof import('./src/components/data-visualization/canvas/CanvasCore.vue')['default']
    CanvasExtFullscreenBar: typeof import('./src/components/visualization/CanvasExtFullscreenBar.vue')['default']
    CanvasOptBar: typeof import('./src/components/visualization/CanvasOptBar.vue')['default']
    ChartConfigDialog: typeof import('./src/components/dialog-content/ChartConfigDialog.vue')['default']
    CollapseSwitchItem: typeof import('./src/components/collapse-switch-item/src/CollapseSwitchItem.vue')['default']
    ColorButton: typeof import('./src/components/assist-button/ColorButton.vue')['default']
    ColorScheme: typeof import('./src/components/color-scheme/src/ColorScheme.vue')['default']
    ColumnList: typeof import('./src/components/column-list/src/ColumnList.vue')['default']
    ComponentButton: typeof import('./src/components/visualization/ComponentButton.vue')['default']
    ComponentButtonLabel: typeof import('./src/components/visualization/ComponentButtonLabel.vue')['default']
    ComponentColorSelector: typeof import('./src/components/dashboard/subject-setting/dashboard-style/ComponentColorSelector.vue')['default']
    ComponentEditBar: typeof import('./src/components/visualization/ComponentEditBar.vue')['default']
    ComponentGroup: typeof import('./src/components/visualization/ComponentGroup.vue')['default']
    ComponentList: typeof import('./src/components/data-visualization/ComponentList.vue')['default']
    ComponentPosition: typeof import('./src/components/visualization/common/ComponentPosition.vue')['default']
    ComponentSelector: typeof import('./src/components/visualization/ComponentSelector.vue')['default']
    ComponentToolBar: typeof import('./src/components/data-visualization/ComponentToolBar.vue')['default']
    ComponentToPublicMixin: typeof import('./src/components/common/ComponentToPublicMixin.vue')['default']
    ComponentWrapper: typeof import('./src/components/data-visualization/canvas/ComponentWrapper.vue')['default']
    ComposeShow: typeof import('./src/components/data-visualization/canvas/ComposeShow.vue')['default']
    ConfigGlobal: typeof import('./src/components/config-global/src/ConfigGlobal.vue')['default']
    ContextMenu: typeof import('./src/components/data-visualization/canvas/ContextMenu.vue')['default']
    ContextMenuAsideDetails: typeof import('./src/components/data-visualization/canvas/ContextMenuAsideDetails.vue')['default']
    ContextMenuDetails: typeof import('./src/components/data-visualization/canvas/ContextMenuDetails.vue')['default']
    ConvertButtonTest: typeof import('./src/components/common/ConvertButtonTest.vue')['default']
    CreateTestComponentsButton: typeof import('./src/components/common/CreateTestComponentsButton.vue')['default']
    Cron: typeof import('./src/components/cron/src/Cron.vue')['default']
    CustomDialogManager: typeof import('./src/components/common/CustomDialogManager.vue')['default']
    CustomPassword: typeof import('./src/components/custom-password/src/CustomPassword.vue')['default']
    DashboardHiddenComponent: typeof import('./src/components/dashboard/DashboardHiddenComponent.vue')['default']
    DatasetParamsComponent: typeof import('./src/components/visualization/DatasetParamsComponent.vue')['default']
    DatasetParamsSettingDialog: typeof import('./src/components/visualization/DatasetParamsSettingDialog.vue')['default']
    Day: typeof import('./src/components/cron/src/Day.vue')['default']
    DbCanvasAttr: typeof import('./src/components/dashboard/DbCanvasAttr.vue')['default']
    DbDragArea: typeof import('./src/components/dashboard/DbDragArea.vue')['default']
    DbToolbar: typeof import('./src/components/dashboard/DbToolbar.vue')['default']
    DeEmpty: typeof import('./src/components/common/DeEmpty.vue')['default']
    DeFullscreen: typeof import('./src/components/visualization/common/DeFullscreen.vue')['default']
    DeGrid: typeof import('./src/components/data-visualization/DeGrid.vue')['default']
    DeGridScreen: typeof import('./src/components/data-visualization/DeGridScreen.vue')['default']
    DePreview: typeof import('./src/components/data-visualization/canvas/DePreview.vue')['default']
    DePreviewPopDialog: typeof import('./src/components/visualization/DePreviewPopDialog.vue')['default']
    DeUpload: typeof import('./src/components/visualization/common/DeUpload.vue')['default']
    DeUploadBase64: typeof import('./src/components/visualization/common/DeUploadBase64.vue')['default']
    DialogDesigner: typeof import('./src/components/common/DialogDesigner.vue')['default']
    DialogDesignerTest: typeof import('./src/components/common/DialogDesignerTest.vue')['default']
    DialogIntegrationExample: typeof import('./src/components/common/DialogIntegrationExample.vue')['default']
    DragInfo: typeof import('./src/components/visualization/common/DragInfo.vue')['default']
    DragShadow: typeof import('./src/components/data-visualization/canvas/DragShadow.vue')['default']
    DrawerEnumFilter: typeof import('./src/components/drawer-filter/src/DrawerEnumFilter.vue')['default']
    DrawerFilter: typeof import('./src/components/drawer-filter/src/DrawerFilter.vue')['default']
    DrawerMain: typeof import('./src/components/drawer-main/src/DrawerMain.vue')['default']
    DrawerTimeFilter: typeof import('./src/components/drawer-filter/src/DrawerTimeFilter.vue')['default']
    DrawerTreeFilter: typeof import('./src/components/drawer-filter/src/DrawerTreeFilter.vue')['default']
    DvHandleMore: typeof import('./src/components/handle-more/src/DvHandleMore.vue')['default']
    DvSidebar: typeof import('./src/components/visualization/DvSidebar.vue')['default']
    DvToolbar: typeof import('./src/components/data-visualization/DvToolbar.vue')['default']
    DynamicDialogContainer: typeof import('./src/components/common/DynamicDialogContainer.vue')['default']
    EditMenu: typeof import('./src/components/visualization/EditMenu.vue')['default']
    ElAside: typeof import('element-plus-secondary/es')['ElAside']
    ElBadge: typeof import('element-plus-secondary/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus-secondary/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus-secondary/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus-secondary/es')['ElButton']
    ElCheckbox: typeof import('element-plus-secondary/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus-secondary/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus-secondary/es')['ElCol']
    ElCollapse: typeof import('element-plus-secondary/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus-secondary/es')['ElCollapseItem']
    ElColorPicker: typeof import('element-plus-secondary/es')['ElColorPicker']
    ElContainer: typeof import('element-plus-secondary/es')['ElContainer']
    ElDatePicker: typeof import('element-plus-secondary/es')['ElDatePicker']
    ElDialog: typeof import('element-plus-secondary/es')['ElDialog']
    ElDivider: typeof import('element-plus-secondary/es')['ElDivider']
    ElDrawer: typeof import('element-plus-secondary/es')['ElDrawer']
    ElDropdown: typeof import('element-plus-secondary/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus-secondary/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus-secondary/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus-secondary/es')['ElEmpty']
    ElFooter: typeof import('element-plus-secondary/es')['ElFooter']
    ElForm: typeof import('element-plus-secondary/es')['ElForm']
    ElFormItem: typeof import('element-plus-secondary/es')['ElFormItem']
    ElHeader: typeof import('element-plus-secondary/es')['ElHeader']
    ElIcon: typeof import('element-plus-secondary/es')['ElIcon']
    ElInput: typeof import('element-plus-secondary/es')['ElInput']
    ElInputNumber: typeof import('element-plus-secondary/es')['ElInputNumber']
    ElMain: typeof import('element-plus-secondary/es')['ElMain']
    ElMenu: typeof import('element-plus-secondary/es')['ElMenu']
    ElOption: typeof import('element-plus-secondary/es')['ElOption']
    ElOptionGroup: typeof import('element-plus-secondary/es')['ElOptionGroup']
    ElPagination: typeof import('element-plus-secondary/es')['ElPagination']
    ElPopover: typeof import('element-plus-secondary/es')['ElPopover']
    ElProgress: typeof import('element-plus-secondary/es')['ElProgress']
    ElRadio: typeof import('element-plus-secondary/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus-secondary/es')['ElRadioGroup']
    ElRow: typeof import('element-plus-secondary/es')['ElRow']
    ElScrollbar: typeof import('element-plus-secondary/es')['ElScrollbar']
    ElSelect: typeof import('element-plus-secondary/es')['ElSelect']
    ElSelectV2: typeof import('element-plus-secondary/es')['ElSelectV2']
    ElSlider: typeof import('element-plus-secondary/es')['ElSlider']
    ElSpace: typeof import('element-plus-secondary/es')['ElSpace']
    ElSwitch: typeof import('element-plus-secondary/es')['ElSwitch']
    ElTable: typeof import('element-plus-secondary/es')['ElTable']
    ElTableColumn: typeof import('element-plus-secondary/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus-secondary/es')['ElTabPane']
    ElTabs: typeof import('element-plus-secondary/es')['ElTabs']
    ElTag: typeof import('element-plus-secondary/es')['ElTag']
    ElTimePicker: typeof import('element-plus-secondary/es')['ElTimePicker']
    ElTooltip: typeof import('element-plus-secondary/es')['ElTooltip']
    ElTree: typeof import('element-plus-secondary/es')['ElTree']
    ElTreeSelect: typeof import('element-plus-secondary/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus-secondary/es')['ElUpload']
    EmptyBackground: typeof import('./src/components/empty-background/src/EmptyBackground.vue')['default']
    EventList: typeof import('./src/components/data-visualization/EventList.vue')['default']
    FieldsList: typeof import('./src/components/visualization/FieldsList.vue')['default']
    FilterStyleSelector: typeof import('./src/components/dashboard/subject-setting/dashboard-style/FilterStyleSelector.vue')['default']
    FilterStyleSimpleSelector: typeof import('./src/components/dashboard/subject-setting/dashboard-style/FilterStyleSimpleSelector.vue')['default']
    FilterText: typeof import('./src/components/filter-text/src/FilterText.vue')['default']
    FormControlTest: typeof import('./src/components/common/FormControlTest.vue')['default']
    FormDialog: typeof import('./src/components/dialog-content/FormDialog.vue')['default']
    GlobalVariableManager: typeof import('./src/components/dashboard/GlobalVariableManager.vue')['default']
    GridTable: typeof import('./src/components/grid-table/src/GridTable.vue')['default']
    HandleMore: typeof import('./src/components/handle-more/src/HandleMore.vue')['default']
    Hour: typeof import('./src/components/cron/src/Hour.vue')['default']
    HyperlinksDialog: typeof import('./src/components/visualization/HyperlinksDialog.vue')['default']
    Icon: typeof import('./src/components/icon-custom/src/Icon.vue')['default']
    JumpSetOuterContentEditor: typeof import('./src/components/visualization/JumpSetOuterContentEditor.vue')['default']
    LinkageSet: typeof import('./src/components/visualization/LinkageSet.vue')['default']
    LinkageSetOption: typeof import('./src/components/visualization/LinkageSetOption.vue')['default']
    LinkJumpSet: typeof import('./src/components/visualization/LinkJumpSet.vue')['default']
    LinkOptBar: typeof import('./src/components/data-visualization/canvas/LinkOptBar.vue')['default']
    MarkLine: typeof import('./src/components/data-visualization/canvas/MarkLine.vue')['default']
    Modal: typeof import('./src/components/data-visualization/Modal.vue')['default']
    Month: typeof import('./src/components/cron/src/Month.vue')['default']
    Nolic: typeof import('./src/components/plugin/src/nolic.vue')['default']
    OuterParamsSet: typeof import('./src/components/visualization/OuterParamsSet.vue')['default']
    OverallSetting: typeof import('./src/components/dashboard/subject-setting/dashboard-style/OverallSetting.vue')['default']
    PGrid: typeof import('./src/components/data-visualization/canvas/PGrid.vue')['default']
    PluginComponent: typeof import('./src/components/plugin/src/PluginComponent.vue')['default']
    PointShadow: typeof import('./src/components/data-visualization/canvas/PointShadow.vue')['default']
    PublicComponentDialog: typeof import('./src/components/common/PublicComponentDialog.vue')['default']
    PublicComponentDialogTest: typeof import('./src/components/common/PublicComponentDialogTest.vue')['default']
    PublicComponentExample: typeof import('./src/components/common/PublicComponentExample.vue')['default']
    PublicComponentManager: typeof import('./src/components/common/PublicComponentManager.vue')['default']
    RealTimeGroup: typeof import('./src/components/data-visualization/RealTimeGroup.vue')['default']
    RealTimeGroupInner: typeof import('./src/components/data-visualization/RealTimeGroupInner.vue')['default']
    RealTimeListTree: typeof import('./src/components/data-visualization/RealTimeListTree.vue')['default']
    RealTimeTab: typeof import('./src/components/data-visualization/RealTimeTab.vue')['default']
    RelationChart: typeof import('./src/components/relation-chart/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SecondAndMinute: typeof import('./src/components/cron/src/SecondAndMinute.vue')['default']
    SeniorStyleSetting: typeof import('./src/components/dashboard/subject-setting/dashboard-style/SeniorStyleSetting.vue')['default']
    SettingMenu: typeof import('./src/components/visualization/SettingMenu.vue')['default']
    Shape: typeof import('./src/components/data-visualization/canvas/Shape.vue')['default']
    SimpleDialogDesigner: typeof import('./src/components/common/SimpleDialogDesigner.vue')['default']
    Slider: typeof import('./src/components/dashboard/subject-setting/pre-subject/Slider.vue')['default']
    Src: typeof import('./src/components/plugin/src/index.vue')['default']
    StreamMediaLinks: typeof import('./src/components/visualization/StreamMediaLinks.vue')['default']
    SubjectEditDialog: typeof import('./src/components/dashboard/subject-setting/pre-subject/SubjectEditDialog.vue')['default']
    SubjectTemplateItem: typeof import('./src/components/dashboard/subject-setting/pre-subject/SubjectTemplateItem.vue')['default']
    TabCarouselDialog: typeof import('./src/components/visualization/TabCarouselDialog.vue')['default']
    TableBody: typeof import('./src/components/grid-table/src/TableBody.vue')['default']
    TinymacEditorAlarm: typeof import('./src/components/rich-text/TinymacEditorAlarm.vue')['default']
    TinymceEditor: typeof import('./src/components/rich-text/TinymceEditor.vue')['default']
    TreeSelect: typeof import('./src/components/tree-select/src/TreeSelect.vue')['default']
    UserViewEnlarge: typeof import('./src/components/visualization/UserViewEnlarge.vue')['default']
    ViewSimpleTitle: typeof import('./src/components/dashboard/subject-setting/dashboard-style/ViewSimpleTitle.vue')['default']
    ViewTitle: typeof import('./src/components/dashboard/subject-setting/dashboard-style/ViewTitle.vue')['default']
    ViewTrackBar: typeof import('./src/components/visualization/ViewTrackBar.vue')['default']
    Week: typeof import('./src/components/cron/src/Week.vue')['default']
    Year: typeof import('./src/components/cron/src/Year.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus-secondary/es')['ElLoadingDirective']
  }
}
