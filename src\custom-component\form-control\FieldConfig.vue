<script setup lang="ts">
import { ref, computed, watch } from 'vue'
// import { ElDialog, ElForm, ElFormItem, ElInput, ElInputNumber, ElSelect, ElOption, ElSwitch, ElButton, ElIcon, ElMessage, ElRadioGroup, ElRadio } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus-secondary'
import type { FormField, SelectOption } from './types'
import { FIELD_TYPE_OPTIONS } from './types'
import { generateID } from '@/utils/generateID'
import { generateUniqueFieldName, fieldNeedsOptions, generateDefaultOptions } from './utils'

const props = defineProps<{
  visible: boolean
  field?: FormField
  existingFields: FormField[]
  isEdit?: boolean
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'confirm': [field: FormField]
  'cancel': []
}>()

// 表单引用
const formRef = ref()

// 表单数据
const formData = ref<Partial<FormField>>({})

// 表单验证规则
const rules = {
  label: [
    { required: true, message: '请输入字段标签', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    { 
      validator: (rule: any, value: string, callback: Function) => {
        if (!value) {
          callback()
          return
        }

        // 检查字段名称唯一性
        const isDuplicate = props.existingFields.some((field) => {
          // 编辑时排除自己
          if (props.isEdit && props.field && field.id === props.field.id) {
            return false
          }
          return field.name === value
        })

        if (isDuplicate) {
          callback(new Error('字段名称已存在'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  type: [
    { required: true, message: '请选择字段类型', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogTitle = computed(() => props.isEdit ? '编辑字段' : '添加字段')

const needsOptions = computed(() => fieldNeedsOptions(formData.value.type || ''))

// 初始化表单数据
const initFormData = () => {
  if (props.field && props.isEdit) {
    // 编辑模式，复制现有字段数据
    formData.value = {
      ...props.field,
      options: props.field.options ? [...props.field.options] : [],
      validation: props.field.validation ? { ...props.field.validation } : {}
    }
  } else {
    // 新增模式，设置默认值
    formData.value = {
      id: generateID(),
      type: 'text',
      label: '',
      name: generateUniqueFieldName(props.existingFields),
      placeholder: '',
      defaultValue: '',
      required: false,
      disabled: false,
      readonly: false,
      visible: true,
      order: props.existingFields.length + 1,
      options: [],
      validation: {}
    }
  }
}

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    initFormData()
  }
})

// 监听字段类型变化
watch(() => formData.value.type, (newType, oldType) => {
  if (newType !== oldType) {
    // 类型变化时，重置默认值和选项
    formData.value.defaultValue = getDefaultValueByType(newType)
    
    if (fieldNeedsOptions(newType)) {
      if (!formData.value.options || formData.value.options.length === 0) {
        formData.value.options = generateDefaultOptions(2)
      }
    } else {
      formData.value.options = []
    }
  }
})

// 根据字段类型获取默认值
const getDefaultValueByType = (type: string) => {
  switch (type) {
    case 'number':
      return null
    case 'checkbox':
      return []
    case 'date':
      return null
    default:
      return ''
  }
}

// 添加选项
const addOption = () => {
  if (!formData.value.options) {
    formData.value.options = []
  }
  
  formData.value.options.push({
    label: `选项${formData.value.options.length + 1}`,
    value: `option${formData.value.options.length + 1}`
  })
}

// 删除选项
const removeOption = (index: number) => {
  if (formData.value.options && formData.value.options.length > 1) {
    formData.value.options.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一个选项')
  }
}

// 确认保存
const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    // 表单验证
    await formRef.value.validate()

    // 验证选项配置
    if (needsOptions.value) {
      if (!formData.value.options || formData.value.options.length === 0) {
        ElMessage.error('请至少添加一个选项')
        return
      }

      // 检查选项是否有空值
      const hasEmptyOption = formData.value.options.some(option => !option.label || !option.value)
      if (hasEmptyOption) {
        ElMessage.error('选项的标签和值不能为空')
        return
      }
    }

    // 构建完整的字段对象
    const field: FormField = {
      id: formData.value.id || generateID(),
      type: formData.value.type || 'text',
      label: formData.value.label || '',
      name: formData.value.name || '',
      placeholder: formData.value.placeholder,
      defaultValue: formData.value.defaultValue,
      required: formData.value.required || false,
      disabled: formData.value.disabled || false,
      readonly: formData.value.readonly || false,
      visible: formData.value.visible !== false,
      order: formData.value.order || 1,
      options: needsOptions.value ? formData.value.options : undefined,
      validation: formData.value.validation
    }

    // 发送确认事件
    emit('confirm', field)

    // 关闭弹框
    dialogVisible.value = false

  } catch (error) {
    console.error('表单验证失败:', error)
    // 验证失败时不关闭弹框，让用户修正错误
    return
  }
}

// 取消
const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  initFormData()
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :before-close="handleCancel"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      size="default"
    >
      <el-form-item label="字段类型" prop="type">
        <el-select
          v-model="formData.type"
          placeholder="请选择字段类型"
          style="width: 100%"
        >
          <el-option
            v-for="option in FIELD_TYPE_OPTIONS"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="字段标签" prop="label">
        <el-input
          v-model="formData.label"
          placeholder="请输入字段标签"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="字段名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入字段名称（用于数据提交）"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="占位符">
        <el-input
          v-model="formData.placeholder"
          placeholder="请输入占位符文本"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="默认值">
        <el-input
          v-if="formData.type !== 'number'"
          v-model="formData.defaultValue"
          placeholder="请输入默认值"
        />
        <el-input-number
          v-else
          v-model="formData.defaultValue"
          placeholder="请输入默认值"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="字段属性">
        <div class="field-properties">
          <el-checkbox v-model="formData.required">必填</el-checkbox>
          <el-checkbox v-model="formData.disabled">禁用</el-checkbox>
          <el-checkbox v-model="formData.readonly">只读</el-checkbox>
          <el-checkbox v-model="formData.visible">显示</el-checkbox>
        </div>
      </el-form-item>
      
      <!-- 选项配置 -->
      <el-form-item v-if="needsOptions" label="选项配置">
        <div class="options-config">
          <div class="options-header">
            <span>配置选项</span>
            <el-button
              size="small"
              type="primary"
              text
              @click="addOption"
            >
              <el-icon><Plus /></el-icon>
              添加选项
            </el-button>
          </div>
          
          <div
            v-for="(option, index) in formData.options"
            :key="index"
            class="option-item"
          >
            <el-input
              v-model="option.label"
              placeholder="选项标签"
              size="small"
            />
            <el-input
              v-model="option.value"
              placeholder="选项值"
              size="small"
            />
            <el-button
              size="small"
              type="danger"
              text
              @click="removeOption(index)"
              :disabled="formData.options && formData.options.length <= 1"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
      </el-form-item>
      
      <!-- 验证规则配置 -->
      <el-form-item label="验证规则">
        <div class="validation-config">
          <el-form-item label="最小长度" size="small" style="margin-bottom: 12px;">
            <el-input-number
              v-model="formData.validation.min"
              :min="0"
              size="small"
              style="width: 120px"
            />
          </el-form-item>

          <el-form-item label="最大长度" size="small" style="margin-bottom: 12px;">
            <el-input-number
              v-model="formData.validation.max"
              :min="0"
              size="small"
              style="width: 120px"
            />
          </el-form-item>

          <el-form-item label="正则表达式" size="small" style="margin-bottom: 12px;">
            <el-input
              v-model="formData.validation.pattern"
              placeholder="请输入正则表达式"
              size="small"
            />
          </el-form-item>

          <el-form-item label="错误提示" size="small" style="margin-bottom: 0;">
            <el-input
              v-model="formData.validation.message"
              placeholder="请输入验证失败时的错误提示"
              size="small"
            />
          </el-form-item>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
.field-properties {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.options-config {
  width: 100%;
  
  .options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
    color: #606266;
  }
  
  .option-item {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    align-items: center;
    
    .el-input {
      flex: 1;
    }
  }
}

.validation-config {
  width: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
  
  .el-form-item {
    margin-bottom: 12px;
    
    :deep(.el-form-item__label) {
      font-size: 12px;
      color: #606266;
    }
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button + .el-button {
    margin-left: 8px;
  }
}
</style>
