<template>
  <div
    class="card-container"
    :class="{ 'canvas-active-custom': element['canvasActive'] }"
    :tab-is-check="isEdit"
    :component-type="'CardContainer'"
    @dblclick="setCanvasActive"
  >
    <!-- 卡片头部 -->
    <div 
      v-if="element.propValue.showHeader && element.propValue.title"
      class="card-header"
      :style="headerStyle"
    >
      <div class="card-title" :style="titleStyle">
        {{ element.propValue.title }}
      </div>
      <div v-if="element.propValue.showExtra" class="card-extra">
        <slot name="extra"></slot>
      </div>
    </div>
    
    <!-- 卡片内容区域 -->
    <div class="card-body" :style="bodyStyle"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
    >
      <!-- 容器组件，支持叠加其他组件 -->
      <de-canvas
        v-if="isEdit && !mobileInPc"
        :ref="'cardCanvas'"
        :component-data="element.propValue.componentData || []"
        :canvas-style-data="canvasStyleData"
        :canvas-view-info="canvasViewInfo"
        :canvas-id="element.id + '--card'"
        :class="moveActive ? 'canvas-move-in' : ''"
        :canvas-position="'tab'"
        :canvas-active="true"
        :font-family="fontFamily"
      />
      <de-preview
        v-else
        :ref="'cardPreview'"
        :dv-info="dvInfo"
        :cur-gap="curPreviewGap"
        :component-data="element.propValue.componentData || []"
        :canvas-style-data="{}"
        :canvas-view-info="canvasViewInfo"
        :canvas-id="element.id + '--card'"
        :preview-active="true"
        :show-position="showPosition"
        :outer-scale="scale"
        :font-family="fontFamily"
        :outer-search-count="searchCount"
      />
      <!-- 默认内容 - 只在非编辑模式且无子组件时显示 -->
      <div v-if="!isEdit && (!element.propValue.componentData || element.propValue.componentData.length === 0)" class="card-placeholder" :style="placeholderStyle">
        <div class="placeholder-text">
          {{ propValue.placeholder || '暂无内容' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs, nextTick, onMounted, onBeforeUnmount, watch, ref, getCurrentInstance } from 'vue'
import DeCanvas from '@/views/canvas/DeCanvas.vue'
import DePreview from '@/components/data-visualization/canvas/DePreview.vue'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import { storeToRefs } from 'pinia'
import eventBus from '@/utils/eventBus'
import { isDashboard, canvasChangeAdaptor, findComponentIndexById } from '@/utils/canvasUtils'
import { dataVTabComponentAdd, groupItemStyleAdaptor } from '@/utils/style'

const props = withDefaults(
  defineProps<{
    canvasStyleData?: any
    canvasViewInfo?: any
    dvInfo?: any
    element?: any
    showPosition?: string
    isEdit?: boolean
    scale?: number
    searchCount?: number
    fontFamily?: string
  }>(),
  {
    canvasStyleData: () => ({}),
    canvasViewInfo: () => ({}),
    dvInfo: () => ({}),
    element: () => ({
      propValue: {
        title: '卡片标题',
        showHeader: true,
        showExtra: false,
        placeholder: '暂无内容',
        componentData: [],
        headerStyle: {
          backgroundColor: '#f5f5f5',
          borderBottom: '1px solid #e8e8e8',
          padding: '12px 16px',
          fontSize: '16px',
          fontWeight: 'bold',
          color: '#333'
        },
        bodyStyle: {
          padding: '16px',
          minHeight: '100px'
        },
        cardStyle: {
          border: '1px solid #e8e8e8',
          borderRadius: '6px',
          backgroundColor: '#fff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }
      }
    }),
    showPosition: 'canvas',
    isEdit: false,
    scale: 100,
    searchCount: 0,
    fontFamily: ''
  }
)

const dvMainStore = dvMainStoreWithOut()
const { tabMoveInActiveId } = storeToRefs(dvMainStore)

const { canvasStyleData, element, dvInfo, showPosition, canvasViewInfo, isEdit, scale, searchCount, fontFamily } = toRefs(props)

// 从element中获取propValue，这是标准的组件数据结构
const propValue = computed(() => element.value.propValue || {})



// 拖拽移入状态
const moveActive = computed(() => {
  return tabMoveInActiveId.value === element.value.id
})

// 移动端预览
const mobileInPc = ref(false)

// 预览间隙
const curPreviewGap = ref(0)



// 当前组件实例 - 立即获取
let currentInstance = getCurrentInstance()

// 鼠标事件处理
const handleMouseEnter = () => {
  if (isEdit.value) {
    element.value.canvasActive = true
  }
}

const handleMouseLeave = () => {
  if (isEdit.value && !dvMainStore.curComponent) {
    element.value.canvasActive = false
  }
}

// 卡片容器样式适配函数
const cardContainerSizeStyleAdaptor = () => {
  const parentStyleAdaptor = { ...element.value.style }
  const headerOffset = propValue.value.showHeader ? 50 : 0
  const domId = dvMainStore.editMode === 'edit'
    ? 'component' + element.value.id
    : 'enlarge-inner-content' + element.value.id

  const cardDom = document.getElementById(domId)
  if (cardDom) {
    parentStyleAdaptor.height = cardDom.clientHeight - headerOffset
    parentStyleAdaptor.width = cardDom.clientWidth
  } else {
    parentStyleAdaptor.height = parentStyleAdaptor.height - headerOffset
  }

  // 对内部组件进行样式适配
  if (propValue.value.componentData && propValue.value.componentData.length > 0) {
    propValue.value.componentData.forEach((childComponent: any) => {
      groupItemStyleAdaptor(childComponent, parentStyleAdaptor)
    })
  }
}





// 头部样式
const headerStyle = computed(() => {
  const fontSize = propValue.value.headerStyle.fontSize
  const style = {
    ...propValue.value.headerStyle,
    fontSize: typeof fontSize === 'number' ? fontSize + 'px' : fontSize,
    borderTopLeftRadius: propValue.value.cardStyle.borderRadius,
    borderTopRightRadius: propValue.value.cardStyle.borderRadius
  }

  // 处理背景图片
  if (propValue.value.headerStyle.backgroundImageEnable && propValue.value.headerStyle.backgroundImage) {
    style.backgroundImage = `url(${propValue.value.headerStyle.backgroundImage})`
    style.backgroundSize = propValue.value.headerStyle.backgroundSize
    style.backgroundPosition = propValue.value.headerStyle.backgroundPosition
    style.backgroundRepeat = propValue.value.headerStyle.backgroundRepeat
  }

  return style
})

// 标题样式
const titleStyle = computed(() => {
  const fontSize = propValue.value.headerStyle.fontSize
  return {
    fontSize: typeof fontSize === 'number' ? fontSize + 'px' : fontSize,
    fontWeight: propValue.value.headerStyle.fontWeight,
    color: propValue.value.headerStyle.color,
    margin: 0
  }
})

// 内容区域样式
const bodyStyle = computed(() => {
  const baseStyle = {
    ...propValue.value.bodyStyle,
    borderBottomLeftRadius: propValue.value.cardStyle.borderRadius,
    borderBottomRightRadius: propValue.value.cardStyle.borderRadius,
    position: 'relative',
    overflow: 'hidden'
  }

  // 编辑模式下移除padding，让de-canvas占满整个容器
  if (isEdit.value) {
    baseStyle.padding = '0'
  }

  return baseStyle
})

// 占位符样式
const placeholderStyle = computed(() => {
  return {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    minHeight: propValue.value.bodyStyle.minHeight || '100px',
    color: '#999',
    fontSize: '14px',
    textAlign: 'center'
  }
})

// 设置画布激活状态
const setCanvasActive = () => {
  if (isEdit.value) {
    // 找到当前组件在组件数据中的索引
    const componentData = dvMainStore.componentData
    const index = componentData.findIndex(comp => comp.id === element.value.id)
    if (index !== -1) {
      dvMainStore.setCurComponent({ component: element.value, index })
    }
  }
}

// 组件移入处理
const componentMoveIn = (component: any) => {
  console.log('卡片容器：开始处理组件移入', component.id)
  if (!propValue.value.componentData) {
    propValue.value.componentData = []
    console.log('卡片容器：初始化componentData数组')
  }

  if (isDashboard()) {
    // 仪表板模式 - 与Tab组件保持一致的处理方式
    eventBus.emit('removeMatrixItemById-canvas-main', component.id)
    dvMainStore.setCurComponent({ component: null, index: null })
    component.canvasId = element.value.id + '--card'

    // 获取de-canvas实例（CardContainer只有一个canvas，不需要索引）
    const refInstance = currentInstance?.refs?.['cardCanvas'] as any
    if (refInstance) {
      console.log('卡片容器：使用de-canvas进行布局处理')
      const matrixBase = refInstance.getBaseMatrixSize() // 获取矩阵基础大小
      canvasChangeAdaptor(component, matrixBase)
      component.x = 1
      component.y = 200
      component.style.left = 0
      component.style.top = 0
      propValue.value.componentData.push(component)
      refInstance.addItemBox(component) // 在适当的时候初始化布局组件
      nextTick(() => {
        refInstance.canvasInitImmediately()
      })
    } else {
      console.log('卡片容器：使用默认布局处理')
      // 如果refInstance不存在，使用默认的布局适配
      const matrixBase = { cellWidth: 10, cellHeight: 10 }
      canvasChangeAdaptor(component, matrixBase)
      component.x = 1
      component.y = 1
      component.style.left = 0
      component.style.top = 0
      propValue.value.componentData.push(component)
    }
    console.log('卡片容器：组件移入成功', component.id, propValue.value.componentData.length)
  } else {
    // 大屏模式
    const curIndex = findComponentIndexById(component.id)
    // 从主画布删除
    dvMainStore.deleteComponent(curIndex)
    dvMainStore.setCurComponent({ component: null, index: null })
    component.canvasId = element.value.id + '--card'
    dataVTabComponentAdd(component, element.value)
    propValue.value.componentData.push(component)
    console.log('卡片容器：组件移入成功（大屏模式）', component.id, propValue.value.componentData.length)
  }
}

// 组件移出处理
const componentMoveOut = (component: any) => {
  if (isDashboard()) {
    canvasChangeAdaptor(component, { cellWidth: 10, cellHeight: 10 }, true)
  }

  // 从卡片容器中移除
  const index = propValue.value.componentData.findIndex((child: any) => child.id === component.id)
  if (index !== -1) {
    propValue.value.componentData.splice(index, 1)
  }

  dvMainStore.setCurComponent({ component: null, index: null })

  // 添加到主画布
  if (isDashboard()) {
    eventBus.emit('moveOutFromTab-canvas-main', component)
  } else {
    addToMain(component)
  }
}

// 添加到主画布
const addToMain = (component: any) => {
  const { left, top } = element.value.style
  component.style.left = component.style.left + left
  component.style.top = component.style.top + top
  component.canvasId = 'canvas-main'
  dvMainStore.addComponent({
    component,
    index: undefined,
    isFromGroup: true
  })
}

onMounted(() => {
  nextTick(() => {
    // 初始化子组件数据
    if (!propValue.value.componentData) {
      propValue.value.componentData = []
    }





    // 确保de-canvas在编辑模式下立即初始化
    if (isEdit.value) {
      nextTick(() => {
        const refInstance = currentInstance?.refs?.['cardCanvas'] as any
        if (refInstance && typeof refInstance.canvasInitImmediately === 'function') {
          refInstance.canvasInitImmediately()
          console.log('卡片容器：de-canvas初始化完成')
          // 初始化时执行样式适配
          nextTick(() => {
            cardContainerSizeStyleAdaptor()
          })
        }
      })
    }
  })

  // 监听组件移入事件（使用与Tab组件相同的事件名称）
  eventBus.on('onTabMoveIn-' + element.value.id, componentMoveIn)
  eventBus.on('onTabMoveOut-' + element.value.id, componentMoveOut)
})

// 监听容器大小变化，自动调整内部组件
watch(
  () => [element.value.style?.width, element.value.style?.height],
  () => {
    if (isEdit.value && propValue.value.componentData && propValue.value.componentData.length > 0) {
      nextTick(() => {
        cardContainerSizeStyleAdaptor()
        // 通知de-canvas重新计算布局
        const refInstance = currentInstance?.refs?.['cardCanvas'] as any
        if (refInstance && typeof refInstance.canvasSizeInit === 'function') {
          refInstance.canvasSizeInit()
        }
      })
    }
  },
  { deep: true }
)

onBeforeUnmount(() => {
  // 清理事件监听
  eventBus.off('onTabMoveIn-' + element.value.id, componentMoveIn)
  eventBus.off('onTabMoveOut-' + element.value.id, componentMoveOut)
})
</script>

<style lang="less" scoped>
.card-container {
  width: 100%;
  height: 100%;
  border: v-bind('propValue.cardStyle.border');
  border-radius: v-bind('propValue.cardStyle.borderRadius');
  background-color: v-bind('propValue.cardStyle.backgroundColor');
  box-shadow: v-bind('propValue.cardStyle.boxShadow');
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &.canvas-active-custom {
    border: 2px solid #409eff !important;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;

    .card-title {
      flex: 1;
    }

    .card-extra {
      flex-shrink: 0;
    }
  }

  .card-body {
    flex: 1;
    position: relative;

    .card-placeholder {
      .placeholder-text {
        user-select: none;
        pointer-events: none;
      }
    }

    // de-canvas样式，确保正确填充容器
    :deep(.content) {
      width: 100%;
      height: 100%;

      .db-canvas {
        width: 100%;
        height: 100%;
      }
    }
  }
}

/* 拖拽移入指示线样式 */
:deep(.canvas-move-in) {
  border: 2px dotted transparent;
  border-color: blueviolet;
}
</style>
