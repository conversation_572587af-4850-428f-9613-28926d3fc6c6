export default {
  server: {
    proxy: {
      '/api/f': {
        target: 'https://demo.dataease.cn',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api\/f/, '')
      },
      // 使用 proxy 实例
      '/api': {
        target: 'http://172.29.1.47:8088',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api/, 'prod-api/dvp-server')
      },
      '/design/proxy': {
        target: 'http://172.29.1.47:8088',
        changeOrigin: true
        // rewrite: path => path.replace(/^\/api/, 'prod-api/dvp-server')
      }
    },
    port: 8080
  }
}
