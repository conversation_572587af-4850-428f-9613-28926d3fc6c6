import request from '@/config/axios'

/**
 * 查询在线地图密钥配置
 * 调用场景：
 * 1. 系统参数管理页面初始化时获取地图配置 (OnlineMap.vue)
 * 2. 地图组件渲染时获取API密钥 (l7.ts, BasicStyleSelector.vue)
 * 3. 地图图表类型初始化时获取密钥配置
 * @returns 返回地图密钥配置信息 {key, securityCode, mapType}
 */
export const queryMapKeyApi = () => request.get({ url: '/sysParameter/queryOnlineMap' })

/**
 * 根据地图类型查询特定的地图密钥配置
 * 调用场景：
 * 1. 系统参数管理页面按地图类型加载配置 (OnlineMap.vue)
 * @param type 地图类型 ('gaode'|'qq'|'tianditu')
 * @returns 返回指定类型的地图密钥配置
 */
export const queryMapKeyApiByType = (type: string) =>
  request.get({ url: `/sysParameter/queryOnlineMap/${type}` })

/**
 * 保存地图密钥配置
 * 调用场景：
 * 1. 系统参数管理页面保存地图API密钥 (OnlineMap.vue)
 * @param data 地图配置数据 {key, securityCode, mapType}
 * @returns 保存结果
 */
export const saveMapKeyApi = data => request.post({ url: '/sysParameter/saveOnlineMap', data })
