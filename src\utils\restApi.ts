/**
 * REST API 统一工具类
 * 用于统一处理REST数据源的API调用，避免代码重复
 */

import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import _ from 'lodash'
import router from '@/router'
import { useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()

export interface RestConfig {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers: Array<{ key: string; value: string }>
  params: Array<{ key: string; value: string }>
  body: string
  timeout: number
  dataPath?: string
  pagination?: {
    enabled: boolean
    pageParam: string
    sizeParam: string
    pageSize: number
  }
}

export interface RestApiOptions {
  enableMockData?: boolean // 是否在失败时启用模拟数据
  logPrefix?: string // 日志前缀，用于区分调用来源
  resolveVariables?: boolean // 是否解析全局变量，默认为true
}

/**
 * 解析配置中的全局变量引用
 * @param config 原始配置
 * @returns 解析后的配置
 */
const resolveConfigVariables = (config: RestConfig): RestConfig => {
  try {
    const dvMainStore = dvMainStoreWithOut()

    // 深拷贝配置以避免修改原始对象
    const resolvedConfig = JSON.parse(JSON.stringify(config))

    // 解析URL中的变量
    resolvedConfig.url = dvMainStore.resolveVariableReferences(config.url)

    // 解析请求头中的变量
    if (resolvedConfig.headers) {
      resolvedConfig.headers = resolvedConfig.headers.map((header: any) => ({
        key: dvMainStore.resolveVariableReferences(header.key),
        value: dvMainStore.resolveVariableReferences(header.value)
      }))
    }

    // 解析请求参数中的变量
    if (resolvedConfig.params) {
      resolvedConfig.params = resolvedConfig.params.map((param: any) => ({
        key: dvMainStore.resolveVariableReferences(param.key),
        value: dvMainStore.resolveVariableReferences(param.value)
      }))
    }

    // 解析请求体中的变量
    if (resolvedConfig.body) {
      resolvedConfig.body = dvMainStore.resolveVariableReferences(config.body)
    }

    // 解析数据路径中的变量
    if (resolvedConfig.dataPath) {
      resolvedConfig.dataPath = dvMainStore.resolveVariableReferences(config.dataPath)
    }

    return resolvedConfig
  } catch (error) {
    console.warn('解析全局变量时出错，使用原始配置:', error)
    return config
  }
}

/**
 * 统一的REST API调用方法
 * @param config REST配置
 * @param options 调用选项
 * @returns Promise<any> API响应数据
 */
export const callRestApi = async (
  config: RestConfig,
  options: RestApiOptions = {}
): Promise<any> => {
  const { enableMockData = false, logPrefix = 'REST API', resolveVariables = true } = options

  try {
    // 验证配置
    if (!config || !config.url) {
      throw new Error('REST配置不完整：缺少URL')
    }

    // 创建配置的深拷贝，避免修改原始配置
    const workingConfig = JSON.parse(JSON.stringify(config))

    // 确保 headers 存在且为数组格式
    if (!workingConfig.headers) {
      workingConfig.headers = []
    }

    // 检查是否已存在 Authorization 头
    const hasAuthHeader = workingConfig.headers.some(
      header => header.key === 'Authorization' || header.key === 'authorization'
    )

    if (!hasAuthHeader) {
      // 优先级：路由参数 > localStorage > 硬编码默认值
      const routeToken =
        router.currentRoute.value.query.token || router.currentRoute.value.params.token

      const localStorageToken = wsCache.get('user.token')

      let authToken = ''
      if (routeToken) {
        // 如果路由参数中有 token，使用路由参数的 token
        authToken = routeToken as string
        console.log('使用路由参数中的 token')
      } else if (localStorageToken) {
        // 如果 localStorage 中有 token，使用 localStorage 的 token
        authToken = localStorageToken
        console.log('使用 localStorage 中的 token')
      } else {
        // 最后使用默认的硬编码 token
        authToken =
          'eyJhbGciOiJSUzUxMiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************.DyAXPRQwEyQupnBlHFBPcp74X43uOz9mV1EuvTExuo-JqEx_skgvxPrP_0VMViQuZYYut_V2S5lXx5gQN6-lxbMixLsSTafsWLrL_OddmR7SJMvUoQwklt8EJYdUE9ncrxQGveo2RstX5LRpv4_15eU9xVOLrYAXSkcVlVjMP_9qcCjzGZokTYwDste1s8_ezil0PqRSZzVqIRKMLZB7H5LjPGkqc2iLX2VQcmMF3tCWJjkvgF7SZDSo_KFbL6ND6PxDAuewKaRkB5KnNtWgMxZi__1rfJ5_T83LGrMqR2CKWLpRSeXKN1ZycSg2X6CyL0m7BSd2laNOl2Mc0Sn0wQ'
        console.log('使用默认硬编码 token')
      }

      // 添加 Authorization 头
      workingConfig.headers.push({ key: 'Authorization', value: authToken })
    }

    // 如果请求路径不包含 prod-api/dev-server，则转发至代理接口
    if (!workingConfig.url.includes('prod-api/dev-server')) {
      // 对于 GET 请求，将参数添加到 URL 中
      if (workingConfig.method === 'GET' && !_.isEmpty(workingConfig.params)) {
        console.log('处理GET请求参数:', {
          isArray: Array.isArray(workingConfig.params),
          params: workingConfig.params
        })

        // 检查参数格式，如果是数组格式则转换为对象格式
        if (Array.isArray(workingConfig.params)) {
          const paramsObj = {}
          workingConfig.params.forEach(param => {
            if (param.key && param.value !== undefined && param.value !== null) {
              paramsObj[param.key] = param.value
            }
          })
          console.log('转换后的参数对象:', paramsObj)
          workingConfig.url += transformUrlParamObj(paramsObj)
        } else {
          workingConfig.url += transformUrlParamObj(workingConfig.params)
        }

        console.log('最终请求URL:', workingConfig.url)
        workingConfig.params = null
      }

      // 设置代理 URL
      const preUrl = import.meta.env.DEV ? '/design/proxy' : '/proxy'

      // 检查是否已存在 Target-Url 头
      const existTargetUrl = workingConfig.headers.find(item => item.key === 'Target-Url')
      if (!existTargetUrl) {
        workingConfig.headers.push({ key: 'Target-Url', value: workingConfig.url })
      }

      // 将请求 URL 替换为代理 URL
      workingConfig.url = `${preUrl}`
    }

    console.log('实际请求URL:', workingConfig.url)
    console.log('原始配置URL保持不变:', config.url)

    // 解析全局变量
    const resolvedConfig = resolveVariables ? resolveConfigVariables(workingConfig) : workingConfig

    console.log(`${logPrefix} 开始调用:`, {
      url: resolvedConfig.url,
      method: resolvedConfig.method,
      paramsCount: resolvedConfig.params?.length || 0,
      headersCount: resolvedConfig.headers?.length || 0,
      timeout: resolvedConfig.timeout,
      variablesResolved: resolveVariables
    })

    // 构建请求URL，包含参数
    const requestUrl = buildRequestUrl(resolvedConfig.url, resolvedConfig.params)

    // 构建请求头
    const requestHeaders = buildRequestHeaders(resolvedConfig.headers)

    // 构建请求选项
    const requestOptions = buildRequestOptions(resolvedConfig, requestHeaders)

    console.log(`${logPrefix} 请求配置:`, {
      url: requestUrl,
      method: requestOptions.method,
      headers: requestHeaders,
      hasBody: !!requestOptions.body
    })

    // 发送REST请求
    const response = await fetch(requestUrl, requestOptions)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log(`${logPrefix} 获取数据成功:`, {
      dataType: typeof data,
      isArray: Array.isArray(data),
      keys: data && typeof data === 'object' ? Object.keys(data) : [],
      sampleRecord: data && data.data && data.data.records ? data.data.records[0] : null,
      nameFieldType: data && data.data && data.data.records && data.data.records[0] ? typeof data.data.records[0].name : 'unknown'
    })

    return data
  } catch (error) {
    console.error(`${logPrefix} 调用失败:`, error)

    // 如果启用模拟数据且是网络错误，返回模拟数据
    if (enableMockData && (error.name === 'AbortError' || error.message.includes('fetch'))) {
      console.warn(`${logPrefix} 使用模拟数据`)
      return getMockData()
    }

    throw error
  }
}

/**
 * 构建请求URL，包含查询参数
 */
const buildRequestUrl = (
  baseUrl: string,
  params: Array<{ key: string; value: string }> = []
): string => {
  let requestUrl = baseUrl

  if (params && params.length > 0) {
    const urlParams = new URLSearchParams()
    params.forEach(param => {
      if (param.key && param.value) {
        urlParams.append(param.key, param.value)
      }
    })

    const paramString = urlParams.toString()
    if (paramString) {
      requestUrl += (baseUrl.includes('?') ? '&' : '?') + paramString
    }
  }

  return requestUrl
}

/**
 * 构建请求头
 */
const buildRequestHeaders = (
  headers: Array<{ key: string; value: string }> = []
): Record<string, string> => {
  const requestHeaders: Record<string, string> = {}

  headers.forEach(header => {
    if (header.key && header.value) {
      requestHeaders[header.key] = header.value
    }
  })

  return requestHeaders
}

/**
 * 构建请求选项
 */
const buildRequestOptions = (config: RestConfig, headers: Record<string, string>): RequestInit => {
  const requestOptions: RequestInit = {
    method: config.method || 'GET',
    headers: headers,
    signal: AbortSignal.timeout((config.timeout || 30) * 1000)
  }

  // 添加请求体（仅POST/PUT）
  if ((config.method === 'POST' || config.method === 'PUT') && config.body) {
    requestOptions.body = config.body
  }

  return requestOptions
}

/**
 * 获取模拟数据
 */
const getMockData = () => {
  return {
    items: [
      { id: 1, name: '测试数据1', value: 100, date: '2023-01-01', category: 'A', active: true },
      { id: 2, name: '测试数据2', value: 200, date: '2023-01-02', category: 'B', active: false },
      { id: 3, name: '测试数据3', value: 150, date: '2023-01-03', category: 'A', active: true }
    ],
    total: 3,
    success: true
  }
}

/**
 * 应用数据路径提取
 * @param data 原始数据
 * @param dataPath 数据路径，如 'data.items'
 * @returns 提取后的数据
 */
export const applyDataPath = (data: any, dataPath?: string): any => {
  if (!dataPath || !dataPath.trim()) {
    return data
  }

  let targetData = data
  const paths = dataPath.split('.')

  for (const path of paths) {
    if (targetData && typeof targetData === 'object' && path in targetData) {
      targetData = targetData[path]
    } else {
      console.warn(`数据路径 ${path} 不存在，跳过路径提取`)
      return data // 回退到原始数据
    }
  }

  return targetData
}

/**
 * 确保数据是数组格式
 * @param data 数据
 * @returns 数组格式的数据
 */
export const ensureArrayData = (data: any): any[] => {
  if (Array.isArray(data)) {
    return data
  }

  if (data && typeof data === 'object') {
    return [data]
  }

  return []
}

/**
 * REST数据预处理
 * 包含数据路径提取和数组格式确保
 * @param data 原始数据
 * @param config REST配置
 * @returns 处理后的数据
 */
export const preprocessRestData = (data: any, config: RestConfig): any[] => {
  // 应用数据路径
  const extractedData = applyDataPath(data, config.dataPath)

  // 确保是数组格式
  return ensureArrayData(extractedData)
}

/**
 * 图表组件专用的REST数据获取函数
 * 包含完整的数据获取、转换和错误处理流程
 * @param view 图表视图对象
 * @param componentType 组件类型，用于日志标识
 * @returns Promise<any> 转换后的REST数据
 */
export const fetchRestDataForChart = async (view: any, componentType: string): Promise<any> => {
  try {
    if (!view.restConfig || !view.restConfig.url) {
      throw new Error('REST配置不完整：缺少URL')
    }

    if (!view.restFields || view.restFields.length === 0) {
      throw new Error('REST字段配置不完整')
    }

    console.log(`${componentType} 开始获取REST数据:`, {
      url: view.restConfig.url,
      method: view.restConfig.method,
      fieldsCount: view.restFields.length
    })

    // 调用REST API
    const restData = await callRestApi(view.restConfig, {
      enableMockData: false, // 图表组件不使用模拟数据
      logPrefix: componentType
    })

    // 预处理数据
    const processedData = preprocessRestData(restData, view.restConfig)

    console.log(`${componentType} 数据预处理完成:`, {
      originalDataType: typeof restData,
      processedDataLength: processedData.length,
      sampleData: processedData.slice(0, 2)
    })

    return {
      originalData: restData,
      processedData: processedData,
      success: true
    }
  } catch (error) {
    console.error(`${componentType} REST数据获取失败:`, error)
    throw error
  }
}

export const transformUrlParamObj = (params: any) => {
  if (_.isEmpty(params)) {
    return ''
  }

  let str = '?'
  const len = Object.keys(params).length
  Object.keys(params).forEach((key: string, index: number) => {
    const value = params[key]
    if (index !== len - 1) {
      str += `${key}=${encodeURIComponent(value) + '&'}`
    } else {
      str += `${key}=${encodeURIComponent(value)}`
    }
  })
  return str
}
