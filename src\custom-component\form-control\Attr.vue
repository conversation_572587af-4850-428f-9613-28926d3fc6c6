<script setup lang="ts">
import { ref, computed } from 'vue'
// import { ElCollapse, ElCollapseItem, ElForm, ElFormItem, ElInput, ElInputNumber, ElSelect, ElOption, ElSwitch, ElButton, ElIcon, ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus-secondary'
import CommonAttr from '@/custom-component/common/CommonAttr.vue'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import { storeToRefs } from 'pinia'
import { snapshotStoreWithOut } from '@/store/modules/data-visualization/snapshot'
import type { FormConfig, FormField, SubmitConfig, LayoutConfig } from './types'
import { DEFAULT_FORM_CONFIG, FIELD_TYPE_OPTIONS } from './types'
import { generateID } from '@/utils/generateID'
import FieldConfig from './FieldConfig.vue'

const props = withDefaults(
  defineProps<{
    themes?: EditorTheme
  }>(),
  {
    themes: 'dark'
  }
)

const dvMainStore = dvMainStoreWithOut()
const { curComponent, componentData } = storeToRefs(dvMainStore)
const snapshotStore = snapshotStoreWithOut()

// 活动的折叠面板
const activeNames = ref(['basic', 'fields', 'submit', 'layout'])

// 字段配置对话框状态
const showFieldDialog = ref(false)
const editingField = ref<FormField | undefined>()
const editingFieldIndex = ref(-1)

// 计算属性
const formConfig = computed({
  get: (): FormConfig => {
    return curComponent.value?.propValue || { ...DEFAULT_FORM_CONFIG }
  },
  set: (value: FormConfig) => {
    if (curComponent.value) {
      curComponent.value.propValue = value
      snapshotStore.recordSnapshotCache('propValue')
    }
  }
})

// 字段管理
const addField = () => {
  editingField.value = undefined
  editingFieldIndex.value = -1
  showFieldDialog.value = true
}

const editField = (field: FormField, index: number) => {
  editingField.value = field
  editingFieldIndex.value = index
  showFieldDialog.value = true
}

// 字段配置对话框确认
const handleFieldConfirm = (field: FormField) => {
  if (editingFieldIndex.value >= 0) {
    // 编辑模式
    formConfig.value.fields[editingFieldIndex.value] = field
  } else {
    // 新增模式
    formConfig.value.fields.push(field)
  }

  snapshotStore.recordSnapshotCache('propValue')
  showFieldDialog.value = false
}

// 字段配置对话框取消
const handleFieldCancel = () => {
  showFieldDialog.value = false
  editingField.value = undefined
  editingFieldIndex.value = -1
}

const removeField = (index: number) => {
  if (formConfig.value.fields.length <= 1) {
    ElMessage.warning('至少需要保留一个字段')
    return
  }
  
  formConfig.value.fields.splice(index, 1)
  snapshotStore.recordSnapshotCache('propValue')
}

const moveFieldUp = (index: number) => {
  if (index > 0) {
    const fields = formConfig.value.fields
    ;[fields[index - 1], fields[index]] = [fields[index], fields[index - 1]]
    snapshotStore.recordSnapshotCache('propValue')
  }
}

const moveFieldDown = (index: number) => {
  if (index < formConfig.value.fields.length - 1) {
    const fields = formConfig.value.fields
    ;[fields[index], fields[index + 1]] = [fields[index + 1], fields[index]]
    snapshotStore.recordSnapshotCache('propValue')
  }
}



// 添加提交头部
const addSubmitHeader = () => {
  formConfig.value.submitConfig.headers.push({
    key: '',
    value: ''
  })
  snapshotStore.recordSnapshotCache('propValue')
}

// 删除提交头部
const removeSubmitHeader = (index: number) => {
  formConfig.value.submitConfig.headers.splice(index, 1)
  snapshotStore.recordSnapshotCache('propValue')
}

// 添加提交参数
const addSubmitParam = () => {
  formConfig.value.submitConfig.params.push({
    key: '',
    value: ''
  })
  snapshotStore.recordSnapshotCache('propValue')
}

// 删除提交参数
const removeSubmitParam = (index: number) => {
  formConfig.value.submitConfig.params.splice(index, 1)
  snapshotStore.recordSnapshotCache('propValue')
}

// 获取可用的表格组件列表
const availableTableComponents = computed(() => {
  return componentData.value
    .filter((comp: any) =>
      comp.component === 'UserView' &&
      ['table-normal', 'table-info', 'table-pivot'].includes(comp.innerType)
    )
    .map((comp: any) => ({
      id: comp.id,
      label: comp.label || `表格组件 ${comp.id.slice(-6)}`
    }))
})

// 处理变化
const handleChange = () => {
  snapshotStore.recordSnapshotCache('propValue')
}
</script>

<template>
  <div class="attr-list de-collapse-style">
    <el-collapse v-model="activeNames" class="form-attr-collapse">
      <!-- 基础配置 -->
      <el-collapse-item title="基础配置" name="basic">
        <el-form label-position="top" size="small">
          <el-form-item label="表单标题">
            <el-input
              v-model="formConfig.title"
              placeholder="请输入表单标题"
              @input="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="表单描述">
            <el-input
              v-model="formConfig.description"
              type="textarea"
              :rows="3"
              placeholder="请输入表单描述"
              @input="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="显示提交按钮">
            <el-switch
              v-model="formConfig.showSubmitButton"
              @change="handleChange"
            />
          </el-form-item>
          
          <el-form-item v-if="formConfig.showSubmitButton" label="提交按钮文本">
            <el-input
              v-model="formConfig.submitButtonText"
              placeholder="提交"
              @input="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="显示重置按钮">
            <el-switch
              v-model="formConfig.showResetButton"
              @change="handleChange"
            />
          </el-form-item>
          
          <el-form-item v-if="formConfig.showResetButton" label="重置按钮文本">
            <el-input
              v-model="formConfig.resetButtonText"
              placeholder="重置"
              @input="handleChange"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>
      
      <!-- 字段配置 -->
      <el-collapse-item title="字段配置" name="fields">
        <div class="field-list">
          <div
            v-for="(field, fieldIndex) in formConfig.fields"
            :key="field.id"
            class="field-item"
          >
            <div class="field-header">
              <div class="field-info">
                <span class="field-title">{{ field.label || '未命名字段' }}</span>
                <span class="field-type">{{ FIELD_TYPE_OPTIONS.find(opt => opt.value === field.type)?.label || field.type }}</span>
              </div>
              <div class="field-actions">
                <el-button
                  size="small"
                  text
                  type="primary"
                  @click="editField(field, fieldIndex)"
                >
                  编辑
                </el-button>
                <el-button
                  size="small"
                  text
                  @click="moveFieldUp(fieldIndex)"
                  :disabled="fieldIndex === 0"
                >
                  ↑
                </el-button>
                <el-button
                  size="small"
                  text
                  @click="moveFieldDown(fieldIndex)"
                  :disabled="fieldIndex === formConfig.fields.length - 1"
                >
                  ↓
                </el-button>
                <el-button
                  size="small"
                  text
                  type="danger"
                  @click="removeField(fieldIndex)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>

            <div class="field-summary">
              <div class="field-property">
                <span class="property-label">名称:</span>
                <span class="property-value">{{ field.name }}</span>
              </div>
              <div class="field-property">
                <span class="property-label">类型:</span>
                <span class="property-value">{{ FIELD_TYPE_OPTIONS.find(opt => opt.value === field.type)?.label || field.type }}</span>
              </div>
              <div class="field-property">
                <span class="property-label">必填:</span>
                <span class="property-value">{{ field.required ? '是' : '否' }}</span>
              </div>
              <div class="field-property" v-if="field.options && field.options.length > 0">
                <span class="property-label">选项数:</span>
                <span class="property-value">{{ field.options.length }}</span>
              </div>
            </div>
          </div>

          <el-button
            type="primary"
            size="small"
            style="width: 100%; margin-top: 12px"
            @click="addField"
          >
            <el-icon><Plus /></el-icon>
            添加字段
          </el-button>
        </div>
      </el-collapse-item>

      <!-- 字段配置对话框 -->
      <FieldConfig
        v-model:visible="showFieldDialog"
        :field="editingField"
        :existing-fields="formConfig.fields"
        :is-edit="editingFieldIndex >= 0"
        @confirm="handleFieldConfirm"
        @cancel="handleFieldCancel"
      />
      
      <!-- 提交配置 -->
      <el-collapse-item title="提交配置" name="submit">
        <el-form label-position="top" size="small">
          <el-form-item label="启用提交">
            <el-switch
              v-model="formConfig.submitConfig.enabled"
              @change="handleChange"
            />
          </el-form-item>
          
          <template v-if="formConfig.submitConfig.enabled">
            <el-form-item label="提交URL">
              <el-input
                v-model="formConfig.submitConfig.url"
                placeholder="请输入提交URL"
                @input="handleChange"
              />
            </el-form-item>
            
            <el-form-item label="请求方法">
              <el-select
                v-model="formConfig.submitConfig.method"
                style="width: 100%"
                @change="handleChange"
              >
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="DELETE" value="DELETE" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="超时时间(ms)">
              <el-input-number
                v-model="formConfig.submitConfig.timeout"
                :min="1000"
                :max="300000"
                style="width: 100%"
                @change="handleChange"
              />
            </el-form-item>
            
            <el-form-item label="成功消息">
              <el-input
                v-model="formConfig.submitConfig.successMessage"
                placeholder="提交成功"
                @input="handleChange"
              />
            </el-form-item>
            
            <el-form-item label="失败消息">
              <el-input
                v-model="formConfig.submitConfig.errorMessage"
                placeholder="提交失败"
                @input="handleChange"
              />
            </el-form-item>

            <el-form-item label="提交后刷新组件">
              <el-select
                v-model="formConfig.submitConfig.refreshTargetComponents"
                multiple
                placeholder="请选择提交成功后要刷新的组件"
                style="width: 100%"
                @change="handleChange"
              >
                <el-option
                  v-for="component in availableTableComponents"
                  :key="component.id"
                  :label="component.label"
                  :value="component.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="提交后重置">
              <el-switch
                v-model="formConfig.submitConfig.resetAfterSubmit"
                @change="handleChange"
              />
            </el-form-item>
            
            <el-form-item label="跳转URL">
              <el-input
                v-model="formConfig.submitConfig.redirectUrl"
                placeholder="提交成功后跳转的URL"
                @input="handleChange"
              />
            </el-form-item>

            <!-- 请求头配置 -->
            <el-form-item label="请求头">
              <div class="header-config">
                <div class="config-header">
                  <span>HTTP Headers</span>
                  <el-button
                    size="small"
                    type="primary"
                    text
                    @click="addSubmitHeader"
                  >
                    <el-icon><Plus /></el-icon>
                    添加
                  </el-button>
                </div>

                <div
                  v-for="(header, index) in formConfig.submitConfig.headers"
                  :key="index"
                  class="config-item"
                >
                  <el-input
                    v-model="header.key"
                    placeholder="Header名称"
                    size="small"
                    @input="handleChange"
                  />
                  <el-input
                    v-model="header.value"
                    placeholder="Header值"
                    size="small"
                    @input="handleChange"
                  />
                  <el-button
                    size="small"
                    type="danger"
                    text
                    @click="removeSubmitHeader(index)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </el-form-item>

            <!-- 请求参数配置 -->
            <el-form-item label="请求参数">
              <div class="param-config">
                <div class="config-header">
                  <span>URL Parameters</span>
                  <el-button
                    size="small"
                    type="primary"
                    text
                    @click="addSubmitParam"
                  >
                    <el-icon><Plus /></el-icon>
                    添加
                  </el-button>
                </div>

                <div
                  v-for="(param, index) in formConfig.submitConfig.params"
                  :key="index"
                  class="config-item"
                >
                  <el-input
                    v-model="param.key"
                    placeholder="参数名称"
                    size="small"
                    @input="handleChange"
                  />
                  <el-input
                    v-model="param.value"
                    placeholder="参数值（支持全局变量）"
                    size="small"
                    @input="handleChange"
                  />
                  <el-button
                    size="small"
                    type="danger"
                    text
                    @click="removeSubmitParam(index)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </template>
        </el-form>
      </el-collapse-item>
      
      <!-- 布局配置 -->
      <el-collapse-item title="布局配置" name="layout">
        <el-form label-position="top" size="small">
          <el-form-item label="列数">
            <el-input-number
              v-model="formConfig.layout.columns"
              :min="1"
              :max="4"
              style="width: 100%"
              @change="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="标签位置">
            <el-select
              v-model="formConfig.layout.labelPosition"
              style="width: 100%"
              @change="handleChange"
            >
              <el-option label="顶部" value="top" />
              <el-option label="左侧" value="left" />
              <el-option label="右侧" value="right" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="标签宽度">
            <el-input
              v-model="formConfig.layout.labelWidth"
              placeholder="100px"
              @input="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="组件尺寸">
            <el-select
              v-model="formConfig.layout.size"
              style="width: 100%"
              @change="handleChange"
            >
              <el-option label="小" value="small" />
              <el-option label="默认" value="default" />
              <el-option label="大" value="large" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="显示边框">
            <el-switch
              v-model="formConfig.layout.showBorder"
              @change="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="间距(px)">
            <el-input-number
              v-model="formConfig.layout.spacing"
              :min="0"
              :max="50"
              style="width: 100%"
              @change="handleChange"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>
    </el-collapse>
    
    <!-- 通用属性 -->
    <CommonAttr
      :themes="themes"
      :element="curComponent"
      :background-color-picker-width="197"
      :background-border-select-width="197"
    />
  </div>
</template>

<style lang="less" scoped>
.form-attr-collapse {
  .field-list {
    .field-item {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 12px;
      margin-bottom: 12px;
      
      .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .field-info {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .field-title {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
          }

          .field-type {
            font-size: 12px;
            color: #909399;
          }
        }

        .field-actions {
          display: flex;
          gap: 4px;
        }
      }

      .field-summary {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        padding: 8px;
        background-color: #f5f7fa;
        border-radius: 4px;
        font-size: 12px;

        .field-property {
          display: flex;
          gap: 4px;

          .property-label {
            color: #606266;
            font-weight: 500;
          }

          .property-value {
            color: #303133;
          }
        }
      }
      
      .field-options {
        margin-top: 12px;
        
        .options-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          font-size: 12px;
          color: #606266;
        }
        
        .option-item {
          display: flex;
          gap: 8px;
          margin-bottom: 8px;
          align-items: center;
          
          .el-input {
            flex: 1;
          }
        }
      }
    }
  }

  .header-config,
  .param-config {
    width: 100%;

    .config-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 12px;
      color: #606266;
    }

    .config-item {
      display: flex;
      gap: 8px;
      margin-bottom: 8px;
      align-items: center;

      .el-input {
        flex: 1;
      }
    }
  }
}
</style>
