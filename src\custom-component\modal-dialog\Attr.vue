<template>
  <div class="attr-list de-collapse-style">
    <el-collapse v-model="activeNames" class="modal-attr-collapse">
      <!-- 基础配置 -->
      <el-collapse-item title="基础配置" name="basic">
        <el-form label-position="top" size="small">
          <el-form-item label="弹框标题">
            <el-input
              v-model="modalConfig.title"
              placeholder="请输入弹框标题"
            />
          </el-form-item>

          <el-form-item label="弹框内容">
            <el-input
              v-model="modalConfig.content"
              type="textarea"
              :rows="3"
              placeholder="请输入弹框内容（当未选择关联组件时显示）"
            />
          </el-form-item>

          <el-form-item label="关联组件">
            <el-select
              v-model="modalConfig.linkedComponentId"
              placeholder="请选择要在弹框中显示的组件"
              clearable
            >
              <el-option
                v-for="component in availableComponents"
                :key="component.id"
                :label="component.label"
                :value="component.id"
              />
              <template #empty>
                <div style="padding: 10px; text-align: center; color: #999;">
                  暂无可选组件，请先在画布上添加其他组件
                </div>
              </template>
            </el-select>
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 尺寸配置 -->
      <el-collapse-item title="尺寸配置" name="size">
        <el-form label-position="top" size="small">
          <el-form-item label="弹框宽度(px)">
            <el-input-number
              v-model="modalConfig.width"
              :min="300"
              :max="1200"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="弹框高度(px)">
            <el-input-number
              v-model="modalConfig.height"
              :min="200"
              :max="800"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="弹框位置">
            <el-select
              v-model="modalConfig.position"
              style="width: 100%"
            >
              <el-option label="居中" value="center" />
              <el-option label="顶部" value="top" />
              <el-option label="底部" value="bottom" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 显示配置 -->
      <el-collapse-item title="显示配置" name="display">
        <el-form label-position="top" size="small">
          <el-form-item label="显示头部">
            <el-switch
              v-model="modalConfig.showHeader"
            />
          </el-form-item>

          <el-form-item label="显示底部按钮">
            <el-switch
              v-model="modalConfig.showFooter"
            />
          </el-form-item>

          <el-form-item label="确定按钮文本" v-if="modalConfig.showFooter">
            <el-input
              v-model="modalConfig.confirmButtonText"
              placeholder="确定"
            />
          </el-form-item>

          <el-form-item label="取消按钮文本" v-if="modalConfig.showFooter">
            <el-input
              v-model="modalConfig.cancelButtonText"
              placeholder="取消"
            />
          </el-form-item>

          <el-form-item label="显示关闭按钮">
            <el-switch
              v-model="modalConfig.showCloseButton"
            />
          </el-form-item>

          <el-form-item label="点击遮罩关闭">
            <el-switch
              v-model="modalConfig.closeOnClickOverlay"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>
    </el-collapse>

    <!-- 通用属性 -->
    <CommonAttr
      :themes="themes"
      :element="curComponent"
      :background-color-picker-width="197"
      :background-border-select-width="197"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElInputNumber, ElCollapse, ElCollapseItem, ElSwitch } from 'element-plus-secondary'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import { snapshotStoreWithOut } from '@/store/modules/data-visualization/snapshot'
import { storeToRefs } from 'pinia'
import CommonAttr from '@/custom-component/common/CommonAttr.vue'

const props = defineProps({
  themes: {
    type: String,
    default: 'dark'
  }
})

const dvMainStore = dvMainStoreWithOut()
const snapshotStore = snapshotStoreWithOut()
const { curComponent, componentData } = storeToRefs(dvMainStore)

// 处理配置变更
const handleChange = () => {
  snapshotStore.recordSnapshotCache('propValue')
}

const activeNames = ref(['basic', 'size', 'display'])

// 弹框配置
const defaultConfig = {
  title: '弹框标题',
  content: '',
  width: 600,
  height: 400,
  showHeader: true,
  showFooter: true,
  showCloseButton: true,
  closeOnClickOverlay: true,
  linkedComponentId: '',
  position: 'center',
  confirmButtonText: '确定',
  cancelButtonText: '取消'
}

// 初始化配置
const initConfig = () => {
  if (curComponent.value && curComponent.value.propValue) {
    return { ...defaultConfig, ...curComponent.value.propValue }
  }
  return { ...defaultConfig }
}

const modalConfig = ref(initConfig())

// 监听当前组件变化，同步配置
watch(
  () => curComponent.value,
  (newComponent) => {
    if (newComponent && newComponent.propValue) {
      modalConfig.value = { ...defaultConfig, ...newComponent.propValue }
    } else if (newComponent) {
      modalConfig.value = { ...defaultConfig }
      newComponent.propValue = modalConfig.value
    }
  },
  { immediate: true }
)

// 监听配置变化，同步到组件
watch(
  modalConfig,
  (newConfig) => {
    if (curComponent.value) {
      curComponent.value.propValue = { ...newConfig }
      handleChange()
    }
  },
  { deep: true }
)

// 获取可用的组件列表（排除当前弹框组件本身）
const availableComponents = computed(() => {
  if (!componentData.value || !Array.isArray(componentData.value)) {
    return []
  }

  return componentData.value
    .filter((comp: any) => {
      // 排除自己
      if (comp.id === curComponent.value?.id) return false
      // 排除其他弹框组件，避免嵌套
      if (comp.component === 'ModalDialog') return false
      // 只排除真正隐藏的组件（category为hidden的组件）
      // 允许表单控件（category为'form'）在弹框中显示
      if (comp.category === 'hidden') return false
      return true
    })
    .map((comp: any) => ({
      id: comp.id,
      label: comp.label || `${getComponentTypeName(comp)} ${comp.id.slice(-6)}`
    }))
})

// 获取组件类型名称
const getComponentTypeName = (component: any) => {
  if (component.component === 'FormControl') return '表单控件'
  if (component.component === 'FormQuery') return '表单查询'
  if (component.component === 'UserView') {
    switch (component.innerType) {
      case 'table-normal': return '普通表格'
      case 'table-info': return '信息表格'
      case 'table-pivot': return '透视表格'
      case 'button': return '按钮'
      default: return '图表组件'
    }
  }
  return component.component || '组件'
}


</script>

<style lang="less" scoped>
.attr-list {
  padding: 8px;
}

.modal-attr-collapse {
  border: none;
  
  :deep(.el-collapse-item__header) {
    background-color: transparent;
    border: none;
    padding: 0 8px;
    font-weight: bold;
  }
  
  :deep(.el-collapse-item__content) {
    padding: 8px;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
  
  :deep(.el-form-item__label) {
    font-size: 12px;
    color: #606266;
  }
}
</style>
