<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus-secondary'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import type { GlobalVariable } from '@/types/globalVariable'
import { callRestApi } from '@/utils/restApi'

const dvMainStore = dvMainStoreWithOut()

// 测试状态
const testResults = ref<string[]>([])
const isLoading = ref(false)

// 计算属性
const globalVariables = computed(() => dvMainStore.globalVariables)
const globalVariableValues = computed(() => dvMainStore.globalVariableValues)

// 添加测试结果
const addTestResult = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  const timestamp = new Date().toLocaleTimeString()
  const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'
  testResults.value.push(`[${timestamp}] ${prefix} ${message}`)
}

// 清空测试结果
const clearTestResults = () => {
  testResults.value = []
}

// 模拟设置路由参数
const simulateRouteParams = () => {
  // 在当前URL中添加token参数
  const currentUrl = new URL(window.location.href)
  currentUrl.searchParams.set('token', 'test-token-12345')
  currentUrl.searchParams.set('userId', '999')
  
  // 更新浏览器URL（不刷新页面）
  window.history.replaceState({}, '', currentUrl.toString())
  
  addTestResult('已设置路由参数: token=test-token-12345, userId=999', 'success')
}

// 创建测试全局变量
const createTestGlobalVariables = () => {
  addTestResult('开始创建测试全局变量')
  
  try {
    // 创建路由类型的token变量
    const tokenVariable: GlobalVariable = {
      id: 'test-token-var',
      key: 'token',
      type: 'route',
      routeParam: 'token',
      description: '从路由获取的token',
      enabled: true,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
    
    // 创建路由类型的userId变量
    const userIdVariable: GlobalVariable = {
      id: 'test-userid-var',
      key: 'userId',
      type: 'route',
      routeParam: 'userId',
      description: '从路由获取的用户ID',
      enabled: true,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
    
    // 创建静态变量
    const apiBaseVariable: GlobalVariable = {
      id: 'test-apibase-var',
      key: 'apiBase',
      type: 'static',
      value: 'https://jsonplaceholder.typicode.com',
      description: 'API基础URL',
      enabled: true,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
    
    dvMainStore.setGlobalVariables([tokenVariable, userIdVariable, apiBaseVariable])
    
    addTestResult('成功创建3个测试全局变量', 'success')
    addTestResult(`变量列表: ${globalVariables.value.map(v => v.key).join(', ')}`)
  } catch (error) {
    addTestResult(`创建变量失败: ${error.message}`, 'error')
  }
}

// 测试变量解析时机
const testVariableResolutionTiming = () => {
  addTestResult('开始测试：变量解析时机')
  
  try {
    // 第一次解析
    addTestResult('第一次解析全局变量...')
    dvMainStore.resolveGlobalVariables()
    addTestResult('第一次解析结果:')
    Object.entries(globalVariableValues.value).forEach(([key, value]) => {
      addTestResult(`  ${key}: ${value || '未解析'}`)
    })
    
    // 等待一段时间后再次解析
    setTimeout(() => {
      addTestResult('延迟500ms后第二次解析全局变量...')
      dvMainStore.resolveGlobalVariables()
      addTestResult('第二次解析结果:')
      Object.entries(globalVariableValues.value).forEach(([key, value]) => {
        addTestResult(`  ${key}: ${value || '未解析'}`)
      })
    }, 500)
    
  } catch (error) {
    addTestResult(`变量解析失败: ${error.message}`, 'error')
  }
}

// 测试REST API调用时机
const testRestApiTiming = async () => {
  addTestResult('开始测试：REST API调用时机')
  isLoading.value = true
  
  try {
    const testConfig = {
      url: '${apiBase}/posts/${userId}',
      method: 'GET' as const,
      headers: [
        { key: 'Authorization', value: 'Bearer ${token}' },
        { key: 'Content-Type', value: 'application/json' }
      ],
      params: [
        { key: 'userId', value: '${userId}' }
      ],
      body: '',
      timeout: 10000
    }
    
    addTestResult('原始REST配置:')
    addTestResult(`  URL: ${testConfig.url}`)
    addTestResult(`  Headers: ${JSON.stringify(testConfig.headers)}`)
    addTestResult(`  Params: ${JSON.stringify(testConfig.params)}`)
    
    // 显示当前全局变量状态
    addTestResult('当前全局变量值:')
    Object.entries(globalVariableValues.value).forEach(([key, value]) => {
      addTestResult(`  ${key}: ${value || '未解析'}`)
    })
    
    // 立即调用API
    addTestResult('立即调用REST API...')
    try {
      const result1 = await callRestApi(testConfig, {
        logPrefix: '立即调用',
        enableMockData: true
      })
      addTestResult('立即调用成功', 'success')
    } catch (error) {
      addTestResult(`立即调用失败: ${error.message}`, 'error')
    }
    
    // 延迟调用API
    setTimeout(async () => {
      addTestResult('延迟1秒后调用REST API...')
      try {
        const result2 = await callRestApi(testConfig, {
          logPrefix: '延迟调用',
          enableMockData: true
        })
        addTestResult('延迟调用成功', 'success')
      } catch (error) {
        addTestResult(`延迟调用失败: ${error.message}`, 'error')
      }
    }, 1000)
    
  } catch (error) {
    addTestResult(`REST API测试失败: ${error.message}`, 'error')
  } finally {
    isLoading.value = false
  }
}

// 模拟dashboard初始化流程
const simulateDashboardInit = async () => {
  addTestResult('开始模拟：dashboard初始化流程')
  
  try {
    // 步骤1：清空当前状态
    addTestResult('步骤1：清空当前状态')
    dvMainStore.setGlobalVariables([])
    
    // 步骤2：模拟加载仪表板配置
    addTestResult('步骤2：模拟加载仪表板配置')
    const mockCanvasInfo = {
      globalVariables: JSON.stringify([
        {
          id: 'dashboard-token',
          key: 'token',
          type: 'route',
          routeParam: 'token',
          description: '仪表板token变量',
          enabled: true
        },
        {
          id: 'dashboard-api',
          key: 'apiUrl',
          type: 'static',
          value: 'https://api.example.com',
          description: '仪表板API地址',
          enabled: true
        }
      ])
    }
    
    // 步骤3：恢复全局变量（模拟initCanvasDataPrepare中的逻辑）
    addTestResult('步骤3：恢复全局变量配置')
    const globalVariables = JSON.parse(mockCanvasInfo.globalVariables) as GlobalVariable[]
    dvMainStore.setGlobalVariables(globalVariables)
    
    // 步骤4：解析全局变量
    addTestResult('步骤4：解析全局变量')
    dvMainStore.resolveGlobalVariables()
    
    addTestResult('解析结果:')
    Object.entries(globalVariableValues.value).forEach(([key, value]) => {
      addTestResult(`  ${key}: ${value || '未解析'}`)
    })
    
    // 步骤5：模拟组件初始化（延迟执行）
    addTestResult('步骤5：延迟100ms后模拟组件初始化')
    setTimeout(() => {
      addTestResult('组件初始化：重新解析全局变量')
      dvMainStore.resolveGlobalVariables()
      
      addTestResult('组件初始化后的解析结果:')
      Object.entries(globalVariableValues.value).forEach(([key, value]) => {
        addTestResult(`  ${key}: ${value || '未解析'}`)
      })
      
      // 模拟REST调用
      const mockRestConfig = {
        url: '${apiUrl}/data?token=${token}',
        method: 'GET' as const,
        headers: [{ key: 'Authorization', value: 'Bearer ${token}' }],
        params: [],
        body: '',
        timeout: 10000
      }
      
      addTestResult('模拟REST调用配置:')
      addTestResult(`  原始URL: ${mockRestConfig.url}`)
      addTestResult(`  解析后URL: ${dvMainStore.resolveVariableReferences(mockRestConfig.url)}`)
      
    }, 100)
    
    addTestResult('dashboard初始化流程模拟完成', 'success')
    
  } catch (error) {
    addTestResult(`dashboard初始化模拟失败: ${error.message}`, 'error')
  }
}

// 运行完整测试
const runCompleteTest = async () => {
  clearTestResults()
  addTestResult('=== 开始全局变量时机测试 ===')
  
  // 设置路由参数
  simulateRouteParams()
  await new Promise(resolve => setTimeout(resolve, 200))
  
  // 创建测试变量
  createTestGlobalVariables()
  await new Promise(resolve => setTimeout(resolve, 200))
  
  // 测试解析时机
  testVariableResolutionTiming()
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 测试API调用时机
  await testRestApiTiming()
  await new Promise(resolve => setTimeout(resolve, 1500))
  
  // 模拟dashboard初始化
  await simulateDashboardInit()
  
  addTestResult('=== 测试完成 ===')
}

// 组件挂载时初始化
onMounted(() => {
  addTestResult('全局变量时机测试页面已加载')
  addTestResult('此页面用于测试全局变量解析和REST调用的时机问题')
})
</script>

<template>
  <div class="global-variable-timing-test">
    <div class="test-header">
      <h2>全局变量时机测试</h2>
      <p>此页面用于测试dashboard初始化时全局变量解析和REST接口调用的时机问题</p>
    </div>

    <div class="test-controls">
      <el-button type="primary" @click="runCompleteTest" :loading="isLoading">
        运行完整测试
      </el-button>
      <el-button @click="simulateRouteParams">
        设置路由参数
      </el-button>
      <el-button @click="createTestGlobalVariables">
        创建测试变量
      </el-button>
      <el-button @click="testVariableResolutionTiming">
        测试解析时机
      </el-button>
      <el-button @click="testRestApiTiming" :loading="isLoading">
        测试API时机
      </el-button>
      <el-button @click="simulateDashboardInit">
        模拟初始化
      </el-button>
      <el-button @click="clearTestResults">
        清空结果
      </el-button>
    </div>

    <div class="test-content">
      <!-- 当前状态 -->
      <div class="current-status">
        <h3>当前状态</h3>
        <div class="status-info">
          <div class="status-item">
            <span class="label">全局变量数量:</span>
            <span class="value">{{ globalVariables.length }}</span>
          </div>
          <div class="status-item">
            <span class="label">已解析变量数量:</span>
            <span class="value">{{ Object.keys(globalVariableValues).length }}</span>
          </div>
          <div class="status-item">
            <span class="label">当前URL参数:</span>
            <span class="value">{{ window.location.search || '无' }}</span>
          </div>
        </div>
        
        <div v-if="globalVariables.length > 0" class="variables-list">
          <h4>全局变量列表:</h4>
          <div v-for="variable in globalVariables" :key="variable.id" class="variable-item">
            <span class="var-key">{{ variable.key }}</span>
            <span class="var-type">({{ variable.type }})</span>
            <span class="var-value">= {{ globalVariableValues[variable.key] || '未解析' }}</span>
          </div>
        </div>
      </div>

      <!-- 测试结果 -->
      <div class="test-results">
        <h3>测试结果</h3>
        <div class="results-container">
          <div v-if="testResults.length === 0" class="no-results">
            暂无测试结果
          </div>
          <div v-else class="results-list">
            <div
              v-for="(result, index) in testResults"
              :key="index"
              class="result-item"
            >
              {{ result }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.global-variable-timing-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;

  .test-header {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 8px;

    h2 {
      margin: 0 0 8px 0;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .test-controls {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .el-button {
      margin-right: 8px;
      margin-bottom: 8px;
    }
  }

  .test-content {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 20px;

    .current-status,
    .test-results {
      background-color: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 16px;

      h3 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 16px;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 8px;
      }
    }

    .current-status {
      .status-info {
        margin-bottom: 16px;

        .status-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .label {
            font-weight: 500;
            color: #606266;
          }

          .value {
            color: #303133;
            word-break: break-all;
          }
        }
      }

      .variables-list {
        h4 {
          margin: 0 0 8px 0;
          color: #303133;
          font-size: 14px;
        }

        .variable-item {
          display: flex;
          align-items: center;
          padding: 4px 0;
          font-size: 12px;

          .var-key {
            font-weight: 500;
            color: #409eff;
            margin-right: 8px;
          }

          .var-type {
            color: #909399;
            margin-right: 8px;
          }

          .var-value {
            color: #67c23a;
            flex: 1;
            word-break: break-all;
          }
        }
      }
    }

    .test-results {
      .results-container {
        max-height: 500px;
        overflow-y: auto;

        .no-results {
          text-align: center;
          color: #909399;
          padding: 40px 20px;
        }

        .results-list {
          .result-item {
            padding: 4px 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            border-bottom: 1px solid #f0f0f0;
            white-space: pre-wrap;
            word-break: break-all;

            &:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .global-variable-timing-test {
    .test-content {
      grid-template-columns: 1fr;
    }
  }
}
</style>
