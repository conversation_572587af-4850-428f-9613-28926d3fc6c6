/**
 * 表单控件相关类型定义
 */

export type FormFieldType = 'text' | 'textarea' | 'number' | 'select' | 'radio' | 'checkbox' | 'date' | 'email' | 'password'

export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
}

export interface FieldValidation {
  required?: boolean
  min?: number
  max?: number
  pattern?: string
  message?: string
}

export interface FieldStyle {
  width?: string
  labelWidth?: string
  labelPosition?: 'top' | 'left' | 'right'
  size?: 'small' | 'default' | 'large'
}

export interface FormField {
  id: string
  type: FormFieldType
  label: string
  name: string
  placeholder?: string
  defaultValue?: any
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  options?: SelectOption[]  // 用于select、radio、checkbox类型
  validation?: FieldValidation
  style?: FieldStyle
  visible?: boolean
  order?: number
}

export interface SubmitConfig {
  enabled: boolean
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers: Array<{ key: string; value: string }>
  params: Array<{ key: string; value: string }>
  body?: string
  timeout?: number
  successMessage?: string
  errorMessage?: string
  resetAfterSubmit?: boolean
  redirectUrl?: string
  refreshTargetComponents?: string[]  // 提交成功后要刷新的目标组件ID列表
}

export interface LayoutConfig {
  columns: number  // 表单列数
  labelPosition: 'top' | 'left' | 'right'
  labelWidth: string
  size: 'small' | 'default' | 'large'
  showBorder: boolean
  spacing: number
}

export interface FormConfig {
  title?: string
  description?: string
  fields: FormField[]
  submitConfig: SubmitConfig
  layout: LayoutConfig
  showSubmitButton: boolean
  submitButtonText: string
  showResetButton: boolean
  resetButtonText: string
}

export interface FormData {
  [fieldName: string]: any
}

export interface FormValidationResult {
  valid: boolean
  errors: Record<string, string>
}

export interface FormSubmitResult {
  success: boolean
  message?: string
  data?: any
  error?: any
}

// 默认配置
export const DEFAULT_FORM_CONFIG: FormConfig = {
  title: '表单',
  description: '',
  fields: [
    {
      id: 'field_1',
      type: 'text',
      label: '姓名',
      name: 'name',
      placeholder: '请输入姓名',
      required: true,
      visible: true,
      order: 1,
      validation: {}
    }
  ],
  submitConfig: {
    enabled: true,
    url: '',
    method: 'POST',
    headers: [
      { key: 'Content-Type', value: 'application/json' }
    ],
    params: [],
    timeout: 30000,
    successMessage: '提交成功',
    errorMessage: '提交失败',
    resetAfterSubmit: false
  },
  layout: {
    columns: 1,
    labelPosition: 'top',
    labelWidth: '100px',
    size: 'default',
    showBorder: true,
    spacing: 16
  },
  showSubmitButton: true,
  submitButtonText: '提交',
  showResetButton: true,
  resetButtonText: '重置'
}

// 字段类型配置
export const FIELD_TYPE_OPTIONS = [
  { label: '单行文本', value: 'text' },
  { label: '多行文本', value: 'textarea' },
  { label: '数字', value: 'number' },
  { label: '下拉选择', value: 'select' },
  { label: '单选按钮', value: 'radio' },
  { label: '复选框', value: 'checkbox' },
  { label: '日期', value: 'date' },
  { label: '邮箱', value: 'email' },
  { label: '密码', value: 'password' }
]

// 验证规则
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^1[3-9]\d{9}$/,
  idCard: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  url: /^https?:\/\/.+/
}
