<template>
  <div class="list-style-demo">
    <h2>列表样式演示</h2>
    
    <div class="style-selector">
      <el-radio-group v-model="currentStyle" @change="changeStyle">
        <el-radio-button label="notice">通知公告</el-radio-button>
        <el-radio-button label="timeline">时间轴</el-radio-button>
        <el-radio-button label="task">任务列表</el-radio-button>
        <el-radio-button label="warning">预警列表</el-radio-button>
      </el-radio-group>
    </div>
    
    <div class="demo-info">
      <p><strong>当前样式:</strong> {{ getStyleName(currentStyle) }}</p>
      <p><strong>特点:</strong> {{ getStyleDescription(currentStyle) }}</p>
    </div>

    <div class="list-container-wrapper">
      <ListContainer
        :prop-value="currentConfig"
        :is-edit="false"
        show-position="preview"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ListContainer from './Component.vue'
import { defaultConfigs } from './test-data.js'

const currentStyle = ref('notice')
const currentConfig = ref(defaultConfigs.notice)

const changeStyle = (style) => {
  currentConfig.value = defaultConfigs[style]
}

const getStyleName = (style) => {
  const names = {
    notice: '通知公告',
    timeline: '时间轴',
    task: '任务列表',
    warning: '预警列表'
  }
  return names[style] || style
}

const getStyleDescription = (style) => {
  const descriptions = {
    notice: '左侧图片 + 中间内容 + 右侧操作按钮',
    timeline: '左侧时间线 + 右侧内容卡片',
    task: '状态指示器 + 任务信息 + 优先级标签',
    warning: '预警图片 + 预警信息 + 操作按钮'
  }
  return descriptions[style] || ''
}

onMounted(() => {
  // 初始化默认样式
  changeStyle('notice')
})
</script>

<style lang="less" scoped>
.list-style-demo {
  padding: 20px;
  
  h2 {
    margin-bottom: 20px;
  }
  
  .style-selector {
    margin-bottom: 20px;
  }

  .demo-info {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 20px;

    p {
      margin: 8px 0;
      color: #666;
    }
  }

  .list-container-wrapper {
    border: 1px dashed #ccc;
    padding: 20px;
    height: 500px;
    background-color: #fff;
    border-radius: 6px;
  }
}
</style>
