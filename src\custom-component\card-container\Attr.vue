<template>
  <div class="attr-list de-collapse-style">
    <!-- 隐藏的文件输入框 -->
    <input
      ref="files"
      type="file"
      accept=".jpeg,.jpg,.png,.gif,.svg"
      hidden
      @click="e => { e.target.value = '' }"
      @change="reUpload"
    />

    <el-collapse v-model="activeName" @change="onChange">
      <!-- 卡片配置 -->
      <el-collapse-item title="卡片配置" name="cardConfig">
        <el-form label-position="top" size="small">
          <el-form-item label="显示头部">
            <el-switch
              v-model="propValue.showHeader"
              @change="handleChange"
            />
          </el-form-item>
          
          <el-form-item v-if="propValue.showHeader" label="卡片标题">
            <el-input
              v-model="propValue.title"
              placeholder="请输入卡片标题"
              @input="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="占位符文本">
            <el-input
              v-model="propValue.placeholder"
              placeholder="请输入占位符文本"
              @input="handleChange"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 头部样式 -->
      <el-collapse-item v-if="propValue.showHeader" title="头部样式" name="headerStyle">
        <el-form label-position="top" size="small">
          <el-form-item label="背景颜色">
            <el-color-picker
              v-model="propValue.headerStyle.backgroundColor"
              @change="handleChange"
              show-alpha
            />
          </el-form-item>
          
          <el-form-item label="字体大小">
            <el-input-number
              v-model="fontSizeValue"
              :min="12"
              :max="32"
              @change="handleFontSizeChange"
            />
          </el-form-item>
          
          <el-form-item label="字体颜色">
            <el-color-picker
              v-model="propValue.headerStyle.color"
              @change="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="字体粗细">
            <el-select
              v-model="propValue.headerStyle.fontWeight"
              @change="handleChange"
            >
              <el-option label="正常" value="normal" />
              <el-option label="粗体" value="bold" />
              <el-option label="100" value="100" />
              <el-option label="200" value="200" />
              <el-option label="300" value="300" />
              <el-option label="400" value="400" />
              <el-option label="500" value="500" />
              <el-option label="600" value="600" />
              <el-option label="700" value="700" />
              <el-option label="800" value="800" />
              <el-option label="900" value="900" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="内边距">
            <el-input
              v-model="propValue.headerStyle.padding"
              placeholder="例如: 12px 16px"
              @input="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="底部边框">
            <el-input
              v-model="propValue.headerStyle.borderBottom"
              placeholder="例如: 1px solid #e8e8e8"
              @input="handleChange"
            />
          </el-form-item>

          <el-form-item class="form-item no-margin-bottom" :class="'form-item-' + themes">
            <el-checkbox
              size="small"
              :effect="themes"
              v-model="propValue.headerStyle.backgroundImageEnable"
              @change="handleChange"
            >
              背景图片
            </el-checkbox>
          </el-form-item>

          <div class="indented-container" v-if="propValue.headerStyle.backgroundImageEnable">
            <div class="indented-item">
              <div class="avatar-uploader-container" :class="`img-area_${themes}`">
                <el-upload
                  action=""
                  :effect="themes"
                  accept=".jpeg,.jpg,.png,.gif,.svg"
                  class="avatar-uploader"
                  list-type="picture-card"
                  :on-preview="handlePictureCardPreview"
                  :on-remove="handleRemove"
                  :before-upload="beforeUploadCheck"
                  :http-request="upload"
                  :file-list="fileList"
                  :disabled="!propValue.headerStyle.backgroundImageEnable"
                >
                  <el-icon><Plus /></el-icon>
                </el-upload>
                <el-row>
                  <span
                    style="margin-top: 2px"
                    v-if="!propValue.headerStyle.backgroundImage"
                    class="image-hint"
                    :class="`image-hint_${themes}`"
                  >
                    支持JPG、PNG、GIF、SVG
                  </span>

                  <el-button
                    size="small"
                    style="margin: 8px 0 0 -4px"
                    v-if="propValue.headerStyle.backgroundImage"
                    text
                    @click="goFile"
                    :disabled="!propValue.headerStyle.backgroundImageEnable"
                  >
                    重新上传
                  </el-button>
                </el-row>
              </div>

              <img-view-dialog v-model="dialogVisible" :image-url="dialogImageUrl" />
            </div>

            <div class="indented-item" v-if="propValue.headerStyle.backgroundImage">
              <el-form-item label="背景尺寸" class="form-item" :class="'form-item-' + themes">
                <el-select
                  v-model="propValue.headerStyle.backgroundSize"
                  style="width: 100%"
                  @change="handleChange"
                >
                  <el-option label="覆盖" value="cover" />
                  <el-option label="包含" value="contain" />
                  <el-option label="拉伸" value="100% 100%" />
                  <el-option label="原始大小" value="auto" />
                </el-select>
              </el-form-item>

              <el-form-item label="背景位置" class="form-item" :class="'form-item-' + themes">
                <el-select
                  v-model="propValue.headerStyle.backgroundPosition"
                  style="width: 100%"
                  @change="handleChange"
                >
                  <el-option label="居中" value="center" />
                  <el-option label="左上" value="left top" />
                  <el-option label="右上" value="right top" />
                  <el-option label="左下" value="left bottom" />
                  <el-option label="右下" value="right bottom" />
                  <el-option label="左侧" value="left center" />
                  <el-option label="右侧" value="right center" />
                  <el-option label="顶部" value="center top" />
                  <el-option label="底部" value="center bottom" />
                </el-select>
              </el-form-item>

              <el-form-item label="背景重复" class="form-item" :class="'form-item-' + themes">
                <el-select
                  v-model="propValue.headerStyle.backgroundRepeat"
                  style="width: 100%"
                  @change="handleChange"
                >
                  <el-option label="不重复" value="no-repeat" />
                  <el-option label="重复" value="repeat" />
                  <el-option label="水平重复" value="repeat-x" />
                  <el-option label="垂直重复" value="repeat-y" />
                </el-select>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </el-collapse-item>

      <!-- 内容样式 -->
      <el-collapse-item title="内容样式" name="bodyStyle">
        <el-form label-position="top" size="small">
          <el-form-item label="内边距">
            <el-input
              v-model="propValue.bodyStyle.padding"
              placeholder="例如: 16px"
              @input="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="最小高度">
            <el-input
              v-model="propValue.bodyStyle.minHeight"
              placeholder="例如: 100px"
              @input="handleChange"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 卡片样式 -->
      <el-collapse-item title="卡片样式" name="cardStyle">
        <el-form label-position="top" size="small">
          <el-form-item label="背景颜色">
            <el-color-picker
              v-model="propValue.cardStyle.backgroundColor"
              @change="handleChange"
              show-alpha
            />
          </el-form-item>
          
          <el-form-item label="边框">
            <el-input
              v-model="propValue.cardStyle.border"
              placeholder="例如: 1px solid #e8e8e8"
              @input="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="圆角">
            <el-input
              v-model="propValue.cardStyle.borderRadius"
              placeholder="例如: 6px"
              @input="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="阴影">
            <el-input
              v-model="propValue.cardStyle.boxShadow"
              placeholder="例如: 0 2px 4px rgba(0,0,0,0.1)"
              @input="handleChange"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>
    </el-collapse>
    
    <!-- 通用属性 -->
    <CommonAttr
      :themes="themes"
      :element="curComponent"
      :background-color-picker-width="197"
      :background-border-select-width="197"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, toRefs, computed, onMounted, watch } from 'vue'
import CommonAttr from '@/custom-component/common/CommonAttr.vue'
import { Plus } from '@element-plus/icons-vue'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import { snapshotStoreWithOut } from '@/store/modules/data-visualization/snapshot'
import { handleImageUpload } from '@/utils/imageBase64Utils'
import { ElMessage } from 'element-plus-secondary'
import { storeToRefs } from 'pinia'

const props = withDefaults(
  defineProps<{
    themes?: EditorTheme
  }>(),
  {
    themes: 'dark'
  }
)

const { themes } = toRefs(props)
const dvMainStore = dvMainStoreWithOut()
const snapshotStore = snapshotStoreWithOut()
const { curComponent } = storeToRefs(dvMainStore)

const activeName = ref(['cardConfig'])

// 使用计算属性来获取响应式的propValue
const propValue = computed(() => {
  return curComponent.value?.propValue || {}
})

// 图片上传相关状态
const fileList = ref([])
const dialogVisible = ref(false)
const dialogImageUrl = ref('')
const files = ref(null)

// 字体大小的计算属性，处理px单位
const fontSizeValue = computed({
  get() {
    const fontSize = propValue.value.headerStyle?.fontSize
    if (typeof fontSize === 'string' && fontSize.endsWith('px')) {
      return parseInt(fontSize)
    }
    return fontSize || 16
  },
  set(value) {
    if (propValue.value.headerStyle) {
      propValue.value.headerStyle.fontSize = value + 'px'
    }
  }
})

const handleChange = () => {
  // 触发组件更新
  snapshotStore.recordSnapshotCache('propValue')
}

const handleFontSizeChange = (value: number) => {
  if (propValue.value.headerStyle) {
    propValue.value.headerStyle.fontSize = value + 'px'
  }
  handleChange()
}

// 图片上传相关函数
const beforeUploadCheck = (file) => {
  const isImage = /\.(jpg|jpeg|png|gif|svg)$/i.test(file.name)
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传 JPG、PNG、GIF、SVG 格式的图片!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

const upload = async (file) => {
  try {
    const result = await handleImageUpload(file.file, {
      compress: true,
      quality: 0.8,
      maxWidth: 1920,
      maxHeight: 1080
    })

    if (result.success) {
      propValue.value.headerStyle.backgroundImage = result.data
      fileList.value = [{ url: result.data }]
      handleChange()
      ElMessage.success('背景图片上传成功')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('背景图片上传失败:', error)
    ElMessage.error('背景图片上传失败，请重试')
  }
}

const handlePictureCardPreview = (file) => {
  dialogImageUrl.value = file.url
  dialogVisible.value = true
}

const handleRemove = () => {
  propValue.value.headerStyle.backgroundImage = ''
  fileList.value = []
  handleChange()
}

const goFile = () => {
  files.value?.click()
}

const reUpload = async (e) => {
  const file = e.target.files[0]
  if (!file) return

  try {
    const result = await handleImageUpload(file, {
      compress: true,
      quality: 0.8,
      maxWidth: 1920,
      maxHeight: 1080
    })

    if (result.success) {
      propValue.value.headerStyle.backgroundImage = result.data
      fileList.value = [{ url: result.data }]
      handleChange()
      ElMessage.success('背景图片重新上传成功')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('背景图片重新上传失败:', error)
    ElMessage.error('背景图片重新上传失败，请重试')
  }
}

const onChange = () => {
  // 折叠面板状态变化
}

// 初始化文件列表
const init = () => {
  if (propValue.value.headerStyle?.backgroundImage) {
    fileList.value = [{ url: propValue.value.headerStyle.backgroundImage }]
  } else {
    fileList.value = []
  }
}

// 监听背景图片变化
watch(
  () => propValue.value.headerStyle?.backgroundImage,
  () => {
    init()
  }
)

onMounted(() => {
  init()
})
</script>

<style lang="less" scoped>
.attr-list {
  width: 100%;

  :deep(.el-collapse-item__header) {
    font-weight: bold;
  }

  :deep(.el-form-item) {
    margin-bottom: 12px;
  }

  :deep(.el-form-item__label) {
    padding-bottom: 4px;
    font-size: 12px;
  }

  :deep(.el-input__inner) {
    font-size: 12px;
  }

  :deep(.el-input-number) {
    width: 100%;
  }
}

// 背景图片相关样式，与全局背景配置保持一致
.indented-container {
  margin-left: 16px;
  border-left: 1px solid #e8e8e8;
  padding-left: 16px;
}

.indented-item {
  margin-bottom: 16px;

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

.avatar-uploader-container {
  width: 100%;

  &.img-area_dark {
    .image-hint {
      color: #a6a6a6;
    }
  }

  &.img-area_light {
    .image-hint {
      color: #666;
    }
  }
}

.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: 0.2s;

    &:hover {
      border-color: #409eff;
    }
  }

  :deep(.el-upload-list__item) {
    transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
  }
}

.image-hint {
  font-size: 12px;
  color: #666;

  &.image-hint_dark {
    color: #a6a6a6;
  }

  &.image-hint_light {
    color: #666;
  }
}

.form-item {
  &.no-margin-bottom {
    margin-bottom: 0;
  }

  &.margin-bottom-8 {
    margin-bottom: 8px;
  }
}
</style>
