# 全局变量功能使用说明

## 概述

全局变量功能允许在仪表板中定义可重用的变量，支持静态值和路由参数两种类型。这些变量可以在REST接口配置中引用，实现动态参数传递。

**重要说明**：全局变量配置作为仪表板配置的一部分进行保存和加载，每个仪表板都有独立的全局变量配置。

## 功能特性

### 1. 变量类型
- **静态值**: 固定的字符串值，如API密钥、固定参数等
- **路由参数**: 从当前页面URL中动态获取的参数值

### 2. 变量引用格式
在REST接口配置中，可以使用以下格式引用全局变量：
- `${变量名}` - 推荐格式
- `{{变量名}}` - 备用格式

### 3. 支持的配置项
- URL地址
- 请求头（Headers）
- 请求参数（Params）
- 请求体（Body）
- 数据路径（Data Path）

## 使用步骤

### 1. 创建全局变量

1. 在仪表板编辑页面，点击工具栏中的"全局变量管理"按钮
2. 在弹出的管理界面中，点击"添加变量"
3. 填写变量信息：
   - **变量名**: 只能包含字母、数字和下划线，不能以数字开头
   - **变量类型**: 选择"静态值"或"路由参数"
   - **变量值**: 
     - 静态值：直接输入固定值
     - 路由参数：输入要获取的URL参数名
   - **描述**: 可选，用于说明变量用途
   - **启用状态**: 控制变量是否生效

### 2. 在REST接口中使用变量

1. 在图表配置中选择"REST数据源"
2. 在URL、请求头或参数配置中，使用 `${变量名}` 格式引用变量
3. 点击输入框右侧的下拉按钮，可以快速选择已定义的全局变量
4. 系统会自动解析变量并在API调用时替换为实际值

### 3. 变量管理

- **编辑变量**: 在变量管理界面中点击编辑按钮
- **删除变量**: 点击删除按钮，确认后删除
- **启用/禁用**: 通过编辑功能切换变量状态
- **查看解析值**: 在管理界面中可以看到变量的当前解析值
- **保存配置**: 全局变量配置会随仪表板一起保存，需要保存仪表板才能持久化变量配置

## 使用示例

### 示例1：API密钥管理
```
变量名: apiKey
类型: 静态值
值: your-api-key-here
描述: API访问密钥

在REST配置中使用:
URL: https://api.example.com/data
Headers: Authorization = Bearer ${apiKey}
```

### 示例2：动态用户ID
```
变量名: userId
类型: 路由参数
路由参数: id
描述: 当前用户ID

在REST配置中使用:
URL: https://api.example.com/users/${userId}/profile
Params: user_id = ${userId}
```

### 示例3：环境配置
```
变量名: apiBaseUrl
类型: 静态值
值: https://api.production.com
描述: API基础地址

在REST配置中使用:
URL: ${apiBaseUrl}/v1/data
```

## 路由参数获取规则

系统会按以下优先级获取路由参数：
1. URL查询参数 (如: ?id=123)
2. Hash参数 (如: #/page?id=123)
3. 路径参数 (从路由路径中提取)

常用的路由参数名：
- `id` - 资源ID
- `token` - 访问令牌
- `userId` - 用户ID
- `orgId` - 组织ID
- `workspaceId` - 工作空间ID

## 注意事项

1. **变量名唯一性**: 同一仪表板中的变量名必须唯一
2. **循环引用**: 避免变量值中引用自身或形成循环引用
3. **安全性**: 敏感信息（如密钥）建议使用静态值类型，避免在URL中暴露
4. **性能**: 变量解析会在每次API调用时执行，建议合理使用
5. **调试**: 可以在浏览器控制台查看变量解析日志
6. **数据持久化**: 全局变量配置存储在仪表板配置中，必须保存仪表板才能持久化变量配置
7. **仪表板独立性**: 每个仪表板都有独立的全局变量配置，不会相互影响

## 故障排除

### 变量未解析
- 检查变量名是否正确
- 确认变量已启用
- 验证路由参数是否存在于当前URL中

### API调用失败
- 检查解析后的URL和参数是否正确
- 在浏览器网络面板中查看实际请求
- 查看控制台错误日志

### 变量值为空
- 路由参数类型：确认URL中包含对应参数
- 静态值类型：检查变量配置是否正确保存

## 技术实现

### 文件结构
```
src/
├── types/globalVariable.ts              # 类型定义
├── store/modules/data-visualization/
│   └── dvMain.ts                        # 状态管理
├── components/dashboard/
│   └── GlobalVariableManager.vue       # 管理界面
├── utils/restApi.ts                     # API调用工具
└── views/test/GlobalVariableTest.vue    # 测试页面
```

### 核心方法
- `addGlobalVariable()` - 添加变量
- `updateGlobalVariable()` - 更新变量
- `deleteGlobalVariable()` - 删除变量
- `resolveGlobalVariables()` - 解析所有变量
- `resolveVariableReferences()` - 解析字符串中的变量引用

## 扩展开发

如需扩展全局变量功能，可以：
1. 在 `globalVariable.ts` 中添加新的变量类型
2. 在 `dvMain.ts` 中实现对应的解析逻辑
3. 在管理界面中添加相应的配置选项
4. 更新REST API调用逻辑以支持新类型

---

更多技术细节请参考源代码注释和类型定义。
