// import { Layout } from './establish' // 暂时注释掉未使用的导入

// 静态路由配置 - 替代动态获取的路由
export const staticRoutes: AppCustomRouteRecordRaw[] = [
  {
    path: '/workbranch',
    name: 'workbranch',
    component: 'Layout',
    hidden: false,
    meta: {
      title: '工作台',
      icon: 'icon-workbench'
    },
    children: [
      {
        path: 'index',
        name: 'workbranch-index',
        component: 'workbranch/index',
        hidden: false,
        meta: {
          title: '工作台',
          icon: 'icon-workbench'
        }
      }
    ]
  },
  {
    path: '/panel',
    name: 'panel',
    component: 'Layout',
    hidden: false,
    meta: {
      title: '仪表板',
      icon: 'icon-dashboard'
    },
    children: [
      {
        path: 'index',
        name: 'panel-index',
        component: 'panel/index',
        hidden: false,
        meta: {
          title: '仪表板',
          icon: 'icon-dashboard'
        }
      }
    ]
  },
  {
    path: '/chart',
    name: 'chart',
    component: 'Layout',
    hidden: false,
    meta: {
      title: '图表',
      icon: 'icon-chart'
    },
    children: [
      {
        path: 'index',
        name: 'chart-index',
        component: 'chart/index',
        hidden: false,
        meta: {
          title: '图表管理',
          icon: 'icon-chart'
        }
      }
    ]
  },
  {
    path: '/dataset',
    name: 'dataset',
    component: 'Layout',
    hidden: false,
    meta: {
      title: '数据集',
      icon: 'icon-dataset'
    },
    children: [
      {
        path: 'index',
        name: 'dataset-index',
        component: 'dataset/index',
        hidden: false,
        meta: {
          title: '数据集管理',
          icon: 'icon-dataset'
        }
      }
    ]
  },
  {
    path: '/datasource',
    name: 'datasource',
    component: 'Layout',
    hidden: false,
    meta: {
      title: '数据源',
      icon: 'icon-datasource'
    },
    children: [
      {
        path: 'index',
        name: 'datasource-index',
        component: 'datasource/index',
        hidden: false,
        meta: {
          title: '数据源管理',
          icon: 'icon-datasource'
        }
      }
    ]
  },
  {
    path: '/system',
    name: 'system',
    component: 'Layout',
    hidden: false,
    meta: {
      title: '系统管理',
      icon: 'icon-system'
    },
    children: [
      {
        path: 'user',
        name: 'system-user',
        component: 'system/user/index',
        hidden: false,
        meta: {
          title: '用户管理',
          icon: 'icon-user'
        }
      },
      {
        path: 'role',
        name: 'system-role',
        component: 'system/role/index',
        hidden: false,
        meta: {
          title: '角色管理',
          icon: 'icon-role'
        }
      },
      {
        path: 'org',
        name: 'system-org',
        component: 'system/org/index',
        hidden: false,
        meta: {
          title: '组织管理',
          icon: 'icon-org'
        }
      },
      {
        path: 'menu',
        name: 'system-menu',
        component: 'system/menu/index',
        hidden: false,
        meta: {
          title: '菜单管理',
          icon: 'icon-menu'
        }
      }
    ]
  }
]

// 根据用户权限过滤路由的函数
export const filterRoutesByPermission = (
  routes: AppCustomRouteRecordRaw[],
  userPermissions?: string[]
): AppCustomRouteRecordRaw[] => {
  // 如果没有传入权限信息，返回所有路由（开发环境或管理员）
  if (!userPermissions || userPermissions.length === 0) {
    return routes
  }

  return routes.filter(route => {
    // 检查当前路由是否有权限访问
    const hasPermission =
      userPermissions.includes(route.name as string) || userPermissions.includes(route.path)

    if (hasPermission && route.children) {
      // 递归过滤子路由
      route.children = filterRoutesByPermission(route.children, userPermissions)
    }

    return hasPermission
  })
}

// 获取用户可访问的路由
export const getUserAccessibleRoutes = (userPermissions?: string[]): AppCustomRouteRecordRaw[] => {
  return filterRoutesByPermission(staticRoutes, userPermissions)
}
