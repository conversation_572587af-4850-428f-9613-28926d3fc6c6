<template>
  <div class="nested-menu-attr">
    <el-collapse v-model="activeName" @change="onChange">
      <!-- 菜单配置 -->
      <el-collapse-item title="菜单配置" name="menuConfig">
        <el-form label-position="top" size="small">
          <el-form-item label="菜单模式">
            <el-select v-model="propValue.mode" @change="handleChange">
              <el-option label="垂直" value="vertical" />
              <el-option label="水平" value="horizontal" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="是否折叠">
            <el-switch
              v-model="propValue.collapsed"
              @change="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="只保持一个子菜单展开">
            <el-switch
              v-model="propValue.uniqueOpened"
              @change="handleChange"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 样式配置 -->
      <el-collapse-item title="样式配置" name="styleConfig">
        <el-form label-position="top" size="small">
          <el-form-item label="背景颜色">
            <el-color-picker
              v-model="propValue.backgroundColor"
              @change="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="文字颜色">
            <el-color-picker
              v-model="propValue.textColor"
              @change="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="激活文字颜色">
            <el-color-picker
              v-model="propValue.activeTextColor"
              @change="handleChange"
            />
          </el-form-item>

          <el-form-item label="字体大小">
            <el-input-number
              v-model="propValue.fontSize"
              :min="10"
              :max="72"
              :step="1"
              @change="handleChange"
            />
          </el-form-item>

          <el-form-item label="字体粗细">
            <el-select v-model="propValue.fontWeight" @change="handleChange">
              <el-option label="正常" value="normal" />
              <el-option label="粗体" value="bold" />
              <el-option label="细体" value="lighter" />
              <el-option label="100" value="100" />
              <el-option label="200" value="200" />
              <el-option label="300" value="300" />
              <el-option label="400" value="400" />
              <el-option label="500" value="500" />
              <el-option label="600" value="600" />
              <el-option label="700" value="700" />
              <el-option label="800" value="800" />
              <el-option label="900" value="900" />
            </el-select>
          </el-form-item>

          <el-form-item label="字体样式">
            <el-select v-model="propValue.fontStyle" @change="handleChange">
              <el-option label="正常" value="normal" />
              <el-option label="斜体" value="italic" />
              <el-option label="倾斜" value="oblique" />
            </el-select>
          </el-form-item>

          <el-form-item label="文字对齐">
            <el-select v-model="propValue.textAlign" @change="handleChange">
              <el-option label="左对齐" value="left" />
              <el-option label="居中" value="center" />
              <el-option label="右对齐" value="right" />
            </el-select>
          </el-form-item>

          <el-form-item label="行高">
            <el-input-number
              v-model="propValue.lineHeight"
              :min="1"
              :max="3"
              :step="0.1"
              :precision="1"
              @change="handleChange"
            />
          </el-form-item>

          <el-form-item label="字符间距">
            <el-input-number
              v-model="propValue.letterSpacing"
              :min="-5"
              :max="10"
              :step="0.5"
              :precision="1"
              @change="handleChange"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 菜单项配置 -->
      <el-collapse-item title="菜单项配置" name="menuItems">
        <div class="menu-items-config">
          <div class="menu-item-header">
            <span>菜单项列表</span>
            <el-button type="primary" size="small" @click="addMenuItem">
              添加菜单项
            </el-button>
          </div>
          
          <div class="menu-items-list">
            <div
              v-for="(item, index) in (propValue.menuItems || [])"
              :key="item?.id || index"
              class="menu-item-config"
            >
              <div class="menu-item-header">
                <span>{{ item?.title || '未命名菜单' }}</span>
                <div class="menu-item-actions">
                  <el-button size="small" @click="addSubMenuItem(index)">
                    添加子项
                  </el-button>
                  <el-button size="small" type="danger" @click="removeMenuItem(index)">
                    删除
                  </el-button>
                </div>
              </div>
              
              <el-form label-position="top" size="small" v-if="item">
                <el-form-item label="标题">
                  <el-input
                    v-model="item.title"
                    placeholder="请输入菜单标题"
                    @input="handleChange"
                  />
                </el-form-item>

                <el-form-item label="图标">
                  <el-input
                    v-model="item.icon"
                    placeholder="请输入图标名称"
                    @input="handleChange"
                  />
                </el-form-item>

                <el-form-item label="跳转链接">
                  <el-input
                    v-model="item.link"
                    placeholder="请输入跳转链接"
                    @input="handleChange"
                  />
                </el-form-item>

                <el-form-item label="打开方式">
                  <el-select v-model="item.target" @change="handleChange">
                    <el-option label="当前窗口" value="_self" />
                    <el-option label="新窗口" value="_blank" />
                  </el-select>
                </el-form-item>
              </el-form>
              
              <!-- 子菜单项 -->
              <div v-if="item?.children && item.children.length > 0" class="sub-menu-items">
                <div class="sub-menu-title">子菜单项</div>
                <div
                  v-for="(child, childIndex) in (item.children || [])"
                  :key="child?.id || childIndex"
                  class="sub-menu-item-config"
                >
                  <div class="sub-menu-item-header">
                    <span>{{ child?.title || '未命名子菜单' }}</span>
                    <el-button 
                      size="small" 
                      type="danger" 
                      @click="removeSubMenuItem(index, childIndex)"
                    >
                      删除
                    </el-button>
                  </div>
                  
                  <el-form label-position="top" size="small" v-if="child">
                    <el-form-item label="标题">
                      <el-input
                        v-model="child.title"
                        placeholder="请输入子菜单标题"
                        @input="handleChange"
                      />
                    </el-form-item>

                    <el-form-item label="图标">
                      <el-input
                        v-model="child.icon"
                        placeholder="请输入图标名称"
                        @input="handleChange"
                      />
                    </el-form-item>

                    <el-form-item label="跳转链接">
                      <el-input
                        v-model="child.link"
                        placeholder="请输入跳转链接"
                        @input="handleChange"
                      />
                    </el-form-item>

                    <el-form-item label="打开方式">
                      <el-select v-model="child.target" @change="handleChange">
                        <el-option label="当前窗口" value="_self" />
                        <el-option label="新窗口" value="_blank" />
                      </el-select>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, toRefs, onMounted } from 'vue'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import { snapshotStoreWithOut } from '@/store/modules/data-visualization/snapshot'
import { storeToRefs } from 'pinia'
import {
  ElCollapse,
  ElCollapseItem,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElSwitch,
  ElColorPicker,
  ElButton,
  ElInput,
  ElInputNumber
} from 'element-plus-secondary'

const props = defineProps({
  themes: {
    type: String,
    default: 'light'
  }
})

const { themes } = toRefs(props)
const dvMainStore = dvMainStoreWithOut()
const snapshotStore = snapshotStoreWithOut()
const { curComponent } = storeToRefs(dvMainStore)

const activeName = ref(['menuConfig'])

// 初始化propValue
const initPropValue = () => {
  if (!curComponent.value?.propValue) {
    return
  }

  const value = curComponent.value.propValue

  // 确保必要的属性存在
  if (!value.menuItems) {
    value.menuItems = []
  }
  if (!value.mode) {
    value.mode = 'vertical'
  }
  if (!value.backgroundColor) {
    value.backgroundColor = '#ffffff'
  }
  if (!value.textColor) {
    value.textColor = '#303133'
  }
  if (!value.activeTextColor) {
    value.activeTextColor = '#409eff'
  }
  if (value.collapsed === undefined) {
    value.collapsed = false
  }
  if (value.uniqueOpened === undefined) {
    value.uniqueOpened = true
  }
  if (!value.fontSize) {
    value.fontSize = 14
  }
  if (!value.fontWeight) {
    value.fontWeight = 'normal'
  }
  if (!value.fontStyle) {
    value.fontStyle = 'normal'
  }
  if (!value.textAlign) {
    value.textAlign = 'left'
  }
  if (!value.lineHeight) {
    value.lineHeight = 1.5
  }
  if (value.letterSpacing === undefined) {
    value.letterSpacing = 0
  }
}

// 直接使用curComponent的propValue
const propValue = computed(() => {
  return curComponent.value?.propValue || {}
})

// 组件挂载时初始化
onMounted(() => {
  initPropValue()
})

const onChange = () => {
  // 折叠面板变化处理
}

const handleChange = () => {
  snapshotStore.recordSnapshotCache('nestedMenuAttrChange')
}

// 生成唯一ID
const generateId = () => {
  return 'menu_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 添加菜单项
const addMenuItem = () => {
  if (!curComponent.value?.propValue) {
    return
  }

  if (!curComponent.value.propValue.menuItems) {
    curComponent.value.propValue.menuItems = []
  }

  curComponent.value.propValue.menuItems.push({
    id: generateId(),
    title: '新菜单项',
    icon: '',
    link: '',
    target: '_self',
    children: []
  })

  handleChange()
}

// 删除菜单项
const removeMenuItem = (index: number) => {
  if (!curComponent.value?.propValue?.menuItems) {
    return
  }
  curComponent.value.propValue.menuItems.splice(index, 1)
  handleChange()
}

// 添加子菜单项
const addSubMenuItem = (parentIndex: number) => {
  if (!curComponent.value?.propValue?.menuItems?.[parentIndex]) {
    return
  }

  if (!curComponent.value.propValue.menuItems[parentIndex].children) {
    curComponent.value.propValue.menuItems[parentIndex].children = []
  }

  curComponent.value.propValue.menuItems[parentIndex].children.push({
    id: generateId(),
    title: '新子菜单项',
    icon: '',
    link: '',
    target: '_self'
  })

  handleChange()
}

// 删除子菜单项
const removeSubMenuItem = (parentIndex: number, childIndex: number) => {
  if (!curComponent.value?.propValue?.menuItems?.[parentIndex]?.children) {
    return
  }
  curComponent.value.propValue.menuItems[parentIndex].children.splice(childIndex, 1)
  handleChange()
}
</script>

<style lang="less" scoped>
.nested-menu-attr {
  .menu-items-config {
    .menu-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding: 8px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
    
    .menu-item-config {
      margin-bottom: 16px;
      padding: 12px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      
      .menu-item-actions {
        display: flex;
        gap: 8px;
      }
      
      .sub-menu-items {
        margin-top: 12px;
        
        .sub-menu-title {
          font-weight: bold;
          margin-bottom: 8px;
          color: #606266;
        }
        
        .sub-menu-item-config {
          margin-bottom: 12px;
          padding: 8px;
          background-color: #fafafa;
          border-radius: 4px;
          
          .sub-menu-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}
</style>
