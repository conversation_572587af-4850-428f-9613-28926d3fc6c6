/**
 * 图片Base64功能测试
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { 
  fileToBase64, 
  compressImageToBase64, 
  isBase64Image, 
  getBase64Size, 
  formatFileSize,
  validateImageFile,
  handleImageUpload
} from '@/utils/imageBase64Utils'
import { uploadImage, validateImage } from '@/services/imageService'
import { getImageConfig, ImageMode } from '@/config/imageConfig'

// 创建测试用的图片文件
function createTestImageFile(name: string = 'test.jpg', type: string = 'image/jpeg', size: number = 1024): File {
  const canvas = document.createElement('canvas')
  canvas.width = 100
  canvas.height = 100
  const ctx = canvas.getContext('2d')
  
  // 绘制一个简单的测试图片
  if (ctx) {
    ctx.fillStyle = '#ff0000'
    ctx.fillRect(0, 0, 50, 50)
    ctx.fillStyle = '#00ff00'
    ctx.fillRect(50, 0, 50, 50)
    ctx.fillStyle = '#0000ff'
    ctx.fillRect(0, 50, 50, 50)
    ctx.fillStyle = '#ffff00'
    ctx.fillRect(50, 50, 50, 50)
  }
  
  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      if (blob) {
        const file = new File([blob], name, { type })
        resolve(file)
      }
    }, type)
  }) as any
}

describe('图片Base64工具函数测试', () => {
  let testFile: File

  beforeEach(async () => {
    testFile = await createTestImageFile()
  })

  describe('isBase64Image', () => {
    it('应该正确识别base64图片', () => {
      expect(isBase64Image('data:image/jpeg;base64,/9j/4AAQ...')).toBe(true)
      expect(isBase64Image('data:image/png;base64,iVBORw0KGgo...')).toBe(true)
      expect(isBase64Image('/static-resource/test.jpg')).toBe(false)
      expect(isBase64Image('http://example.com/image.jpg')).toBe(false)
      expect(isBase64Image('')).toBe(false)
      expect(isBase64Image(null as any)).toBe(false)
    })
  })

  describe('getBase64Size', () => {
    it('应该正确计算base64图片大小', () => {
      const base64 = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD'
      const size = getBase64Size(base64)
      expect(size).toBeGreaterThan(0)
    })

    it('对于非base64字符串应该返回0', () => {
      expect(getBase64Size('/static-resource/test.jpg')).toBe(0)
      expect(getBase64Size('')).toBe(0)
    })
  })

  describe('formatFileSize', () => {
    it('应该正确格式化文件大小', () => {
      expect(formatFileSize(0)).toBe('0 B')
      expect(formatFileSize(1024)).toBe('1 KB')
      expect(formatFileSize(1024 * 1024)).toBe('1 MB')
      expect(formatFileSize(1024 * 1024 * 1024)).toBe('1 GB')
      expect(formatFileSize(1536)).toBe('1.5 KB')
    })
  })

  describe('validateImageFile', () => {
    it('应该验证有效的图片文件', () => {
      const result = validateImageFile(testFile)
      expect(result.valid).toBe(true)
    })

    it('应该拒绝非图片文件', () => {
      const textFile = new File(['test'], 'test.txt', { type: 'text/plain' })
      const result = validateImageFile(textFile)
      expect(result.valid).toBe(false)
      expect(result.message).toContain('请上传图片文件')
    })

    it('应该拒绝过大的文件', () => {
      const largeFile = new File(['x'.repeat(16 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' })
      const result = validateImageFile(largeFile, 15 * 1024 * 1024)
      expect(result.valid).toBe(false)
      expect(result.message).toContain('图片大小不能超过')
    })
  })

  describe('fileToBase64', () => {
    it('应该将文件转换为base64', async () => {
      const base64 = await fileToBase64(testFile)
      expect(base64).toMatch(/^data:image\/jpeg;base64,/)
      expect(isBase64Image(base64)).toBe(true)
    })
  })

  describe('handleImageUpload', () => {
    it('应该成功处理图片上传', async () => {
      const result = await handleImageUpload(testFile)
      expect(result.success).toBe(true)
      expect(result.data).toBeDefined()
      expect(isBase64Image(result.data!)).toBe(true)
    })

    it('应该拒绝无效文件', async () => {
      const textFile = new File(['test'], 'test.txt', { type: 'text/plain' })
      const result = await handleImageUpload(textFile)
      expect(result.success).toBe(false)
      expect(result.message).toBeDefined()
    })
  })
})

describe('图片服务测试', () => {
  let testFile: File

  beforeEach(async () => {
    testFile = await createTestImageFile()
  })

  describe('validateImage', () => {
    it('应该验证有效的图片文件', () => {
      const result = validateImage(testFile)
      expect(result.valid).toBe(true)
    })
  })

  describe('uploadImage', () => {
    it('应该成功上传图片', async () => {
      const result = await uploadImage(testFile)
      expect(result.success).toBe(true)
      expect(result.data).toBeDefined()
      expect(result.mode).toBeDefined()
    })
  })
})

describe('配置管理测试', () => {
  describe('getImageConfig', () => {
    it('应该返回有效的配置', () => {
      const config = getImageConfig()
      expect(config).toBeDefined()
      expect(config.useBase64).toBeDefined()
      expect(config.compression).toBeDefined()
      expect(config.maxFileSize).toBeGreaterThan(0)
      expect(config.supportedTypes).toBeInstanceOf(Array)
    })
  })
})

describe('兼容性测试', () => {
  it('应该正确处理传统URL', () => {
    const traditionalUrl = '/static-resource/test.jpg'
    expect(isBase64Image(traditionalUrl)).toBe(false)
  })

  it('应该正确处理base64 URL', () => {
    const base64Url = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD'
    expect(isBase64Image(base64Url)).toBe(true)
  })
})

describe('错误处理测试', () => {
  it('应该处理空文件', async () => {
    const emptyFile = new File([], 'empty.jpg', { type: 'image/jpeg' })
    const result = await handleImageUpload(emptyFile)
    expect(result.success).toBe(false)
  })

  it('应该处理损坏的base64数据', () => {
    const invalidBase64 = 'data:image/jpeg;base64,invalid'
    expect(getBase64Size(invalidBase64)).toBe(0)
  })
})
