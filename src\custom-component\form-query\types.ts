/**
 * 表单查询组件类型定义
 */

export type FormQueryFieldType = 
  | 'text' 
  | 'number' 
  | 'select' 
  | 'date' 
  | 'daterange'

export interface SelectOption {
  label: string
  value: string | number
}

export interface FieldValidation {
  min?: number
  max?: number
  pattern?: string
  message?: string
}

export interface FieldStyle {
  width?: string
  fontSize?: string
  color?: string
}

export interface ParamMapping {
  paramName?: string // 普通字段的参数名映射
  startParam?: string // 日期范围开始参数名
  endParam?: string // 日期范围结束参数名
}

export interface FormQueryField {
  id: string
  type: FormQueryFieldType
  label: string
  name: string
  placeholder?: string
  defaultValue?: any
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  options?: SelectOption[]  // 用于select类型
  validation?: FieldValidation
  style?: FieldStyle
  visible?: boolean
  order?: number
  paramMapping?: ParamMapping // 参数映射配置
}

export interface FormQueryLayout {
  columns: number // 表单列数
  labelPosition: 'top' | 'left' | 'right'
  labelWidth: string
  size: 'large' | 'default' | 'small'
  showBorder: boolean
  spacing: number // 字段间距
  actionsAlign: 'left' | 'center' | 'right' // 按钮对齐方式
  actionsPosition: 'inline' | 'newline' // 按钮位置：inline-字段末位，newline-新行
}

export interface FormQueryStyle {
  backgroundColor?: string
  padding?: string
  borderRadius?: string
  border?: string
}

export interface FormQueryTitleStyle {
  fontSize?: string
  fontWeight?: string
  color?: string
  marginBottom?: string
  textAlign?: 'left' | 'center' | 'right'
}

export interface FormQueryConfig {
  title: string
  showTitle: boolean
  titleStyle: FormQueryTitleStyle
  fields: FormQueryField[]
  layout: FormQueryLayout
  style: FormQueryStyle
  
  // 查询相关配置
  targetComponents: string[] // 目标组件ID列表
  autoQuery: boolean // 是否自动查询（字段变化时）
  autoQueryDelay: number // 自动查询延迟（毫秒）
  initialQuery: boolean // 是否初始查询
  resetAndQuery: boolean // 重置后是否自动查询
  
  // 按钮配置
  showQueryButton: boolean
  queryButtonText: string
  showResetButton: boolean
  resetButtonText: string
  showClearButton: boolean
  clearButtonText: string
}

// 默认配置
export const DEFAULT_FORM_QUERY_CONFIG: FormQueryConfig = {
  title: '查询表单',
  showTitle: true,
  titleStyle: {
    fontSize: '16px',
    fontWeight: 'bold',
    color: '#333',
    marginBottom: '16px',
    textAlign: 'left'
  },
  fields: [
    {
      id: 'field_1',
      type: 'text',
      label: '关键词',
      name: 'keyword',
      placeholder: '请输入关键词',
      required: false,
      visible: true,
      order: 1,
      validation: {},
      paramMapping: {
        paramName: 'keyword'
      }
    }
  ],
  layout: {
    columns: 2,
    labelPosition: 'top',
    labelWidth: '100px',
    size: 'default',
    showBorder: false,
    spacing: 16,
    actionsAlign: 'left',
    actionsPosition: 'newline'
  },
  style: {
    backgroundColor: 'transparent',
    padding: '16px',
    borderRadius: '4px',
    border: 'none'
  },
  targetComponents: [], // 需要在配置时指定目标表格组件ID
  autoQuery: false,
  autoQueryDelay: 500,
  initialQuery: false,
  resetAndQuery: true,
  showQueryButton: true,
  queryButtonText: '查询',
  showResetButton: true,
  resetButtonText: '重置',
  showClearButton: false,
  clearButtonText: '清空'
}

// 字段类型选项
export const FIELD_TYPE_OPTIONS = [
  { label: '文本输入', value: 'text' },
  { label: '数字输入', value: 'number' },
  { label: '下拉选择', value: 'select' },
  { label: '日期选择', value: 'date' },
  { label: '日期范围', value: 'daterange' }
]

// 布局选项
export const LABEL_POSITION_OPTIONS = [
  { label: '顶部', value: 'top' },
  { label: '左侧', value: 'left' },
  { label: '右侧', value: 'right' }
]

export const SIZE_OPTIONS = [
  { label: '大', value: 'large' },
  { label: '默认', value: 'default' },
  { label: '小', value: 'small' }
]

export const ACTIONS_ALIGN_OPTIONS = [
  { label: '左对齐', value: 'left' },
  { label: '居中', value: 'center' },
  { label: '右对齐', value: 'right' }
]

export const ACTIONS_POSITION_OPTIONS = [
  { label: '字段末位', value: 'inline' },
  { label: '新行末位', value: 'newline' }
]

// 生成唯一ID
export const generateID = (): string => {
  return 'field_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}
