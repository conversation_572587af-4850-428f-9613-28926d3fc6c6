<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus-secondary'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import type { GlobalVariable } from '@/types/globalVariable'

const dvMainStore = dvMainStoreWithOut()

// 测试状态
const testResults = ref<string[]>([])

// 计算属性
const globalVariables = computed(() => dvMainStore.globalVariables)
const dvInfo = computed(() => dvMainStore.dvInfo)

// 添加测试结果
const addTestResult = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  const timestamp = new Date().toLocaleTimeString()
  const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'
  testResults.value.push(`[${timestamp}] ${prefix} ${message}`)
}

// 清空测试结果
const clearTestResults = () => {
  testResults.value = []
}

// 测试存储方案
const testStorageIntegration = () => {
  addTestResult('开始测试：全局变量存储集成')
  
  try {
    // 1. 创建测试变量
    const testVariable: GlobalVariable = {
      id: 'storage-test-1',
      key: 'testStorageVar',
      type: 'static',
      value: 'storage-test-value',
      description: '存储测试变量',
      enabled: true,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
    
    dvMainStore.addGlobalVariable(testVariable)
    addTestResult('创建测试变量成功', 'success')
    
    // 2. 模拟保存过程
    const canvasInfo = {
      globalVariables: JSON.stringify(dvMainStore.globalVariables),
      // 其他配置...
      componentData: '[]',
      canvasStyleData: '{}',
      canvasViewInfo: {}
    }
    
    addTestResult('模拟保存过程：序列化全局变量配置', 'success')
    addTestResult(`序列化结果长度: ${canvasInfo.globalVariables.length} 字符`)
    
    // 3. 模拟加载过程
    const loadedVariables = JSON.parse(canvasInfo.globalVariables) as GlobalVariable[]
    addTestResult('模拟加载过程：反序列化全局变量配置', 'success')
    addTestResult(`加载的变量数量: ${loadedVariables.length}`)
    
    // 4. 验证数据完整性
    const originalVariable = dvMainStore.globalVariables.find(v => v.id === 'storage-test-1')
    const loadedVariable = loadedVariables.find(v => v.id === 'storage-test-1')
    
    if (originalVariable && loadedVariable) {
      const isDataIntact = 
        originalVariable.key === loadedVariable.key &&
        originalVariable.value === loadedVariable.value &&
        originalVariable.type === loadedVariable.type
      
      if (isDataIntact) {
        addTestResult('数据完整性验证通过', 'success')
      } else {
        addTestResult('数据完整性验证失败', 'error')
      }
    } else {
      addTestResult('找不到测试变量', 'error')
    }
    
    // 5. 测试重新加载
    dvMainStore.setGlobalVariables(loadedVariables)
    dvMainStore.resolveGlobalVariables()
    addTestResult('重新加载变量配置成功', 'success')
    
    addTestResult('存储集成测试完成', 'success')
    
  } catch (error) {
    addTestResult(`存储集成测试失败: ${error.message}`, 'error')
  }
}

// 测试仪表板配置集成
const testDashboardIntegration = () => {
  addTestResult('开始测试：仪表板配置集成')
  
  try {
    // 检查当前仪表板信息
    addTestResult(`当前仪表板ID: ${dvInfo.value.id || '未设置'}`)
    addTestResult(`当前仪表板名称: ${dvInfo.value.name || '未设置'}`)
    addTestResult(`当前仪表板类型: ${dvInfo.value.type || '未设置'}`)
    
    // 检查全局变量状态
    addTestResult(`当前全局变量数量: ${globalVariables.value.length}`)
    
    if (globalVariables.value.length > 0) {
      addTestResult('全局变量列表:')
      globalVariables.value.forEach(variable => {
        addTestResult(`  - ${variable.key}: ${variable.value || variable.routeParam} (${variable.type})`)
      })
    }
    
    // 模拟canvasSave中的逻辑
    const mockCanvasInfo = {
      globalVariables: JSON.stringify(globalVariables.value),
      componentData: JSON.stringify([]),
      canvasStyleData: JSON.stringify({}),
      canvasViewInfo: {},
      ...dvInfo.value
    }
    
    addTestResult('模拟canvasSave逻辑成功', 'success')
    addTestResult(`配置对象包含globalVariables字段: ${!!mockCanvasInfo.globalVariables}`)
    
    addTestResult('仪表板配置集成测试完成', 'success')
    
  } catch (error) {
    addTestResult(`仪表板配置集成测试失败: ${error.message}`, 'error')
  }
}

// 清理测试数据
const cleanupTestData = () => {
  try {
    dvMainStore.deleteGlobalVariable('storage-test-1')
    addTestResult('清理测试数据完成', 'success')
  } catch (error) {
    // 忽略删除错误
  }
}

// 运行所有测试
const runAllTests = async () => {
  clearTestResults()
  addTestResult('=== 开始全局变量存储方案测试 ===')
  
  testStorageIntegration()
  await new Promise(resolve => setTimeout(resolve, 500))
  
  testDashboardIntegration()
  
  addTestResult('=== 测试完成 ===')
  addTestResult('说明：此测试验证了全局变量与仪表板配置的集成存储方案')
}

// 组件挂载时初始化
onMounted(() => {
  addTestResult('全局变量存储测试页面已加载')
  addTestResult('点击"运行测试"开始测试存储方案')
})
</script>

<template>
  <div class="global-variable-storage-test">
    <div class="test-header">
      <h2>全局变量存储方案测试</h2>
      <p>此页面用于测试全局变量与仪表板配置的集成存储方案</p>
    </div>

    <div class="test-controls">
      <el-button type="primary" @click="runAllTests">
        运行存储测试
      </el-button>
      <el-button @click="clearTestResults">
        清空结果
      </el-button>
      <el-button type="warning" @click="cleanupTestData">
        清理测试数据
      </el-button>
    </div>

    <div class="test-content">
      <!-- 当前状态 -->
      <div class="current-status">
        <h3>当前状态</h3>
        <div class="status-info">
          <div class="status-item">
            <span class="label">仪表板ID:</span>
            <span class="value">{{ dvInfo.id || '未设置' }}</span>
          </div>
          <div class="status-item">
            <span class="label">仪表板名称:</span>
            <span class="value">{{ dvInfo.name || '未设置' }}</span>
          </div>
          <div class="status-item">
            <span class="label">全局变量数量:</span>
            <span class="value">{{ globalVariables.length }}</span>
          </div>
        </div>
      </div>

      <!-- 测试结果 -->
      <div class="test-results">
        <h3>测试结果</h3>
        <div class="results-container">
          <div v-if="testResults.length === 0" class="no-results">
            暂无测试结果
          </div>
          <div v-else class="results-list">
            <div
              v-for="(result, index) in testResults"
              :key="index"
              class="result-item"
            >
              {{ result }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.global-variable-storage-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .test-header {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 8px;

    h2 {
      margin: 0 0 8px 0;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .test-controls {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .el-button + .el-button {
      margin-left: 12px;
    }
  }

  .test-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;

    .current-status,
    .test-results {
      background-color: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 16px;

      h3 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 16px;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 8px;
      }
    }

    .current-status {
      .status-info {
        .status-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .label {
            font-weight: 500;
            color: #606266;
          }

          .value {
            color: #303133;
          }
        }
      }
    }

    .test-results {
      .results-container {
        max-height: 400px;
        overflow-y: auto;

        .no-results {
          text-align: center;
          color: #909399;
          padding: 40px 20px;
        }

        .results-list {
          .result-item {
            padding: 4px 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            border-bottom: 1px solid #f0f0f0;
            white-space: pre-wrap;
            word-break: break-all;

            &:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .global-variable-storage-test {
    .test-content {
      grid-template-columns: 1fr;
    }
  }
}
</style>
