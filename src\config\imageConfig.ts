/**
 * 图片处理配置
 */

export interface ImageConfig {
  // 是否启用base64模式（true: 使用base64存储, false: 上传到服务器）
  useBase64: boolean
  
  // 图片压缩配置
  compression: {
    // 是否启用压缩
    enabled: boolean
    // 压缩质量 (0-1)
    quality: number
    // 最大宽度
    maxWidth: number
    // 最大高度
    maxHeight: number
  }
  
  // 文件大小限制（字节）
  maxFileSize: number
  
  // 支持的文件类型
  supportedTypes: string[]
}

// 默认配置
export const DEFAULT_IMAGE_CONFIG: ImageConfig = {
  useBase64: true, // 默认启用base64模式
  compression: {
    enabled: true,
    quality: 0.8,
    maxWidth: 1920,
    maxHeight: 1080
  },
  maxFileSize: 15 * 1024 * 1024, // 15MB
  supportedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml']
}

// 获取当前图片配置
export function getImageConfig(): ImageConfig {
  // 可以从环境变量或配置文件中读取
  const envConfig = {
    useBase64: import.meta.env.VITE_IMAGE_USE_BASE64 !== 'false', // 默认true，除非明确设置为false
    compression: {
      enabled: import.meta.env.VITE_IMAGE_COMPRESSION_ENABLED !== 'false',
      quality: Number(import.meta.env.VITE_IMAGE_COMPRESSION_QUALITY) || DEFAULT_IMAGE_CONFIG.compression.quality,
      maxWidth: Number(import.meta.env.VITE_IMAGE_MAX_WIDTH) || DEFAULT_IMAGE_CONFIG.compression.maxWidth,
      maxHeight: Number(import.meta.env.VITE_IMAGE_MAX_HEIGHT) || DEFAULT_IMAGE_CONFIG.compression.maxHeight
    },
    maxFileSize: Number(import.meta.env.VITE_IMAGE_MAX_FILE_SIZE) || DEFAULT_IMAGE_CONFIG.maxFileSize,
    supportedTypes: DEFAULT_IMAGE_CONFIG.supportedTypes
  }
  
  return { ...DEFAULT_IMAGE_CONFIG, ...envConfig }
}

// 图片处理模式枚举
export enum ImageMode {
  BASE64 = 'base64',
  UPLOAD = 'upload'
}

// 获取当前图片处理模式
export function getImageMode(): ImageMode {
  return getImageConfig().useBase64 ? ImageMode.BASE64 : ImageMode.UPLOAD
}
