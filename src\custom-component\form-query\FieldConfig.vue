<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑字段' : '添加字段'"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      size="small"
    >
      <!-- 基础信息 -->
      <el-form-item label="字段类型" prop="type">
        <el-select v-model="formData.type" @change="handleTypeChange">
          <el-option
            v-for="option in FIELD_TYPE_OPTIONS"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="字段标签" prop="label">
        <el-input v-model="formData.label" placeholder="显示给用户的标签" />
      </el-form-item>

      <el-form-item label="字段名称" prop="name">
        <el-input v-model="formData.name" placeholder="用于数据提交的字段名" />
      </el-form-item>

      <el-form-item label="占位符">
        <el-input v-model="formData.placeholder" placeholder="输入框的占位符文本" />
      </el-form-item>

      <el-form-item label="默认值">
        <!-- 文本类型 -->
        <el-input
          v-if="formData.type === 'text'"
          v-model="formData.defaultValue"
          placeholder="默认文本值"
        />
        
        <!-- 数字类型 -->
        <el-input-number
          v-else-if="formData.type === 'number'"
          v-model="formData.defaultValue"
          placeholder="默认数字值"
          style="width: 100%"
        />
        
        <!-- 选择类型 -->
        <el-select
          v-else-if="formData.type === 'select'"
          v-model="formData.defaultValue"
          placeholder="选择默认值"
          style="width: 100%"
        >
          <el-option
            v-for="option in (formData.options || [])"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        
        <!-- 日期类型 -->
        <el-date-picker
          v-else-if="formData.type === 'date'"
          v-model="formData.defaultValue"
          type="date"
          placeholder="选择默认日期"
          style="width: 100%"
        />
        
        <!-- 日期范围类型 -->
        <el-date-picker
          v-else-if="formData.type === 'daterange'"
          v-model="formData.defaultValue"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 选项配置（仅选择类型） -->
      <el-form-item v-if="needsOptions && formData.options" label="选项配置">
        <div class="options-config">
          <div
            v-for="(option, index) in (formData.options || [])"
            :key="index"
            class="option-item"
          >
            <el-input
              v-model="option.label"
              placeholder="显示文本"
              style="width: 45%"
            />
            <el-input
              v-model="option.value"
              placeholder="选项值"
              style="width: 45%"
            />
            <el-button
              type="danger"
              size="small"
              @click="removeOption(index)"
            >
              删除
            </el-button>
          </div>
          <el-button type="primary" size="small" @click="addOption">
            添加选项
          </el-button>
        </div>
      </el-form-item>

      <!-- 参数映射配置 -->
      <el-form-item label="参数映射" v-if="formData.paramMapping">
        <div v-if="formData.type === 'daterange'" class="param-mapping">
          <el-form-item label="开始参数名" label-width="100px">
            <el-input
              v-model="formData.paramMapping.startParam"
              :placeholder="`${formData.name || 'field'}_start`"
            />
          </el-form-item>
          <el-form-item label="结束参数名" label-width="100px">
            <el-input
              v-model="formData.paramMapping.endParam"
              :placeholder="`${formData.name || 'field'}_end`"
            />
          </el-form-item>
        </div>
        <div v-else>
          <el-input
            v-model="formData.paramMapping.paramName"
            :placeholder="formData.name || 'field'"
          />
          <div class="form-tip">API请求时使用的参数名，默认使用字段名称</div>
        </div>
      </el-form-item>

      <!-- 验证规则 -->
      <el-form-item label="验证规则" v-if="formData.validation">
        <el-checkbox v-model="formData.required">必填</el-checkbox>

        <div v-if="formData.type === 'text'" class="validation-config">
          <el-form-item label="最小长度" label-width="80px">
            <el-input-number v-model="formData.validation.min" :min="0" />
          </el-form-item>
          <el-form-item label="最大长度" label-width="80px">
            <el-input-number v-model="formData.validation.max" :min="0" />
          </el-form-item>
          <el-form-item label="正则表达式" label-width="80px">
            <el-input v-model="formData.validation.pattern" placeholder="验证正则" />
          </el-form-item>
        </div>

        <div v-if="formData.type === 'number'" class="validation-config">
          <el-form-item label="最小值" label-width="80px">
            <el-input-number v-model="formData.validation.min" />
          </el-form-item>
          <el-form-item label="最大值" label-width="80px">
            <el-input-number v-model="formData.validation.max" />
          </el-form-item>
        </div>

        <el-form-item label="错误提示" label-width="80px">
          <el-input v-model="formData.validation.message" placeholder="自定义错误提示" />
        </el-form-item>
      </el-form-item>

      <!-- 其他配置 -->
      <el-form-item label="其他配置">
        <el-checkbox v-model="formData.disabled">禁用</el-checkbox>
        <el-checkbox v-model="formData.readonly">只读</el-checkbox>
        <el-checkbox v-model="formData.visible">可见</el-checkbox>
      </el-form-item>

      <el-form-item label="排序">
        <el-input-number v-model="formData.order" :min="1" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="confirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import type { FormQueryField } from './types'
import { FIELD_TYPE_OPTIONS, generateID } from './types'

interface Props {
  visible: boolean
  field?: FormQueryField | null
  existingFields: FormQueryField[]
  isEdit: boolean
}

const props = withDefaults(defineProps<Props>(), {
  field: null,
  isEdit: false
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  confirm: [field: FormQueryField]
}>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单引用
const formRef = ref()

// 表单数据
const formData = ref<Partial<FormQueryField>>({})

// 是否需要选项配置
const needsOptions = computed(() => {
  return formData.value.type === 'select'
})

// 表单验证规则
const rules = {
  label: [
    { required: true, message: '请输入字段标签', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    { 
      validator: (rule: any, value: string, callback: Function) => {
        if (!value) {
          callback()
          return
        }

        // 检查字段名称唯一性
        const isDuplicate = props.existingFields.some((field) => {
          // 编辑时排除自己
          if (props.isEdit && props.field && field.id === props.field.id) {
            return false
          }
          return field.name === value
        })

        if (isDuplicate) {
          callback(new Error('字段名称已存在'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  type: [
    { required: true, message: '请选择字段类型', trigger: 'change' }
  ]
}

// 初始化表单数据
const initFormData = () => {
  // 创建默认数据结构
  const defaultData = {
    id: '',
    type: 'text',
    label: '',
    name: '',
    placeholder: '',
    defaultValue: '',
    required: false,
    disabled: false,
    readonly: false,
    visible: true,
    order: props.existingFields.length + 1,
    options: [],
    validation: {},
    paramMapping: {}
  }

  if (props.field) {
    // 编辑模式，合并现有字段数据和默认数据
    formData.value = {
      ...defaultData,
      ...JSON.parse(JSON.stringify(props.field)),
      // 确保嵌套对象存在
      validation: { ...defaultData.validation, ...(props.field.validation || {}) },
      paramMapping: { ...defaultData.paramMapping, ...(props.field.paramMapping || {}) },
      options: props.field.options ? [...props.field.options] : []
    }
  } else {
    // 新增模式，使用默认数据
    formData.value = { ...defaultData }
  }

  // 确保paramMapping有正确的默认值
  if (formData.value.type === 'daterange') {
    if (!formData.value.paramMapping.startParam) {
      formData.value.paramMapping.startParam = `${formData.value.name || 'field'}_start`
    }
    if (!formData.value.paramMapping.endParam) {
      formData.value.paramMapping.endParam = `${formData.value.name || 'field'}_end`
    }
  } else {
    if (!formData.value.paramMapping.paramName) {
      formData.value.paramMapping.paramName = formData.value.name || ''
    }
  }
}

// 处理类型变化
const handleTypeChange = () => {
  // 重置默认值
  formData.value.defaultValue = ''
  
  // 重置选项
  if (formData.value.type === 'select' && !formData.value.options?.length) {
    formData.value.options = [
      { label: '选项1', value: 'option1' },
      { label: '选项2', value: 'option2' }
    ]
  }
  
  // 重置验证规则
  formData.value.validation = {}
  
  // 重置参数映射
  if (formData.value.type === 'daterange') {
    formData.value.paramMapping = {
      startParam: `${formData.value.name}_start`,
      endParam: `${formData.value.name}_end`
    }
  } else {
    formData.value.paramMapping = {
      paramName: formData.value.name
    }
  }
}

// 添加选项
const addOption = () => {
  if (!formData.value.options) {
    formData.value.options = []
  }
  formData.value.options.push({
    label: `选项${formData.value.options.length + 1}`,
    value: `option${formData.value.options.length + 1}`
  })
}

// 删除选项
const removeOption = (index: number) => {
  formData.value.options?.splice(index, 1)
}

// 取消
const cancel = () => {
  dialogVisible.value = false
}

// 确认
const confirm = async () => {
  try {
    // 验证表单
    await formRef.value.validate()

    // 构建完整的字段对象
    const field: FormQueryField = {
      id: formData.value.id || generateID(),
      type: formData.value.type || 'text',
      label: formData.value.label || '',
      name: formData.value.name || '',
      placeholder: formData.value.placeholder,
      defaultValue: formData.value.defaultValue,
      required: formData.value.required || false,
      disabled: formData.value.disabled || false,
      readonly: formData.value.readonly || false,
      visible: formData.value.visible !== false,
      order: formData.value.order || 1,
      options: needsOptions.value ? formData.value.options : undefined,
      validation: formData.value.validation,
      paramMapping: formData.value.paramMapping
    }

    // 发送确认事件
    emit('confirm', field)

    // 关闭弹框
    dialogVisible.value = false

  } catch (error) {
    console.error('表单验证失败:', error)
    // 验证失败时不关闭弹框，让用户修正错误
    return
  }
}

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      nextTick(() => {
        initFormData()
      })
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.options-config {
  width: 100%;
}

.option-item {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

.validation-config {
  margin-top: 8px;
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
}

.param-mapping {
  width: 100%;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-size: 12px;
}
</style>
