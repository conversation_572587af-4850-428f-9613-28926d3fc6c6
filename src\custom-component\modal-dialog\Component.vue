<template>


  <!-- 编辑模式预览 - 只在编辑模式且组件可见时显示，但不在全屏预览时显示 -->
  <div v-if="isEditMode && !isDashboardFullscreen && !isComponentHidden" class="modal-dialog-wrapper edit-mode">
    <!-- 编辑模式预览 -->
    <div class="modal-dialog edit-mode" :style="modalStyle">
      <div class="modal-header" v-if="config.showHeader">
        <div class="modal-title">{{ config.title || '弹框标题' }}</div>
        <div class="modal-close" v-if="config.showCloseButton">
          <el-icon><Close /></el-icon>
        </div>
      </div>
      <div class="modal-body" :style="bodyStyle">
        <div class="default-content">
          <p>编辑模式预览 - 点击按钮时将显示此弹框</p>
          <p v-if="linkedComponent">关联组件: {{ linkedComponent.component }}</p>
          <p v-else>{{ config.content || '请在配置中选择要显示的组件' }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 隐藏状态的编辑模式占位符 - 不在全屏预览时显示 -->
  <div v-if="isEditMode && !isDashboardFullscreen && isComponentHidden" class="modal-dialog-wrapper edit-mode hidden-placeholder">
    <div class="hidden-indicator">
      <el-icon><Hide /></el-icon>
      <span>弹框组件（已隐藏）</span>
      <p>预览时点击按钮仍可正常打开</p>
    </div>
  </div>

  <!-- 预览模式 - 确保组件始终被挂载以监听事件 -->
  <div v-if="!isEditMode || isDashboardFullscreen" class="modal-runtime-container" :class="{ 'hidden-runtime-container': isComponentHidden }">
    <!-- 预览模式下的占位容器，确保组件被挂载并能监听事件 -->
  </div>

  <!-- 运行时弹框 - 在预览模式或仪表板全屏预览下显示 -->
  <teleport to="body" v-if="(!isEditMode || isDashboardFullscreen) && isVisible">
    <div class="modal-dialog-wrapper runtime-mode">
      <!-- 遮罩层 -->
      <div
        class="modal-overlay"
        :style="overlayStyle"
        @click="handleOverlayClick"
      ></div>

      <!-- 运行时弹框主体 -->
      <div
        class="modal-dialog runtime-mode"
        :style="modalStyle"
        @click.stop
      >
        <!-- 弹框头部 -->
        <div class="modal-header" v-if="config.showHeader">
          <div class="modal-title">{{ config.title || '弹框标题' }}</div>
          <div class="modal-close" v-if="config.showCloseButton" @click="handleClose">
            <el-icon><Close /></el-icon>
          </div>
        </div>

        <!-- 弹框内容 -->
        <div class="modal-body" :style="bodyStyle">
          <!-- 如果关联了组件，渲染关联组件 -->
          <div v-if="linkedComponent" class="linked-component-container">
            <component
              :is="getComponentName(linkedComponent.component)"
              :element="linkedComponent"
              :dv-info="dvInfo"
              :show-position="'preview'"
              :style="linkedComponentStyle"
            />
          </div>

          <!-- 默认内容 -->
          <div v-else class="default-content">
            <p>{{ config.content || '请在配置中选择要显示的组件' }}</p>
          </div>
        </div>

        <!-- 弹框底部 -->
        <div class="modal-footer" v-if="config.showFooter">
          <el-button @click="handleCancel">{{ config.cancelButtonText || '取消' }}</el-button>
          <el-button type="primary" @click="handleConfirm">{{ config.confirmButtonText || '确定' }}</el-button>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onBeforeUnmount } from 'vue'
import { ElIcon, ElButton } from 'element-plus-secondary'
import { Close, Hide } from '@element-plus/icons-vue'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import { storeToRefs } from 'pinia'
import emitter from '@/utils/eventBus'
import findComponent from '@/utils/components'

const props = defineProps({
  element: {
    type: Object,
    required: true
  },
  dvInfo: {
    type: Object,
    required: true
  },
  showPosition: {
    type: String,
    default: 'preview'
  }
})

const dvMainStore = dvMainStoreWithOut()
const { componentData } = storeToRefs(dvMainStore)

// 弹框显示状态
const isVisible = ref(false)

// 检测是否为真正的编辑模式（不包括全屏预览）
const isEditMode = computed(() => {
  return props.showPosition === 'edit'
})

// 检测是否为仪表板全屏预览模式
const isDashboardFullscreen = computed(() => {
  return dvMainStore.fullscreenFlag
})



// 检测组件是否被设置为隐藏
const isComponentHidden = computed(() => {
  return !props.element?.isShow ||
         props.element?.dashboardHidden ||
         props.element?.category === 'hidden' ||
         props.element?.style?.display === 'none' ||
         props.element?.style?.visibility === 'hidden'
})

// 弹框配置
const config = computed(() => {
  return props.element.propValue || {
    title: '弹框标题',
    content: '',
    width: 600,
    height: 400,
    showHeader: true,
    showFooter: true,
    showCloseButton: true,
    closeOnClickOverlay: true,
    linkedComponentId: '',
    position: 'center',
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }
})

// 查找关联的组件
const linkedComponent = computed(() => {
  if (!config.value.linkedComponentId) return null
  
  return componentData.value.find(comp => comp.id === config.value.linkedComponentId)
})

// 遮罩层样式
const overlayStyle = computed(() => ({
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  zIndex: 1000
}))

// 弹框样式
const modalStyle = computed(() => {
  const style: any = {
    width: config.value.width + 'px',
    height: config.value.height + 'px',
    zIndex: 1001
  }
  
  // 根据位置设置样式
  switch (config.value.position) {
    case 'center':
      style.top = '50%'
      style.left = '50%'
      style.transform = 'translate(-50%, -50%)'
      break
    case 'top':
      style.top = '20%'
      style.left = '50%'
      style.transform = 'translateX(-50%)'
      break
    case 'bottom':
      style.bottom = '20%'
      style.left = '50%'
      style.transform = 'translateX(-50%)'
      break
  }
  
  return style
})

// 弹框内容区域样式
const bodyStyle = computed(() => {
  let height = config.value.height - 20 // 基础padding
  if (config.value.showHeader) height -= 50
  if (config.value.showFooter) height -= 60
  
  return {
    height: height + 'px'
  }
})

// 关联组件样式
const linkedComponentStyle = computed(() => ({
  width: '100%',
  height: '100%'
}))

// 获取组件名称
const getComponentName = (componentType: string) => {
  return findComponent(componentType)
}

// 处理遮罩层点击
const handleOverlayClick = () => {
  if (config.value.closeOnClickOverlay) {
    handleClose()
  }
}

// 关闭弹框
const handleClose = () => {
  isVisible.value = false
  emitter.emit(`modal-closed-${props.element.id}`)
}

// 取消操作 - 重置关联表单并关闭弹框
const handleCancel = () => {
  if (linkedComponent.value && linkedComponent.value.component === 'FormControl') {
    // 触发关联表单的重置事件
    emitter.emit(`form-reset-${linkedComponent.value.id}`)
  }
  handleClose()
}

// 确认操作 - 触发关联表单提交
const handleConfirm = () => {
  if (linkedComponent.value && linkedComponent.value.component === 'FormControl') {
    // 触发关联表单的提交事件
    emitter.emit(`form-submit-${linkedComponent.value.id}`)
  } else {
    // 如果没有关联表单，直接关闭弹框
    emitter.emit(`modal-confirmed-${props.element.id}`)
    handleClose()
  }
}

// 监听打开弹框事件
const handleOpenModal = () => {
  isVisible.value = true
  emitter.emit(`modal-opened-${props.element.id}`)
}

onMounted(() => {
  // 监听打开弹框的事件 - 无论组件是否隐藏都要注册事件监听
  const eventName = `open-modal-${props.element.id}`
  emitter.on(eventName, handleOpenModal)

  // 监听关联表单的提交成功事件
  if (linkedComponent.value && linkedComponent.value.component === 'FormControl') {
    emitter.on(`form-submit-success-${linkedComponent.value.id}`, handleFormSubmitSuccess)
  }
})

// 处理表单提交成功
const handleFormSubmitSuccess = () => {
  handleClose()
}

onBeforeUnmount(() => {
  emitter.off(`open-modal-${props.element.id}`, handleOpenModal)

  // 清理表单事件监听
  if (linkedComponent.value && linkedComponent.value.component === 'FormControl') {
    emitter.off(`form-submit-success-${linkedComponent.value.id}`, handleFormSubmitSuccess)
  }
})
</script>

<style lang="less" scoped>
/* 编辑模式样式 */
.modal-dialog-wrapper.edit-mode {
  position: relative;
  width: 100%;
  height: 100%;
  border: 2px dashed #409eff;
  border-radius: 8px;
  overflow: hidden;

  .modal-dialog.edit-mode {
    position: relative;
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 6px;
    display: flex;
    flex-direction: column;

    .modal-header {
      background: #f0f9ff;
      border-bottom: 1px solid #e4e7ed;
    }
  }

  /* 隐藏状态占位符 */
  &.hidden-placeholder {
    border: 2px dashed #ccc;
    background: #f9f9f9;

    .hidden-indicator {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #999;
      font-size: 14px;

      .el-icon {
        font-size: 24px;
        margin-bottom: 8px;
      }

      span {
        font-weight: 500;
        margin-bottom: 4px;
      }

      p {
        font-size: 12px;
        margin: 0;
        opacity: 0.8;
      }
    }
  }
}

/* 预览模式容器 */
.modal-runtime-container {
  position: absolute;
  width: 0;
  height: 0;
  overflow: hidden;
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
}

/* 预览模式隐藏状态 */
.hidden-runtime-container {
  /* 继承上面的样式，确保完全隐藏 */
}

/* 运行时模式样式 */
.modal-dialog-wrapper.runtime-mode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  pointer-events: auto;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.modal-dialog.runtime-mode {
  position: absolute;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
  
  .modal-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
  
  .modal-close {
    cursor: pointer;
    color: #909399;
    font-size: 16px;
    
    &:hover {
      color: #409eff;
    }
  }
}

.modal-body {
  flex: 1;
  padding: 20px;
  overflow: auto;
  
  .linked-component-container {
    width: 100%;
    height: 100%;
  }
  
  .default-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #909399;
    font-size: 14px;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #e4e7ed;
  background: #f8f9fa;
}
</style>
