<template>
  <div class="form-query-container" :style="containerStyle">
    <!-- 表单标题 -->
    <div v-if="config.showTitle && config.title" class="form-query-title" :style="titleStyle">
      {{ config.title }}
    </div>
    
    <!-- 表单区域 -->
    <el-form
      ref="formRef"
      :model="formData"
      :label-position="config.layout.labelPosition"
      :label-width="config.layout.labelWidth"
      :size="config.layout.size"
      class="form-query-form"
      :class="{ 'form-with-border': config.layout.showBorder }"
    >
      <div
        class="form-fields-grid"
        :style="gridStyle"
      >
        <!-- 表单字段 -->
        <el-form-item
          v-for="field in visibleFields"
          :key="field.id"
          :label="field.label"
          :prop="field.name"
          class="form-field-item"
        >
          <!-- 文本输入框 -->
          <el-input
            v-if="field.type === 'text'"
            v-model="formData[field.name]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :readonly="field.readonly"
            clearable
            @change="handleFieldChange(field.name)"
          />
          
          <!-- 数字输入框 -->
          <el-input-number
            v-else-if="field.type === 'number'"
            v-model="formData[field.name]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :readonly="field.readonly"
            :min="field.validation?.min"
            :max="field.validation?.max"
            style="width: 100%"
            @change="handleFieldChange(field.name)"
          />
          
          <!-- 下拉选择 -->
          <el-select
            v-else-if="field.type === 'select'"
            v-model="formData[field.name]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            clearable
            style="width: 100%"
            @change="handleFieldChange(field.name)"
          >
            <el-option
              v-for="option in field.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          
          <!-- 日期选择器 -->
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="formData[field.name]"
            type="date"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :readonly="field.readonly"
            style="width: 100%"
            @change="handleFieldChange(field.name)"
          />
          
          <!-- 日期范围选择器 -->
          <el-date-picker
            v-else-if="field.type === 'daterange'"
            v-model="formData[field.name]"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :disabled="field.disabled"
            :readonly="field.readonly"
            style="width: 100%"
            @change="handleFieldChange(field.name)"
          />
        </el-form-item>

        <!-- 内联按钮：当配置为inline时，在网格中添加按钮区域 -->
        <div
          v-if="config.layout.actionsPosition === 'inline'"
          class="form-inline-actions"
          :style="inlineActionsStyle"
        >
          <el-button
            v-if="config.showResetButton"
            @click="resetForm"
            :size="config.layout.size"
          >
            {{ config.resetButtonText || '重置' }}
          </el-button>

          <el-button
            v-if="config.showQueryButton"
            type="primary"
            @click="handleQuery"
            :loading="querying"
            :size="config.layout.size"
          >
            {{ config.queryButtonText || '查询' }}
          </el-button>

          <el-button
            v-if="config.autoQuery && config.showClearButton"
            @click="clearAndQuery"
            :size="config.layout.size"
          >
            {{ config.clearButtonText || '清空' }}
          </el-button>
        </div>
      </div>
      
      <!-- 新行按钮区域：当配置为newline时显示 -->
      <div
        v-if="config.layout.actionsPosition === 'newline'"
        class="form-query-actions"
        :style="actionsStyle"
      >
        <el-button
          v-if="config.showResetButton"
          @click="resetForm"
          :size="config.layout.size"
        >
          {{ config.resetButtonText || '重置' }}
        </el-button>
        
        <el-button
          v-if="config.showQueryButton"
          type="primary"
          @click="handleQuery"
          :loading="querying"
          :size="config.layout.size"
        >
          {{ config.queryButtonText || '查询' }}
        </el-button>
        
        <el-button
          v-if="config.autoQuery && config.showClearButton"
          @click="clearAndQuery"
          :size="config.layout.size"
        >
          {{ config.clearButtonText || '清空' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus-secondary'
import { useEmitt } from '@/hooks/web/useEmitt'

import type { FormQueryConfig } from './types'
import { DEFAULT_FORM_QUERY_CONFIG } from './types'

interface Props {
  element: any
  themes?: string
}

const props = withDefaults(defineProps<Props>(), {
  themes: 'light'
})

const emit = defineEmits(['input'])

// 获取组件配置
const config = computed<FormQueryConfig>(() => {
  const elementConfig = props.element?.propValue

  // 深度合并配置，确保所有必要属性都存在
  const finalConfig: FormQueryConfig = {
    ...DEFAULT_FORM_QUERY_CONFIG,
    ...elementConfig,
    layout: {
      ...DEFAULT_FORM_QUERY_CONFIG.layout,
      ...elementConfig?.layout
    },
    style: {
      ...DEFAULT_FORM_QUERY_CONFIG.style,
      ...elementConfig?.style
    },
    titleStyle: {
      ...DEFAULT_FORM_QUERY_CONFIG.titleStyle,
      ...elementConfig?.titleStyle
    }
  }

  // 调试配置信息
  console.log('表单查询组件配置:', {
    hasElementConfig: !!elementConfig,
    showQueryButton: finalConfig.showQueryButton,
    showResetButton: finalConfig.showResetButton,
    actionsPosition: finalConfig.layout.actionsPosition,
    finalConfig: finalConfig
  })

  return finalConfig
})

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive<Record<string, any>>({})

// 查询状态
const querying = ref(false)

// 事件总线
const { emitter } = useEmitt()

// 可见字段
const visibleFields = computed(() => {
  return config.value.fields
    .filter(field => field.visible !== false)
    .sort((a, b) => (a.order || 0) - (b.order || 0))
})

// 容器样式
const containerStyle = computed(() => {
  const style = config.value.style || {}
  return {
    backgroundColor: style.backgroundColor || 'transparent',
    padding: style.padding || '16px',
    borderRadius: style.borderRadius || '4px',
    border: style.border || 'none'
  }
})

// 标题样式
const titleStyle = computed(() => {
  const titleStyle = config.value.titleStyle || {}
  return {
    fontSize: titleStyle.fontSize || '16px',
    fontWeight: titleStyle.fontWeight || 'bold',
    color: titleStyle.color || '#333',
    marginBottom: titleStyle.marginBottom || '16px',
    textAlign: titleStyle.textAlign || 'left'
  }
})

// 网格样式
const gridStyle = computed(() => {
  const layout = config.value.layout || {} as any
  const isInline = layout.actionsPosition === 'inline'

  // 如果是内联模式，需要为按钮预留一个网格位置
  const totalColumns = isInline ? (layout.columns || 2) + 1 : (layout.columns || 2)

  return {
    gridTemplateColumns: `repeat(${totalColumns}, 1fr)`,
    gap: `${layout.spacing || 16}px`
  }
})

// 操作按钮样式（新行模式）
const actionsStyle = computed(() => {
  const layout = config.value.layout || {} as any
  return {
    marginTop: '16px',
    textAlign: layout.actionsAlign || 'left',
    display: 'flex',
    gap: '8px',
    justifyContent: layout.actionsAlign === 'center' ? 'center' :
                   layout.actionsAlign === 'right' ? 'flex-end' : 'flex-start'
  }
})

// 内联按钮样式
const inlineActionsStyle = computed(() => {
  const layout = config.value.layout || {} as any
  return {
    display: 'flex',
    gap: '8px',
    alignItems: 'flex-end', // 与表单字段底部对齐
    justifyContent: layout.actionsAlign === 'center' ? 'center' :
                   layout.actionsAlign === 'right' ? 'flex-end' : 'flex-start',
    paddingBottom: layout.labelPosition === 'top' ? '0' : '4px' // 根据标签位置调整
  }
})

// 初始化表单数据
const initFormData = () => {
  config.value.fields.forEach(field => {
    if (field.defaultValue !== undefined) {
      formData[field.name] = field.defaultValue
    } else {
      // 根据字段类型设置默认值
      switch (field.type) {
        case 'number':
          formData[field.name] = undefined
          break
        case 'select':
          formData[field.name] = ''
          break
        case 'date':
          formData[field.name] = null
          break
        case 'daterange':
          formData[field.name] = []
          break
        default:
          formData[field.name] = ''
      }
    }
  })
}

// 处理字段变化
const handleFieldChange = () => {
  emit('input', props.element, config.value)
  
  // 如果启用了自动查询，在字段变化时触发查询
  if (config.value.autoQuery) {
    // 防抖处理，避免频繁查询
    clearTimeout(autoQueryTimer)
    autoQueryTimer = setTimeout(() => {
      handleQuery()
    }, config.value.autoQueryDelay || 500)
  }
}

let autoQueryTimer: any = null

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  initFormData()
  
  // 如果启用了重置后自动查询
  if (config.value.resetAndQuery) {
    nextTick(() => {
      handleQuery()
    })
  }
}

// 清空并查询
const clearAndQuery = () => {
  resetForm()
  nextTick(() => {
    handleQuery()
  })
}

// 构建查询参数
const buildQueryParams = () => {
  const params: Array<{ key: string; value: string }> = []
  
  config.value.fields.forEach(field => {
    const value = formData[field.name]
    
    if (value !== undefined && value !== null && value !== '') {
      // 处理不同类型的值
      if (field.type === 'daterange' && Array.isArray(value) && value.length === 2) {
        // 日期范围处理
        const startParamName = field.paramMapping?.startParam || `${field.name}_start`
        const endParamName = field.paramMapping?.endParam || `${field.name}_end`
        
        params.push({ key: startParamName, value: value[0] })
        params.push({ key: endParamName, value: value[1] })
      } else {
        // 普通字段处理
        const paramName = field.paramMapping?.paramName || field.name
        params.push({ key: paramName, value: String(value) })
      }
    }
  })
  
  return params
}

// 处理查询
const handleQuery = async () => {
  if (querying.value) return
  
  try {
    querying.value = true
    
    // 构建查询参数
    const queryParams = buildQueryParams()
    
    console.log('表单查询组件触发查询:', {
      componentId: props.element.id,
      queryParams,
      targetComponents: config.value.targetComponents,
      formData: formData
    })
    
    // 更新目标组件的REST配置参数
    if (config.value.targetComponents && config.value.targetComponents.length > 0) {
      config.value.targetComponents.forEach(targetId => {
        // 通过事件通知目标组件更新查询参数
        emitter.emit(`update-query-params-${targetId}`, queryParams)

        // 触发目标组件重新查询数据
        setTimeout(() => {
          console.log(`表单查询组件触发目标组件 ${targetId} 重新查询数据`)
          emitter.emit(`query-data-${targetId}`)
        }, 200) // 增加延迟确保参数更新完成
      })
    }
    
    ElMessage.success('查询已触发')

  } catch (error) {
    console.error('查询失败:', error)
    if (error.message && error.message.includes('REST字段配置不完整')) {
      ElMessage.error('目标表格组件缺少REST字段配置，请先在图表编辑器中配置REST字段映射')
    } else {
      ElMessage.error('查询失败: ' + error.message)
    }
  } finally {
    querying.value = false
  }
}

// 监听配置变化
watch(() => config.value, () => {
  initFormData()
}, { deep: true })

// 组件挂载时初始化
onMounted(() => {
  initFormData()
  
  // 如果启用了初始查询
  if (config.value.initialQuery) {
    nextTick(() => {
      handleQuery()
    })
  }
})
</script>

<style scoped>
.form-query-container {
  width: 100%;
  height: 100%;
}

.form-query-title {
  margin-bottom: 16px;
}

.form-query-form {
  width: 100%;
}

.form-fields-grid {
  display: grid;
  width: 100%;
}

.form-field-item {
  margin-bottom: 0;
}

.form-with-border {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
}

.form-query-actions {
  margin-top: 16px;
}

.form-inline-actions {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  min-height: 32px; /* 确保与表单字段高度一致 */
}

/* 当标签在顶部时，内联按钮需要额外的顶部间距 */
.form-query-form[data-label-position="top"] .form-inline-actions {
  margin-top: 24px; /* 标签高度 + 间距 */
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: normal;
}
</style>
