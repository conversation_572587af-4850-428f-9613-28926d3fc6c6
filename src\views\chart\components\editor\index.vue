<script lang="ts" setup>
import dvInfoSvg from '@/assets/svg/dv-info.svg'
import icon_down_outlined1 from '@/assets/svg/icon_down_outlined-1.svg'
import icon_deleteTrash_outlined from '@/assets/svg/icon_delete-trash_outlined.svg'
import icon_info_outlined from '@/assets/svg/icon_info_outlined.svg'
import iconFilter from '@/assets/svg/icon-filter.svg'
import icon_edit_outlined from '@/assets/svg/icon_edit_outlined.svg'
import icon_refresh_outlined from '@/assets/svg/icon_refresh_outlined.svg'
import icon_add_outlined from '@/assets/svg/icon_add_outlined.svg'
import icon_searchOutline_outlined from '@/assets/svg/icon_search-outline_outlined.svg'
import icon_copy_outlined from '@/assets/svg/icon_copy_outlined.svg'
import {
  PropType,
  reactive,
  ref,
  watch,
  toRefs,
  computed,
  nextTick,
  onBeforeMount,
  provide,
  unref,
  onBeforeUnmount,
  onMounted
} from 'vue'
import Icon from '@/components/icon-custom/src/Icon.vue'
import type { FormInstance, FormRules } from 'element-plus-secondary'
import { useAppStoreWithOut } from '@/store/modules/app'
import { useI18n } from '@/hooks/web/useI18n'
import { Tree } from '../../../visualized/data/dataset/form/CreatDsGroup.vue'
import { useEmitt } from '@/hooks/web/useEmitt'
import { ElMessage, ElTreeSelect } from 'element-plus-secondary'
import draggable from 'vuedraggable'
import DimensionItem from './drag-item/DimensionItem.vue'
import { fieldType } from '@/utils/attr'
import QuotaItem from '@/views/chart/components/editor/drag-item/QuotaItem.vue'
import DragPlaceholder from '@/views/chart/components/editor/drag-item/DragPlaceholder.vue'
import FilterTree from './filter/FilterTree.vue'
import ChartStyle from '@/views/chart/components/editor/editor-style/ChartStyle.vue'
import VQueryChartStyle from '@/views/chart/components/editor/editor-style/VQueryChartStyle.vue'
import Senior from '@/views/chart/components/editor/editor-senior/Senior.vue'
import QuotaFilterEditor from '@/views/chart/components/editor/filter/QuotaFilterEditor.vue'
import ResultFilterEditor from '@/views/chart/components/editor/filter/ResultFilterEditor.vue'
import { ElIcon } from 'element-plus-secondary'
import DrillItem from '@/views/chart/components/editor/drag-item/DrillItem.vue'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import { storeToRefs } from 'pinia'
import { BASE_VIEW_CONFIG, getViewConfig } from '@/views/chart/components/editor/util/chart'
import ChartType from '@/views/chart/components/editor/chart-type/ChartType.vue'
import { useRouter, useRoute } from 'vue-router'
import CompareEdit from '@/views/chart/components/editor/drag-item/components/CompareEdit.vue'
import ValueFormatterEdit from '@/views/chart/components/editor/drag-item/components/ValueFormatterEdit.vue'
import CustomSortEdit from '@/views/chart/components/editor/drag-item/components/CustomSortEdit.vue'
import SortPriorityEdit from '@/views/chart/components/editor/drag-item/components/SortPriorityEdit.vue'
import { snapshotStoreWithOut } from '@/store/modules/data-visualization/snapshot'
import CalcFieldEdit from '@/views/visualized/data/dataset/form/CalcFieldEdit.vue'
import { getFieldName, guid } from '@/views/visualized/data/dataset/form/util'
import { cloneDeep, forEach, get, debounce, set, concat, keys } from 'lodash-es'
import { deleteField, saveField } from '@/api/dataset'
import { getWorldTree, listCustomGeoArea } from '@/api/map'
import chartViewManager from '@/views/chart/components/js/panel'
import DatasetSelect from '@/views/chart/components/editor/dataset-select/DatasetSelect.vue'
import RestDataSource from '@/views/chart/components/editor/rest-data-source/RestDataSource.vue'
import { useDraggable } from '@vueuse/core'
import { callRestApi, preprocessRestData, type RestConfig } from '@/utils/restApi'
import { PluginComponent } from '@/components/plugin'
import { Field, getFieldByDQ, copyChartField, deleteChartField } from '@/api/chart'
import ChartTemplateInfo from '@/views/chart/components/editor/common/ChartTemplateInfo.vue'
import { XpackComponent } from '@/components/plugin'
import { useEmbedded } from '@/store/modules/embedded'
import { iconChartMap } from '@/components/icon-group/chart-list'
import { iconFieldMap } from '@/components/icon-group/field-list'
import {
  iconFieldCalculatedMap,
  iconFieldCalculatedQMap
} from '@/components/icon-group/field-calculated-list'
import { useCache } from '@/hooks/web/useCache'
import { canvasSave } from '@/utils/canvasUtils'

const { wsCache } = useCache('localStorage')
const embeddedStore = useEmbedded()
const snapshotStore = snapshotStoreWithOut()
const dvMainStore = dvMainStoreWithOut()
const {
  canvasCollapse,
  curComponent,
  componentData,
  editMode,
  mobileInPc,
  fullscreenFlag,
  dvInfo
} = storeToRefs(dvMainStore)
const router = useRouter()
let componentNameEdit = ref(false)
let inputComponentName = ref({ id: null, name: null })
let componentNameInput = ref(null)

const { t } = useI18n()
const loading = ref(false)
const tabActive = ref('data')
const datasetSelector = ref(null)
const curDatasetWeight = ref(0)
const renameForm = ref<FormInstance>()
// REST配置组件的引用
const restDataSourceRef = ref()
const { emitter } = useEmitt({
  name: 'set-table-column-width',
  callback: args => onTableColumnWidthChange(args)
})
useEmitt({
  name: 'set-page-size',
  callback: args => onTablePageSizeChange(args)
})
const props = defineProps({
  view: {
    type: Object as PropType<ChartObj>,
    required: false,
    default() {
      return { ...BASE_VIEW_CONFIG }
    }
  },
  datasetTree: {
    type: Array as PropType<Tree[]>,
    default: () => []
  },
  themes: {
    type: String as PropType<EditorTheme>,
    default: 'dark'
  }
})

const editCalcField = ref(false)
const isCalcFieldAdd = ref(true)
const calcEdit = ref()
const route = useRoute()

const onComponentNameChange = () => {
  snapshotStore.recordSnapshotCache('onComponentNameChange')
}

const closeEditComponentName = () => {
  componentNameEdit.value = false
  if (curComponent.value.id !== inputComponentName.value.id) {
    return
  }
  if (!inputComponentName.value.name || !inputComponentName.value.name.trim()) {
    return
  }
  if (inputComponentName.value.name.trim() === view.value.title) {
    return
  }
  if (
    inputComponentName.value.name.trim().length > 64 ||
    inputComponentName.value.name.trim().length < 2
  ) {
    ElMessage.warning('名称字段长度2-64个字符')
    editComponentName()
    return
  }
  view.value.title = inputComponentName.value.name
  if (view.value.type === 'VQuery') {
    view.value.customStyle.component.title = inputComponentName.value.name
  }
  if (curComponent.value) {
    curComponent.value.label = inputComponentName.value.name
    curComponent.value.name = inputComponentName.value.name
  }
  inputComponentName.value.name = ''
  inputComponentName.value.id = ''
}

const editComponentName = () => {
  componentNameEdit.value = true
  inputComponentName.value.name = view.value.title
  inputComponentName.value.id = view.value.id
  nextTick(() => {
    componentNameInput.value.focus()
  })
}
const toolTip = computed(() => {
  return props.themes || 'dark'
})

const templateStatusShow = computed(() => {
  return (
    view.value['dataFrom'] === 'template' &&
    view.value.type !== 'picture-group' &&
    !mobileInPc.value
  )
})

const { view } = toRefs(props)

let cacheId = ''

const clearRemove = items => {
  if (items) {
    items.forEach(item => removeItems(item))
  }
}

onBeforeMount(() => {
  cacheId = route.query.id as unknown as string
})

onBeforeUnmount(() => {
  cacheId = ''
})

onMounted(() => {
  useEmitt({
    name: 'clear-remove',
    callback: clearRemove
  })

  // 初始化数据源类型
  initializeRestConfig()
})

// 初始化REST配置的函数
const initializeRestConfig = () => {
  console.log('初始化REST配置，组件ID:', view.value.id, '数据源类型:', view.value.datasourceType)

  if (view.value.datasourceType === 'rest') {
    state.datasourceType = 'rest'
    // 不要自动打开配置对话框，只设置数据源类型
    if (view.value.restConfig) {
      // 深拷贝配置，避免引用问题
      state.restConfig = JSON.parse(JSON.stringify(view.value.restConfig))
      console.log('加载组件REST配置:', state.restConfig)
    } else {
      // 如果没有配置，初始化为默认空配置
      state.restConfig = {
        url: '',
        method: 'GET',
        headers: [{ key: 'Content-Type', value: 'application/json' }],
        params: [],
        body: '',
        timeout: 30,
        dataPath: '',
        pagination: {
          enabled: false,
          pageParam: 'page',
          sizeParam: 'size',
          pageSize: 100
        }
      }
      console.log('初始化默认REST配置')
    }
    if (view.value.restFields) {
      state.restFields = JSON.parse(JSON.stringify(view.value.restFields))
      onRestFieldsChange(view.value.restFields)
      console.log('加载组件REST字段:', state.restFields.length, '个字段')
    } else {
      // 如果没有字段配置，清空字段
      state.restFields = []
      state.dimension = []
      state.quota = []
      state.dimensionData = []
      state.quotaData = []
      console.log('清空REST字段配置')
    }
  } else {
    // 如果组件没有明确的数据源类型，检查是否有REST配置
    if (view.value.restConfig || view.value.restFields) {
      // 如果有REST配置，说明这个组件之前配置过REST数据源
      state.datasourceType = 'rest'
      if (view.value.restConfig) {
        state.restConfig = JSON.parse(JSON.stringify(view.value.restConfig))
        console.log('恢复组件的REST配置:', state.restConfig)
      }
      if (view.value.restFields) {
        state.restFields = JSON.parse(JSON.stringify(view.value.restFields))
        onRestFieldsChange(view.value.restFields)
        console.log('恢复组件的REST字段:', state.restFields.length, '个字段')
      }
    } else if (view.value.datasourceType === undefined && state.datasourceType === 'rest') {
      console.log('组件数据源类型未定义，但当前是REST模式，保持REST状态')
      // 保持REST状态，但清空配置（因为组件确实没有配置）
      state.restConfig = null
      state.restFields = []
      state.dimension = []
      state.quota = []
      state.dimensionData = []
      state.quotaData = []
    } else {
      // 默认使用REST数据源而不是数据集
      state.datasourceType = 'rest'
      state.showRestConfig = false
      state.restConfig = null
      state.restFields = []
      console.log('默认使用REST数据源')
    }
  }
}

// 监听view变化，当切换组件时重新初始化REST配置
watch(
  () => view.value.id,
  (newId, oldId) => {
    if (newId !== oldId && newId) {
      console.log('切换到组件:', newId, '，重新初始化REST配置')
      console.log('组件完整信息:', {
        id: view.value.id,
        datasourceType: view.value.datasourceType,
        hasRestConfig: !!view.value.restConfig,
        hasRestFields: !!view.value.restFields,
        restConfigUrl: view.value.restConfig?.url,
        restFieldsCount: view.value.restFields?.length || 0,
        tableId: view.value.tableId
      })
      initializeRestConfig()
    }
  },
  { immediate: false }
)

const appStore = useAppStoreWithOut()
const isDataEaseBi = computed(() => appStore.getIsDataEaseBi || appStore.getIsIframe)
const itemFormRules = reactive<FormRules>({
  chartShowName: [
    { required: true, message: t('commons.input_content'), trigger: 'change' },
    { max: 200, message: t('commons.char_count_limit', { count: 200 }), trigger: 'change' }
  ]
})

const state = reactive({
  extData: '',
  moveId: -1,
  dimension: [],
  quota: [],
  dimensionData: [],
  quotaData: [],
  renameItem: false,
  itemForm: {
    name: '',
    chartShowName: '',
    index: 0,
    renameType: ''
  },
  quotaFilterEdit: false,
  quotaItem: {},
  resultFilterEdit: false,
  filterItem: {},
  chartForFilter: {},
  searchField: '',
  quotaItemCompare: {},
  showEditQuotaCompare: false,
  showValueFormatter: false,
  valueFormatterItem: {},
  showCustomSort: false,
  showSortPriority: false,
  sortPriority: [],
  customSortList: [],
  customSortField: {},
  currEditField: {},
  worldTree: [],
  areaId: '',
  chartTypeOptions: [],
  useless: null,
  // REST数据源相关状态
  datasourceType: 'rest', // 'dataset', 'rest' - 默认使用REST数据源
  restConfig: null,
  restFields: [],
  showRestConfig: false
})

const filedList = computed(() => {
  return [...state.dimension, ...state.quota].filter(ele => ele.id !== 'count' && !!ele.summary)
})

provide('filedList', () => filedList.value)
provide('quota', () => state.quota)
watch(
  [() => view.value['tableId']],
  () => {
    nextTick(() => {
      if ('picture-group' === props.view.type) {
        return
      }
      getFields(props.view.tableId, props.view.id, props.view.type)
      const nodeId = view.value['tableId']
      if (!!nodeId) {
        cacheId = nodeId as unknown as string
      }
      const node = datasetSelector?.value?.getNode(nodeId)
      if (node?.data) {
        curDatasetWeight.value = node.data.weight
      }
    })
  },
  { deep: true, immediate: true }
)
const getFields = (id, chartId, type) => {
  // 如果是REST数据源，使用已配置的REST字段，不调用后端API
  if (state.datasourceType === 'rest' || view.value.datasourceType === 'rest') {
    fieldLoading.value = true

    // 使用已配置的REST字段
    const restFields = state.restFields || view.value.restFields || []

    if (restFields.length > 0) {
      // 重新生成维度和指标字段
      onRestFieldsChange(restFields)
    } else {
      // 如果没有REST字段配置，清空字段列表
      state.dimension = []
      state.quota = []
      state.dimensionData = []
      state.quotaData = []
    }

    fieldLoading.value = false
    emitter.emit('dataset-change')
    return
  }

  // 传统数据集数据源的处理逻辑
  if (id && chartId) {
    fieldLoading.value = true
    getFieldByDQ(id, chartId, { type: type })
      .then(res => {
        state.dimension = (res.dimensionList as unknown as Field[]) || []
        state.quota = (res.quotaList as unknown as Field[]) || []
        state.dimensionData = JSON.parse(JSON.stringify(state.dimension))
        state.quotaData = JSON.parse(JSON.stringify(state.quota))

        fieldLoading.value = false
        emitter.emit('dataset-change')
      })
      .catch(() => {
        state.dimension = []
        state.quota = []
        state.dimensionData = []
        state.quotaData = []

        fieldLoading.value = false
      })
  } else {
    state.dimension = []
    state.quota = []
    state.dimensionData = []
    state.quotaData = []

    fieldLoading.value = false
  }
}

const chartStyleShow = computed(() => {
  return (
    !['richText', 'Picture'].includes(view.value.type) &&
    curComponent.value &&
    curComponent.value.component === 'UserView'
  )
})

const chartViewInstance = computed(() => {
  // 对于UserView组件，使用innerType来获取图表视图
  if (curComponent.value?.component === 'UserView' && curComponent.value?.innerType) {
    const innerType = curComponent.value.innerType
    // 嵌套菜单组件使用特殊的render和type
    if (innerType === 'nested-menu') {
      return chartViewManager.getChartView('custom', 'nested-menu')
    }
    if (innerType === 'button') {
      return chartViewManager.getChartView('custom', 'button')
    }
    if (innerType === 'rich-text') {
      return chartViewManager.getChartView('custom', 'rich-text')
    }
    // 其他UserView组件可以在这里添加
  }

  return chartViewManager.getChartView(view.value.render, view.value.type)
})
const showAxis = (axis: AxisType) => chartViewInstance.value?.axis?.includes(axis)
const areaSelect = ref()
const expandKeys = ref([])
watch(
  () => [view.value.type, view.value],
  newVal => {
    if (showAxis('area')) {
      if (!state.worldTree?.length) {
        getWorldTree().then(async res => {
          const customAreaList = (await listCustomGeoArea()).data
          const customRoot = {
            id: 'customRoot',
            name: '自定义区域',
            disabled: true
          }
          if (customAreaList.length) {
            customRoot.children = customAreaList
          }
          state.worldTree.splice(0, state.worldTree.length, res.data, customRoot)
          state.areaId = view.value?.customAttr?.map?.id
        })
      } else {
        state.areaId = view.value?.customAttr?.map?.id
      }
      areaSelect.value?.blur()
      expandKeys.value.splice(0)
      areaSelect.value?.setCurrentKey(state.areaId)
    }
    state.chartTypeOptions.splice(0, state.chartTypeOptions.length, getViewConfig(newVal[0]))
    state.useless = newVal[0]
  },
  { immediate: true, deep: false }
)
const treeProps = {
  label: 'name',
  children: 'children',
  disabled: 'disabled'
}

const recordSnapshotInfo = type => {
  view.value['dataFrom'] = 'calc'
  snapshotStore.recordSnapshotCache(type, view.value.id)
}

const changeDataset = () => {
  // change dataset, do clear field or other thing
  view.value['calParams'] = []
  recordSnapshotInfo('calcData')
}

// REST数据源相关函数
const toggleDataSourceType = (type: 'dataset' | 'rest') => {
  state.datasourceType = type
  if (type === 'rest') {
    state.showRestConfig = true
    // 清空数据集相关数据
    view.value.tableId = null
    state.dimension = []
    state.quota = []
    state.dimensionData = []
    state.quotaData = []

    // 如果已有REST配置，恢复它们
    if (view.value.restConfig) {
      state.restConfig = view.value.restConfig
    }
    if (view.value.restFields && view.value.restFields.length > 0) {
      state.restFields = view.value.restFields
      onRestFieldsChange(view.value.restFields)
    }
  } else {
    state.showRestConfig = false
    // 清空REST相关数据
    state.restConfig = null
    state.restFields = []
    // 清空图表中的REST字段配置
    view.value.restConfig = null
    view.value.restFields = []
    view.value.datasourceType = 'dataset'
  }
  recordSnapshotInfo('calcData')
}

const onRestConfigChange = config => {
  // 深拷贝配置，避免引用问题
  state.restConfig = JSON.parse(JSON.stringify(config))
  view.value.restConfig = JSON.parse(JSON.stringify(config))
  recordSnapshotInfo('calcData')
}

const onRestFieldsChange = fields => {
  // 深拷贝字段配置，避免引用问题
  state.restFields = JSON.parse(JSON.stringify(fields))
  // 将REST字段转换为维度和指标，支持自定义配置
  state.dimension = fields
    .filter(f => f.groupType === 'd' && f.enabled !== false)
    .map(f => ({
      id: f.id || f.name,
      name: f.customName || f.name,
      dataeaseName: f.customName || f.name,
      originName: f.originalName || f.name,
      groupType: 'd',
      deType: f.deType || getDeTypeFromRestType(f.type),
      extField: 0,
      checked: true,
      sort: f.sort || 'none',
      restField: true, // 标记为REST字段
      restPath: f.restPath || f.path, // 保存JSON路径
      restType: f.type
    }))

  state.quota = fields
    .filter(f => f.groupType === 'q' && f.enabled !== false)
    .map(f => ({
      id: f.id || f.name,
      name: f.customName || f.name,
      dataeaseName: f.customName || f.name,
      originName: f.originalName || f.name,
      groupType: 'q',
      deType: f.deType || getDeTypeFromRestType(f.type),
      extField: 0,
      checked: true,
      summary: f.summary || 'sum', // 聚合方式
      sort: f.sort || 'none',
      restField: true, // 标记为REST字段
      restPath: f.restPath || f.path, // 保存JSON路径
      restType: f.type
    }))

  state.dimensionData = JSON.parse(JSON.stringify(state.dimension))
  state.quotaData = JSON.parse(JSON.stringify(state.quota))

  // 保存REST字段配置到view
  view.value.restFields = fields

  emitter.emit('dataset-change')
}

// 将REST字段类型转换为DataEase字段类型
const getDeTypeFromRestType = (restType: string): number => {
  switch (restType) {
    case 'string':
      return 0 // 文本
    case 'date':
      return 1 // 时间
    case 'number':
      return 2 // 整型数值
    case 'boolean':
      return 4 // 布尔
    default:
      return 0
  }
}

// 获取REST数据 - 使用统一的REST API工具
const getRestData = async (restConfig: RestConfig) => {
  return await callRestApi(restConfig, {
    enableMockData: true,
    logPrefix: '编辑器'
  })
}

// 转换REST数据为图表数据格式
const transformRestDataToChartData = (restData, restFields, view) => {
  if (!restData || !restFields) {
    return { tableRow: [] }
  }

  console.log('转换REST数据:', {
    restDataKeys: restData ? Object.keys(restData) : [],
    fieldsCount: restFields.length,
    dataPath: view.restConfig?.dataPath,
    originalRestData: restData
  })

  // 使用统一的数据预处理工具
  const targetData = preprocessRestData(restData, view.restConfig || state.restConfig)
  console.log('预处理后的目标数据:', targetData)

  // 获取用户配置的字段 - 优先使用用户在数据面板中配置的字段
  let enabledFields = []

  // 检查用户是否在数据面板中配置了字段
  const userConfiguredFields = [
    ...(view.xAxis || []),
    ...(view.yAxis || []),
    ...(view.xAxisExt || []),
    ...(view.yAxisExt || [])
  ]

  console.log('字段配置检查:', {
    userConfiguredCount: userConfiguredFields.length,
    userFields: userConfiguredFields.map(f => ({
      id: f.id,
      name: f.name,
      dataeaseName: f.dataeaseName
    })),
    restFieldsCount: restFields.length,
    restFields: restFields.map(f => ({ id: f.id, name: f.name, originalName: f.originalName }))
  })

  if (userConfiguredFields.length > 0) {
    // 如果用户配置了字段，只使用用户配置的字段
    console.log('使用用户配置的字段')
    enabledFields = userConfiguredFields.map(userField => {
      // 在REST字段中找到对应的字段配置
      const matchingRestField = restFields.find(
        restField =>
          restField.name === userField.name ||
          restField.originalName === userField.name ||
          restField.id === userField.id ||
          restField.name === userField.dataeaseName
      )

      if (matchingRestField) {
        // 合并用户配置和REST字段信息
        let mergedField = {
          ...matchingRestField,
          ...userField,
          enabled: true,
          restField: true,
          // 确保有正确的路径信息
          restPath: matchingRestField.path || matchingRestField.name,
          path: matchingRestField.path || matchingRestField.name
        }

        // 强制修正已知的字段类型错误
        if (mergedField.name === 'name' && mergedField.type === 'number') {
          console.log('transformRestDataToChartData: 检测到name字段类型错误，强制修正为string类型')
          mergedField.type = 'string'
        }
        console.log(`字段映射成功: ${userField.name}`, {
          userField: {
            id: userField.id,
            name: userField.name,
            dataeaseName: userField.dataeaseName
          },
          matchingRestField: {
            id: matchingRestField.id,
            name: matchingRestField.name,
            path: matchingRestField.path
          },
          mergedField: {
            id: mergedField.id,
            name: mergedField.name,
            restPath: mergedField.restPath,
            path: mergedField.path
          }
        })
        return mergedField
      } else {
        // 如果没找到匹配的REST字段，保持用户配置
        console.warn(`未找到匹配的REST字段: ${userField.name}`)
        return {
          ...userField,
          enabled: true,
          restField: false,
          restPath: userField.name,
          path: userField.name // 使用字段名作为路径
        }
      }
    })
  } else {
    // 如果用户没有配置字段，使用所有启用的REST字段
    console.log('用户未配置字段，使用所有REST字段')
    enabledFields = restFields.filter(field => field.enabled !== false)
  }

  // 转换数据行 - 按字段顺序排列数据
  console.log('开始转换数据行:', {
    targetDataCount: targetData.length,
    enabledFieldsCount: enabledFields.length,
    enabledFields: enabledFields.map(f => ({ name: f.name, restPath: f.restPath, path: f.path })),
    sampleDataItem: targetData[0]
  })

  // 生成二维数组格式（兼容性）
  const tableRowArray = targetData.map((item, itemIndex) => {
    const row = []
    enabledFields.forEach((field, fieldIndex) => {
      // 使用 restPath 而不是 path，因为这是REST字段的路径属性
      let fieldPath = field.restPath || field.path
      let value = getValueByPath(item, fieldPath)

      // 添加详细的数据提取调试
      if (itemIndex === 0) {
        // 只为第一行数据添加调试信息
        console.log(`字段 ${field.name} 数据提取:`, {
          fieldPath: fieldPath,
          extractedValue: value,
          itemKeys: Object.keys(item),
          fieldConfig: { restPath: field.restPath, path: field.path, name: field.name }
        })
      }

      // 如果按原路径找不到值，尝试去掉可能错误的前缀
      if (value === null && fieldPath.includes('.')) {
        const pathParts = fieldPath.split('.')
        // 尝试去掉第一层路径（如 result.total -> total）
        const simplifiedPath = pathParts.slice(1).join('.')
        if (simplifiedPath) {
          const simplifiedValue = getValueByPath(item, simplifiedPath)
          if (simplifiedValue !== null) {
            value = simplifiedValue
            console.log(`字段 ${field.name} 使用简化路径 ${simplifiedPath} 成功提取值:`, value)
          }
        }

        // 如果简化路径还是找不到，尝试直接使用最后一段路径
        if (value === null) {
          const lastPath = pathParts[pathParts.length - 1]
          const directValue = getValueByPath(item, lastPath)
          if (directValue !== null) {
            value = directValue
            console.log(`字段 ${field.name} 使用直接路径 ${lastPath} 成功提取值:`, value)
          }
        }
      }

      // 数据类型转换
      // 为第一行添加类型转换调试
      if (itemIndex === 0) {
        console.log(`transformRestDataToChartData 字段转换调试 - ${field.name}:`, {
          fieldType: field.type,
          rawValue: value,
          rawValueType: typeof value
        })
      }

      if (field.type === 'number' && value !== null && value !== undefined) {
        value = Number(value)
        if (isNaN(value)) value = 0
      } else if (field.type === 'boolean' && value !== null && value !== undefined) {
        value = Boolean(value)
      } else if (field.type === 'date' && value !== null && value !== undefined) {
        // 保持日期字符串格式，图表组件会自己处理
        value = String(value)
      } else if (field.type === 'array' && Array.isArray(value)) {
        // 对于数组类型的处理
        if (field.groupType === 'q') {
          // 如果是指标字段，返回数组长度
          value = value.length
        } else {
          // 如果是维度字段，需要特殊处理
          // 检查数组元素类型
          if (value.length > 0 && typeof value[0] === 'object') {
            // 如果是对象数组，对于仪表盘等简单图表，可以忽略或使用数组长度
            console.log(`字段 ${field.name} 是对象数组，长度: ${value.length}`)

            // 对于仪表盘组件，维度字段通常不重要，可以使用简化处理
            if (view.type === 'gauge' || view.type === 'liquid') {
              value = `${value.length} items` // 简化显示
            } else {
              // 对于其他图表，尝试提取第一个对象的某个字段或使用JSON
              value = JSON.stringify(value)
            }
          } else {
            // 如果是基本类型数组，转换为逗号分隔的字符串
            value = value.join(', ')
          }
        }
      } else if (field.type === 'object' && typeof value === 'object' && value !== null) {
        // 对于对象类型，转换为JSON字符串
        value = JSON.stringify(value)
      } else {
        value = value !== null && value !== undefined ? String(value) : ''
      }

      row.push(value)
    })
    return row
  })

  // 生成对象数组格式（S2表格组件需要）
  const tableRow = targetData.map((item, itemIndex) => {
    const rowObj = {}
    enabledFields.forEach((field, fieldIndex) => {
      let fieldPath = field.restPath || field.path
      let value = getValueByPath(item, fieldPath)

      // 如果按原路径找不到值，尝试去掉可能错误的前缀
      if (value === null && fieldPath.includes('.')) {
        const pathParts = fieldPath.split('.')
        const simplifiedPath = pathParts.slice(1).join('.')
        if (simplifiedPath) {
          const simplifiedValue = getValueByPath(item, simplifiedPath)
          if (simplifiedValue !== null) {
            value = simplifiedValue
          }
        }
        if (value === null) {
          const lastPath = pathParts[pathParts.length - 1]
          const directValue = getValueByPath(item, lastPath)
          if (directValue !== null) {
            value = directValue
          }
        }
      }

      // 数据类型转换（与上面相同的逻辑）
      if (field.type === 'number' && value !== null && value !== undefined) {
        value = Number(value)
        if (isNaN(value)) value = 0
      } else if (field.type === 'boolean' && value !== null && value !== undefined) {
        value = Boolean(value)
      } else if (field.type === 'date' && value !== null && value !== undefined) {
        value = String(value)
      } else if (field.type === 'array' && Array.isArray(value)) {
        if (field.groupType === 'q') {
          value = value.length
        } else {
          if (value.length > 0 && typeof value[0] === 'object') {
            if (view.type === 'gauge' || view.type === 'liquid') {
              value = `${value.length} items`
            } else {
              value = JSON.stringify(value)
            }
          } else {
            value = value.join(', ')
          }
        }
      } else if (field.type === 'object' && typeof value === 'object' && value !== null) {
        value = JSON.stringify(value)
      } else {
        value = value !== null && value !== undefined ? String(value) : ''
      }

      // 使用字段名作为对象的键
      rowObj[field.name] = value
    })

    if (itemIndex === 0) {
      console.log('第一行对象数据:', rowObj)
    }

    return rowObj
  })

  console.log('数据转换结果:', {
    enabledFieldsCount: enabledFields.length,
    tableRowCount: tableRow.length,
    tableRowArrayCount: tableRowArray.length,
    sampleRowObject: tableRow[0],
    sampleRowArray: tableRowArray[0],
    fieldPaths: enabledFields.map(f => ({ name: f.name, path: f.restPath || f.path })),
    targetDataSample: targetData[0],
    targetDataType: typeof targetData[0],
    targetDataKeys: targetData[0] ? Object.keys(targetData[0]) : []
  })

  // 详细调试每个字段的值提取过程
  if (targetData.length > 0) {
    const firstItem = targetData[0]
    console.log('详细字段提取调试:')
    console.log('第一个数据项:', firstItem)

    // 测试 getValueByPath 函数
    console.log('getValueByPath 测试:')
    console.log('测试 result.total:', getValueByPath(firstItem, 'result.total'))
    console.log('测试 result.rows:', getValueByPath(firstItem, 'result.rows'))

    enabledFields.forEach(field => {
      const fieldPath = field.restPath || field.path
      const value = getValueByPath(firstItem, fieldPath)
      console.log(`字段 ${field.name}:`, {
        path: fieldPath,
        extractedValue: value,
        valueType: typeof value,
        pathExists:
          fieldPath.split('.').reduce((obj, key) => {
            if (obj && typeof obj === 'object' && key in obj) {
              return obj[key]
            }
            return undefined
          }, firstItem) !== undefined
      })
    })
  }

  // 为指标卡组件生成series数据
  const series = []
  if (enabledFields.length > 0 && tableRow.length > 0) {
    enabledFields.forEach((field, fieldIndex) => {
      if (field.groupType === 'q') {
        // 只为指标字段生成series
        // 从对象数组中提取数据
        const seriesData = tableRow.map(row => row[field.name])
        series.push({
          name: field.customName || field.name,
          data: seriesData
        })
      }
    })
  }

  // 为图表组件生成正确的数据格式
  const result = {
    tableRow, // 对象数组格式，S2表格组件需要
    tableRowArray, // 二维数组格式，兼容性
    series,
    // 为图表组件添加字段信息
    fields: enabledFields.map(field => ({
      id: field.id,
      name: field.customName || field.name,
      dataeaseName: field.customName || field.name,
      originName: field.originalName || field.name,
      chartShowName: field.customName || field.name,
      groupType: field.groupType,
      deType: field.deType,
      extField: 0,
      checked: true,
      summary: field.summary || (field.groupType === 'q' ? 'sum' : undefined),
      sort: field.sort || 'none',
      restField: true,
      restPath: field.restPath || field.path,
      restType: field.type
    })),
    // 添加数据源信息
    sourceData: tableRow,
    totalPage: 1,
    currentPage: 1,
    total: tableRow.length
  }

  console.log('最终转换结果:', {
    tableRowCount: result.tableRow.length,
    fieldsCount: result.fields.length,
    seriesCount: result.series.length,
    sampleTableRow: result.tableRow[0],
    fieldNames: result.fields.map(f => f.name)
  })

  return result
}

// 根据路径获取值
const getValueByPath = (obj, path) => {
  if (!path || !obj) return null

  const paths = path.split('.')
  let value = obj

  for (const p of paths) {
    if (value && typeof value === 'object' && p in value) {
      value = value[p]
    } else {
      return null
    }
  }

  return value
}

// 转换REST字段为图表字段格式
const transformFieldsForChart = restFields => {
  if (!restFields || !Array.isArray(restFields)) {
    return []
  }

  return restFields
    .filter(field => field.enabled !== false)
    .map(field => ({
      id: field.id,
      name: field.customName || field.name,
      dataeaseName: field.customName || field.name,
      originName: field.originalName || field.name,
      chartShowName: field.customName || field.name, // 图表显示名称
      groupType: field.groupType,
      deType: field.deType,
      extField: 0,
      checked: true,
      summary: field.summary || (field.groupType === 'q' ? 'sum' : undefined),
      sort: field.sort || 'none',
      restField: true,
      restPath: field.restPath || field.path,
      restType: field.type,
      // 为仪表盘组件添加必要的字段
      fieldType: field.groupType === 'd' ? 'dimension' : 'quota',
      tableId: 'rest_field_' + (field.id || field.name)
    }))
}

// 打开REST配置对话框
const openRestConfig = () => {
  console.log('打开REST配置对话框，当前组件ID:', view.value.id)

  // 强制设置为REST数据源类型
  state.datasourceType = 'rest'

  // 在打开对话框前，确保配置是当前组件的最新配置
  if (view.value.restConfig) {
    state.restConfig = JSON.parse(JSON.stringify(view.value.restConfig))
    console.log('加载当前组件的REST配置:', state.restConfig)
  } else {
    // 如果当前组件没有配置，使用默认空配置
    state.restConfig = {
      url: '',
      method: 'GET',
      headers: [{ key: 'Content-Type', value: 'application/json' }],
      params: [],
      body: '',
      timeout: 30,
      dataPath: '',
      pagination: {
        enabled: false,
        pageParam: 'page',
        sizeParam: 'size',
        pageSize: 100
      }
    }
    console.log('使用默认空配置')
  }

  if (view.value.restFields) {
    state.restFields = JSON.parse(JSON.stringify(view.value.restFields))
    console.log('加载当前组件的REST字段:', state.restFields.length, '个字段')
  } else {
    state.restFields = []
    console.log('当前组件无REST字段配置')
  }

  // 打开对话框
  state.showRestConfig = true

  // 在下一个tick中清除REST配置组件的测试状态
  nextTick(() => {
    if (restDataSourceRef.value && restDataSourceRef.value.clearTestStates) {
      restDataSourceRef.value.clearTestStates()
      console.log('已调用清除测试状态方法')
    } else {
      console.log('REST配置组件引用不存在或clearTestStates方法不存在')
    }
  })
}

const saveRestConfig = () => {
  if (!state.restConfig || !state.restConfig.url) {
    ElMessage.warning('请配置REST接口URL')
    return
  }

  state.showRestConfig = false
  view.value.datasourceType = 'rest'
  // 深拷贝配置，避免引用问题
  view.value.restConfig = JSON.parse(JSON.stringify(state.restConfig))
  view.value.restFields = JSON.parse(JSON.stringify(state.restFields))

  // 确保图表有tableId（用于标识）
  if (!view.value.tableId) {
    view.value.tableId = 'rest_' + view.value.id
  }

  console.log('保存REST配置到组件:', {
    componentId: view.value.id,
    restConfigUrl: view.value.restConfig.url,
    restConfigDataPath: view.value.restConfig.dataPath,
    restFieldsCount: view.value.restFields.length
  })

  recordSnapshotInfo('calcData')
  ElMessage.success('REST配置保存成功')
}

// 处理REST数据计算
const calcRestData = async (view, resetDrill = false, updateQuery = '') => {
  try {
    // 确保使用正确的REST配置和字段
    const restConfig = view.restConfig || state.restConfig
    let restFields = view.restFields || state.restFields

    // 保护用户配置的字段类型，避免被重新推断覆盖
    if (restFields && restFields.length > 0) {
      restFields = restFields.map(field => {
        // 确保字段类型不被意外修改
        const protectedField = { ...field }
        if (!protectedField.type && protectedField.name) {
          // 只有在没有类型时才进行推断
          const lowerName = protectedField.name.toLowerCase()
          if (lowerName.includes('name') || lowerName.includes('title')) {
            protectedField.type = 'string'
          }
        }
        return protectedField
      })

      console.log('calcRestData 保护字段类型配置:', {
        fieldsCount: restFields.length,
        nameField: restFields.find(f => f.name === 'name'),
        allFieldTypes: restFields.map(f => ({ name: f.name, type: f.type }))
      })
    }

    if (!restConfig || !restConfig.url) {
      ElMessage.error('REST配置不完整，请重新配置')
      return
    }

    if (!restFields || restFields.length === 0) {
      ElMessage.error('请先配置REST字段映射')
      return
    }

    console.log('开始获取REST数据:', {
      url: restConfig.url,
      fieldsCount: restFields.length,
      config: restConfig
    })

    // 获取REST数据
    const restData = await getRestData(restConfig)

    console.log('REST数据获取成功:', {
      dataType: typeof restData,
      isArray: Array.isArray(restData),
      keys: restData ? Object.keys(restData) : [],
      sampleData: restData
    })

    // 转换数据格式为图表可用格式
    const chartData = transformRestDataToChartData(restData, restFields, view)

    // 检查是否需要应用数据配置面板的结果数量限制
    let finalChartData = chartData

    console.log('检查数据配置面板设置:', {
      resultMode: view.resultMode,
      resultCount: view.resultCount,
      viewType: view.type,
      totalRows: chartData.tableRow.length
    })

    // 检查数据配置面板的结果数量设置
    if (view.resultMode === 'custom' && view.resultCount && view.resultCount > 0) {
      const resultCount = parseInt(view.resultCount)
      console.log('应用数据配置面板限制:', {
        resultMode: view.resultMode,
        resultCount: resultCount,
        totalRows: chartData.tableRow.length
      })

      if (resultCount > 0 && chartData.tableRow.length > resultCount) {
        // 应用数据量限制，只取前N条数据
        const limitedTableRow = chartData.tableRow.slice(0, resultCount)
        const limitedTableRowArray = chartData.tableRowArray
          ? chartData.tableRowArray.slice(0, resultCount)
          : []

        finalChartData = {
          ...chartData,
          tableRow: limitedTableRow,
          tableRowArray: limitedTableRowArray,
          allRows: chartData.tableRow, // 保存完整数据用于后续分页
          total: chartData.tableRow.length, // 总数据量
          currentPage: 1,
          totalPage: Math.ceil(chartData.tableRow.length / resultCount)
        }

        console.log('数据量限制应用完成:', {
          originalRows: chartData.tableRow.length,
          limitedRows: limitedTableRow.length,
          totalPages: finalChartData.totalPage,
          limitedSampleRow: limitedTableRow[0],
          limitedTableRowArrayLength: limitedTableRowArray.length
        })
      }
    }

    // 为G2Plot图表生成特定的数据格式
    const generateG2PlotData = (tableRowArray, fields, view) => {
      console.log('编辑器generateG2PlotData输入检查:', {
        hasTableRowArray: !!tableRowArray,
        tableRowArrayLength: tableRowArray?.length || 0,
        tableRowArrayType: typeof tableRowArray,
        isArray: Array.isArray(tableRowArray),
        sampleTableRowArray: tableRowArray?.slice(0, 2),
        hasFields: !!fields,
        fieldsLength: fields?.length || 0,
        fieldsType: typeof fields,
        viewId: view?.id
      })

      if (!tableRowArray || tableRowArray.length === 0 || !fields || fields.length === 0) {
        return []
      }

      // 获取图表实际配置的轴字段
      const configuredXAxisFields = view?.xAxis || []
      const configuredYAxisFields = view?.yAxis || []

      console.log('编辑器图表轴配置检查:', {
        viewId: view?.id,
        configuredXAxisCount: configuredXAxisFields.length,
        configuredYAxisCount: configuredYAxisFields.length,
        configuredXAxisNames: configuredXAxisFields.map(f => f.name || f.dataeaseName),
        configuredYAxisNames: configuredYAxisFields.map(f => f.name || f.dataeaseName)
      })

      // 如果图表配置了轴字段，优先使用配置的字段
      let dimensionFields, measureFields

      if (configuredXAxisFields.length > 0 && configuredYAxisFields.length > 0) {
        // 使用图表配置的轴字段
        dimensionFields = configuredXAxisFields
        measureFields = configuredYAxisFields
        console.log('编辑器使用图表配置的轴字段')
      } else {
        // 回退到自动识别字段类型
        dimensionFields = fields.filter(field =>
          field.deType === 0 || field.deType === 1 || field.type === 'string' || field.type === 'date'
        )
        measureFields = fields.filter(field =>
          field.deType === 2 || field.type === 'number'
        )
        console.log('编辑器使用自动识别的字段类型')
      }

      console.log('编辑器生成G2Plot数据:', {
        totalRows: tableRowArray.length,
        fieldsCount: fields.length,
        dimensionFields: dimensionFields.length,
        measureFields: measureFields.length,
        dimensionFieldNames: dimensionFields.map(f => f.name),
        measureFieldNames: measureFields.map(f => f.name)
      })

      if (dimensionFields.length === 0 || measureFields.length === 0) {
        console.warn('编辑器缺少必要的维度或指标字段')
        return []
      }

      const chartData = []
      const dimensionField = dimensionFields[0] // 使用第一个维度字段作为X轴

      tableRowArray.forEach((rowObj, index) => {
        const dimensionFieldName = dimensionField.name || dimensionField.dataeaseName
        const dimensionValue = rowObj[dimensionFieldName]

        // 为每个指标字段生成一条数据
        measureFields.forEach(measureField => {
          const measureFieldName = measureField.name || measureField.dataeaseName
          const measureValue = rowObj[measureFieldName]

          // 调试数据提取（只对前3行和所有指标字段进行调试）
          if (index < 3) {
            console.log(`编辑器数据提取调试 - 行${index}, 指标${measureFieldName}:`, {
              rowObj: rowObj,
              rowObjKeys: Object.keys(rowObj),
              dimensionFieldName: dimensionFieldName,
              measureFieldName: measureFieldName,
              dimensionValue: dimensionValue,
              measureValue: measureValue,
              dimensionValueType: typeof dimensionValue,
              measureValueType: typeof measureValue
            })
          }

          chartData.push({
            field: String(dimensionValue || ''), // X轴值
            value: Number(measureValue || 0), // Y轴值
            category: measureFieldName // 系列名称
          })
        })
      })

      console.log('编辑器生成的G2Plot数据样例:', {
        totalCount: chartData.length,
        sampleData: chartData.slice(0, 3),
        dataStructure: chartData[0] ? Object.keys(chartData[0]) : []
      })

      return chartData
    }

    // 构建符合图表组件期望的数据格式
    const mockChartData = {
      tableRow: finalChartData.tableRow || [],
      tableRowArray: finalChartData.tableRowArray || [],
      fields: finalChartData.fields || transformFieldsForChart(restFields || []),
      sourceData: finalChartData.sourceData || finalChartData.tableRow || [],
      series: finalChartData.series || [], // 为指标卡组件添加series数据
      allRows: finalChartData.allRows, // 保存完整数据用于分页
      totalPage: finalChartData.totalPage || 1,
      currentPage: finalChartData.currentPage || 1,
      total: finalChartData.total || (finalChartData.tableRow ? finalChartData.tableRow.length : 0),
      data: generateG2PlotData(finalChartData.tableRow || finalChartData.tableRowArray, finalChartData.fields, view) // 添加G2Plot需要的数据格式
    }

    console.log('构建的图表数据:', {
      tableRowCount: mockChartData.tableRow.length,
      fieldsCount: mockChartData.fields.length,
      seriesCount: mockChartData.series.length,
      total: mockChartData.total,
      sampleTableRow: mockChartData.tableRow[0],
      fieldNames: mockChartData.fields.map(f => f.name),
      viewType: view.type,
      viewId: view.id,
      hasAllRows: !!mockChartData.allRows,
      allRowsCount: mockChartData.allRows ? mockChartData.allRows.length : 0,
      resultMode: view.resultMode,
      resultCount: view.resultCount
    })

    // 确保图表轴配置正确映射REST字段
    const ensureAxisMapping = (view, restFields) => {
      if (!restFields || !Array.isArray(restFields)) return view

      // 如果图表没有配置轴，自动配置
      if ((!view.xAxis || view.xAxis.length === 0) && (!view.yAxis || view.yAxis.length === 0)) {
        const dimensions = restFields.filter(f => f.groupType === 'd' && f.enabled !== false)
        const quotas = restFields.filter(f => f.groupType === 'q' && f.enabled !== false)

        // 为维度配置xAxis
        if (dimensions.length > 0 && !view.xAxis?.length) {
          view.xAxis = dimensions.slice(0, 1).map(field => ({
            id: field.id,
            name: field.customName || field.name,
            dataeaseName: field.customName || field.name,
            originName: field.originalName || field.name,
            groupType: 'd',
            deType: field.deType,
            extField: 0,
            checked: true,
            restField: true,
            restPath: field.restPath || field.path,
            restType: field.type
          }))
        }

        // 为指标配置yAxis
        if (quotas.length > 0 && !view.yAxis?.length) {
          view.yAxis = quotas.slice(0, 1).map(field => ({
            id: field.id,
            name: field.customName || field.name,
            dataeaseName: field.customName || field.name,
            originName: field.originalName || field.name,
            groupType: 'q',
            deType: field.deType,
            extField: 0,
            checked: true,
            summary: field.summary || 'sum',
            restField: true,
            restPath: field.restPath || field.path,
            restType: field.type
          }))
        }

        console.log('自动配置图表轴:', {
          xAxisCount: view.xAxis?.length || 0,
          yAxisCount: view.yAxis?.length || 0,
          dimensionsCount: dimensions.length,
          quotasCount: quotas.length
        })
      }

      return view
    }

    // 应用轴映射
    const mappedView = ensureAxisMapping({ ...view }, restFields)

    // 创建一个包含数据的view副本，标记为REST数据源
    const viewWithData = {
      ...mappedView,
      data: mockChartData,
      datasourceType: 'rest',
      restConfig: restConfig,
      restFields: restFields,
      tableId: view?.tableId || 'rest_' + view.id // 给REST数据源一个特殊的tableId
    }

    // 直接设置view.data，确保组件能获取到数据
    view.data = mockChartData

    // 将数据存储到dvMainStore中，模拟正常的数据流
    dvMainStore.setViewDataDetails(view.id, {
      data: mockChartData,
      code: 0,
      msg: 'success'
    })

    console.log('REST数据计算完成:', {
      viewId: view.id,
      viewType: view.type,
      datasourceType: viewWithData.datasourceType,
      fieldsCount: restFields?.length || 0,
      dataRowsCount: mockChartData.tableRow?.length || 0,
      seriesCount: mockChartData.series?.length || 0,
      isIndicator: view.type === 'indicator',
      viewDataSet: !!view.data,
      viewDataRowsCount: view.data?.tableRow?.length || 0,
      chartData: mockChartData
    })

    // 触发图表更新事件
    if (resetDrill) {
      useEmitt().emitter.emit('resetDrill-' + view.id, 0)
    } else {
      if (mobileInPc.value) {
        useEmitt().emitter.emit('onMobileStatusChange', {
          type: 'componentStyleChange',
          value: { type: 'calcData', component: JSON.parse(JSON.stringify(viewWithData)) }
        })
      } else {
        // 传递包含数据的view对象
        const eventName = 'calcData-' + view.id
        console.log('编辑器发送 calcData 事件:', {
          eventName: eventName,
          viewId: view.id,
          viewWithDataKeys: Object.keys(viewWithData),
          hasData: !!viewWithData.data,
          dataContent: viewWithData.data,
          viewWithData: viewWithData
        })

        // 测试事件发送
        console.log('准备发送事件:', eventName)

        // 测试事件监听器是否存在
        const listeners = useEmitt().emitter.all.get(eventName)
        console.log('事件监听器数量:', listeners ? listeners.length : 0)

        useEmitt().emitter.emit(eventName, viewWithData)
        console.log('事件已发送:', eventName)

        // 延迟检查事件是否被处理
        setTimeout(() => {
          console.log('检查事件处理结果 - 5秒后')
        }, 5000)
        snapshotStore.recordSnapshotCache('renderChart', view.id)
      }
    }

    snapshotStore.recordSnapshotCache('calcData', view.id)

    if (updateQuery === 'updateQuery') {
      queryList.value.forEach(ele => {
        useEmitt().emitter.emit(`updateQueryCriteria${ele.id}`)
      })
    }
  } catch (error) {
    ElMessage.error('REST数据获取失败: ' + error.message)
    console.error('REST数据计算错误:', error)
  }
}

const filterNode = (value, data) => {
  if (!value) {
    return true
  }
  return data.name?.includes(value)
}

const allFields = computed(() => {
  return concat(state.quotaData, state.dimensionData)
})

const queryList = computed(() => {
  let arr = []
  componentData.value.forEach(com => {
    if (com.innerType === 'VQuery') {
      arr.push(com)
    }
    if ('DeTabs' === com.innerType) {
      com.propValue.forEach(itx => {
        arr = [...itx.componentData.filter(item => item.innerType === 'VQuery'), ...arr]
      })
    }
  })
  return arr
})

const quotaData = computed(() => {
  let result = JSON.parse(JSON.stringify(state.quota))
  if (view.value?.type === 'table-info') {
    result = result?.filter(item => item.id !== '-1')
  }
  if (state.searchField) {
    result = result.filter(item =>
      item.name.toLowerCase().includes(state.searchField.toLowerCase())
    )
  }
  return result
})
const dimensionData = computed(() => {
  let result = JSON.parse(JSON.stringify(state.dimensionData))
  if (state.searchField) {
    result = result.filter(item =>
      item.name.toLowerCase().includes(state.searchField.toLowerCase())
    )
  }
  return result
})
const realQuota = computed(() => {
  let result = JSON.parse(JSON.stringify(state.quota))
  if (view.value?.type === 'table-info') {
    result = result?.filter(item => item.id !== '-1')
  }
  return result
})
provide('quotaData', realQuota)

const startToMove = (e: DragEvent, item) => {
  e.dataTransfer.setData(
    'dimension',
    JSON.stringify(
      item
        .filter(ele => ele.id)
        .map(ele => ({ ...cloneDeep(unref(ele)), datasetId: view.value.tableId }))
    )
  )
}

const dimensionItemChange = item => {
  recordSnapshotInfo('calcData')
  // do dimensionItemChange
  if (view.value.type === 'bar-range') {
    if (item.axisType === 'quota') {
      view.value.yAxisExt?.forEach(y => {
        y.dateStyle = item.dateStyle
        y.datePattern = item.datePattern
      })
    } else if (item.axisType === 'quotaExt') {
      view.value.yAxis?.forEach(y => {
        y.dateStyle = item.dateStyle
        y.datePattern = item.datePattern
      })
    }
  }
}
const dimensionItemRemove = item => {
  recordSnapshotInfo('calcData')
  if (item.removeType === 'dimension') {
    view.value.xAxis.splice(item.index, 1)
  } else if (item.removeType === 'dimensionExt') {
    view.value.xAxisExt.splice(item.index, 1)
  } else if (item.removeType === 'dimensionStack') {
    view.value.extStack.splice(item.index, 1)
  } else if (item.removeType === 'quota') {
    view.value.yAxis.splice(item.index, 1)
  } else if (item.removeType === 'quotaExt') {
    view.value.yAxisExt.splice(item.index, 1)
  } else if (item.removeType === 'xAxisExtRight') {
    view.value.extBubble.splice(item.index, 1)
  } else if (item.removeType === 'flowMapStartName') {
    view.value.flowMapStartName.splice(item.index, 1)
  } else if (item.removeType === 'flowMapEndName') {
    view.value.flowMapEndName.splice(item.index, 1)
  } else if (item.removeType === 'extColor') {
    view.value.extColor.splice(item.index, 1)
  }
}

const quotaItemChange = (axis: Axis, axisType: AxisType) => {
  recordSnapshotInfo('calcData')
  // do quotaItemChange
  emitter.emit('updateAxis', { axisType, axis: [axis], editType: 'update' })
}

const aggregateChange = () => {
  recordSnapshotInfo('calcData')
}

const quotaItemRemove = item => {
  recordSnapshotInfo('calcData')
  let axisType: AxisType = item.removeType
  let axis
  if (item.removeType === 'quota') {
    axisType = 'yAxis'
    axis = view.value.yAxis.splice(item.index, 1)
  } else if (item.removeType === 'quotaExt') {
    axisType = 'yAxisExt'
    axis = view.value.yAxisExt.splice(item.index, 1)
  } else if (item.removeType === 'extLabel') {
    axis = view.value.extLabel.splice(item.index, 1)
  } else if (item.removeType === 'extTooltip') {
    axis = view.value.extTooltip.splice(item.index, 1)
  } else if (item.removeType === 'extBubble') {
    axis = view.value.extBubble.splice(item.index, 1)
  }
  useEmitt().emitter.emit('removeAxis', { axisType, axis, editType: 'remove' })
}

const isFilterActive = computed(() => {
  return !!view.value.customFilter?.items?.length
})

const drillItemChange = () => {
  recordSnapshotInfo('calcData')
  // temp do nothing
}
const drillItemRemove = item => {
  recordSnapshotInfo('calcData')
  view.value.drillFields.splice(item.index, 1)
}

const customSortAxis = ref<AxisType>('xAxis')
const customSort = () => {
  state.showCustomSort = true
}
const customSortChange = val => {
  state.customSortList = val
}
const closeCustomSort = () => {
  state.showCustomSort = false
  state.customSortField = {}
  state.customSortList = []
}
const saveCustomSort = () => {
  view.value[customSortAxis.value].forEach(ele => {
    if (ele.id === state.customSortField.id) {
      ele.sort = 'custom_sort'
      ele.customSort = state.customSortList
    }
  })
  closeCustomSort()
}
const onCustomSort = item => {
  recordSnapshotInfo('render')
  state.customSortField = view.value.xAxis[item.index]
  customSortAxis.value = 'xAxis'
  customSort()
}

const onStackCustomSort = item => {
  recordSnapshotInfo('render')
  state.customSortField = view.value.extStack[item.index]
  customSortAxis.value = 'extStack'
  customSort()
}

const onExtCustomSort = item => {
  recordSnapshotInfo('render')
  state.customSortField = view.value.xAxisExt[item.index]
  customSortAxis.value = 'xAxisExt'
  customSort()
}

const onExtCustomRightSort = item => {
  recordSnapshotInfo('render')
  state.customSortField = view.value.extBubble[item.index]
  customSortAxis.value = 'extBubble'
  customSort()
}

const onCustomFlowMapStartNameSort = item => {
  recordSnapshotInfo('render')
  state.customSortField = view.value.flowMapStartName[item.index]
  customSortAxis.value = 'flowMapStartName'
  customSort()
}

const onCustomFlowMapEndNameSort = item => {
  recordSnapshotInfo('render')
  state.customSortField = view.value.flowMapEndName[item.index]
  customSortAxis.value = 'flowMapEndName'
  customSort()
}
const onCustomExtColorSort = item => {
  recordSnapshotInfo('render')
  state.customSortField = view.value.extColor[item.index]
  customSortAxis.value = 'extColor'
  customSort()
}
const onDrillCustomSort = item => {
  recordSnapshotInfo('render')
  state.customSortField = view.value.drillFields[item.index]
  customSortAxis.value = 'drillFields'
  customSort()
}
const onMove = e => {
  recordSnapshotInfo('calcData')
  state.moveId = e.draggedContext.element.id
  return true
}
// drag
const dragCheckType = (list, type) => {
  if (list && list.length > 0) {
    let valid = true
    for (let i = 0; i < list.length; i++) {
      if (list[i].groupType !== type) {
        list.splice(i, 1)
        valid = false
      }
    }
    if (!valid) {
      ElMessage({
        message: type === 'd' ? t('chart.error_q_2_d') : t('chart.error_d_2_q'),
        type: 'warning'
      })
    }
    return valid
  }
}
const dragMoveDuplicate = (list, e, mode) => {
  if (mode === 'ds') {
    list.splice(e.newDraggableIndex, 1)
  } else {
    const dup = list.filter(function (m) {
      return m.id === state.moveId
    })
    if (dup && dup.length > 1) {
      list.splice(e.newDraggableIndex, 1)
      return dup
    }
  }
}
const dragRemoveAggField = (list, e) => {
  const dup = list.filter(function (m) {
    return m.id === state.moveId
  })
  if (dup && dup.length > 0) {
    if (dup[0].summary === '') {
      list.splice(e.newDraggableIndex, 1)
    }
  }
}

const showAggregate = computed<boolean>(() => {
  if (view.value.type === 'bar-range') {
    const tempYAxis = view.value.yAxis[0]
    const tempYAxisExt = view.value.yAxisExt[0]
    if (
      (tempYAxis && tempYAxis.groupType === 'd') ||
      (tempYAxisExt && tempYAxisExt.groupType === 'd')
    ) {
      return true
    }
  }
  return false
})

const disableUpdate = computed(() => {
  let flag = false
  if (view.value.type === 'table-info') {
    return flag
  }
  if (!chartViewInstance.value) {
    return flag
  }
  const axisConfig = chartViewInstance.value.axisConfig
  if (!axisConfig) {
    return flag
  }
  for (const key in axisConfig) {
    if (Object.prototype.hasOwnProperty.call(axisConfig, key)) {
      const axis = view.value[key]
      if (axis instanceof Array) {
        axis.forEach(a => {
          if (a.desensitized) {
            flag = true
          }
        })
      }
    }
  }
  return flag
})

const dragCheckMapType = list => {
  if (list && list.length > 0) {
    let valid = true
    for (let i = 0; i < list.length; i++) {
      if (list[i].deType !== 5) {
        list.splice(i, 1)
        valid = false
      }
    }
    if (!valid) {
      ElMessage({
        message: t('chart.error_d_not_coordinates'),
        type: 'warning'
      })
    }
    return valid
  }
}

const addAxis = (e, axis: AxisType) => {
  recordSnapshotInfo('calcData')
  const axisSpec = chartViewInstance.value?.axisConfig[axis]
  if (!axisSpec) {
    return
  }
  const { type, limit, duplicate } = axisSpec
  let typeValid, dup

  if (view.value.type === 'bar-range' && (axis === 'yAxis' || axis === 'yAxisExt')) {
    //区间条形图先排除非时间纬度或者指标的情况
    const list = view.value[axis]
    if (list && list.length > 0) {
      let valid = true
      for (let i = 0; i < list.length; i++) {
        if (list[i].groupType === 'd' && list[i].deType === 1) {
          list[i].sort = 'asc'
        }
        if (!(list[i].groupType === 'q' || (list[i].groupType === 'd' && list[i].deType === 1))) {
          list.splice(i, 1)
          valid = false
        }
      }
      if (!valid) {
        ElMessage({
          message: t('chart.error_d_not_time_2_q'),
          type: 'warning'
        })
      }
      typeValid = valid
    }
  } else if (
    ((view.value.type === 'symbolic-map' || view.value.type === 'heat-map') && axis === 'xAxis') ||
    (view.value.type === 'flow-map' && (axis === 'xAxis' || axis === 'xAxisExt'))
  ) {
    typeValid = dragCheckMapType(view.value[axis])
  } else if (type) {
    typeValid = dragCheckType(view.value[axis], type)
  }

  // 针对指标卡进行数值类型判断
  if (typeValid && type === 'q' && view.value.type === 'indicator') {
    const list = view.value[axis]
    if (list && list.length > 0) {
      let valid = true
      for (let i = 0; i < list.length; i++) {
        if (list[i].deType !== 2 && list[i].deType !== 3) {
          list.splice(i, 1)
          valid = false
        }
      }
      typeValid = valid
      if (!typeValid) {
        ElMessage({
          message: t('chart.error_not_number'),
          type: 'warning'
        })
      }
    }
  }

  if (!duplicate) {
    dup = dragMoveDuplicate(view.value[axis], e, 'chart')
  }
  if (view.value[axis].length > limit) {
    const removedAxis = view.value[axis].splice(limit)
    if (e.newDraggableIndex + 1 <= limit) {
      emitter.emit('removeAxis', { axisType: axis, axis: removedAxis, editType: 'remove' })
      emitter.emit('addAxis', {
        axisType: axis,
        axis: [view.value[axis][e.newDraggableIndex]],
        editType: 'add'
      })
    }
  } else {
    if (!dup && typeValid) {
      const isGaugeOrLiquid = view.value.type === 'gauge' || view.value.type === 'liquid'
      const quotaData = cloneDeep(state.quotaData)
      emitter.emit('addAxis', {
        axisType: axis,
        axis: [view.value[axis][e.newDraggableIndex]],
        editType: 'add',
        ...(isGaugeOrLiquid ? { quotaData: quotaData } : {})
      })
    }
  }
  if (view.value.type === 'line') {
    if (view.value?.xAxisExt?.length && view.value?.yAxis?.length > 1) {
      const axis = view.value.yAxis.splice(1)
      emitter.emit('removeAxis', { axisType: 'yAxis', axis, editType: 'remove' })
    }
  }
  if (view.value.type.includes('chart-mix')) {
    if (axis === 'yAxis') {
      if (view.value.yAxisExt.length > 0) {
        const chartType = view.value.yAxisExt[0].chartType
        forEach(view.value.yAxis, axis => {
          if (chartType === 'bar') {
            axis.chartType = 'line'
          } else if (chartType === 'line') {
            axis.chartType = 'bar'
          }
        })
      }
    } else if (axis === 'yAxisExt') {
      if (view.value.yAxis.length > 0) {
        const chartType = view.value.yAxis[0].chartType
        forEach(view.value.yAxisExt, axis => {
          if (chartType === 'bar') {
            axis.chartType = 'line'
          } else if (chartType === 'line') {
            axis.chartType = 'bar'
          }
        })
      }
    }
  }

  if (typeValid && view.value.type === 'bar-range') {
    //处理某一个轴有数据的情况
    let tempType = null
    let tempDeType = null
    if (axis === 'yAxis' && view.value.yAxisExt[0]) {
      tempType = view.value.yAxisExt[0].groupType
      tempDeType = view.value.yAxisExt[0].deType
    } else if (axis === 'yAxisExt' && view.value.yAxis[0]) {
      tempType = view.value.yAxis[0].groupType
      tempDeType = view.value.yAxis[0].deType
    }
    if (tempType !== null) {
      const list = view.value[axis]
      if (list && list.length > 0) {
        let valid = true
        for (let i = 0; i < list.length; i++) {
          if (
            !(
              list[i].groupType === tempType &&
              (tempType === 'q' || (tempType === 'd' && list[i].deType === tempDeType))
            )
          ) {
            list.splice(i, 1)
            valid = false
          }
        }
        if (!valid) {
          ElMessage({
            message: t('chart.error_bar_range_axis_type_not_equal'),
            type: 'warning'
          })
        }
        typeValid = valid
      }
    }
  }
}

const addXaxis = e => {
  addAxis(e, 'xAxis')
}

const addXaxisExt = e => {
  addAxis(e, 'xAxisExt')
}

const addExtStack = e => {
  addAxis(e, 'extStack')
}

const addYaxis = e => {
  addAxis(e, 'yAxis')
}

const addYaxisExt = e => {
  addAxis(e, 'yAxisExt')
}

const addExtBubble = e => {
  addAxis(e, 'extBubble')
}

const addDrill = e => {
  recordSnapshotInfo('calcData')
  dragCheckType(view.value.drillFields, 'd')
  dragMoveDuplicate(view.value.drillFields, e, '')
  dragRemoveAggField(view.value.drillFields, e)
}

const addFlowMapStartName = e => {
  addAxis(e, 'flowMapStartName')
}

const addFlowMapEndName = e => {
  addAxis(e, 'flowMapEndName')
}

const addExtColor = e => {
  addAxis(e, 'extColor')
}

const onAxisChange = (e, axis: AxisType) => {
  if (e.removed) {
    const { element } = e.removed
    emitter.emit('removeAxis', { axisType: axis, axis: [element], editType: 'remove' })
  }
}

const calcData = (view, resetDrill = false, updateQuery = '') => {
  if (
    view.refreshTime === '' ||
    parseFloat(view.refreshTime).toString() === 'NaN' ||
    parseFloat(view.refreshTime) < 1
  ) {
    ElMessage.error(t('chart.only_input_number'))
    return
  }

  // 如果是REST数据源，需要特殊处理
  if (
    (state.datasourceType === 'rest' || view.datasourceType === 'rest') &&
    (view.restConfig || state.restConfig)
  ) {
    calcRestData(view, resetDrill, updateQuery)
    return
  }

  if (resetDrill) {
    useEmitt().emitter.emit('resetDrill-' + view.id, 0)
  } else {
    if (mobileInPc.value) {
      //移动端设计
      useEmitt().emitter.emit('onMobileStatusChange', {
        type: 'componentStyleChange',
        value: { type: 'calcData', component: JSON.parse(JSON.stringify(view)) }
      })
    } else {
      useEmitt().emitter.emit('calcData-' + view.id, view)
      snapshotStore.recordSnapshotCache('renderChart', view.id)
    }
  }
  snapshotStore.recordSnapshotCache('calcData', view.id)
  if (updateQuery === 'updateQuery') {
    queryList.value.forEach(ele => {
      useEmitt().emitter.emit(`updateQueryCriteria${ele.id}`)
    })
  }
}

const updateChartData = view => {
  curComponent.value['state'] = 'ready'
  useEmitt().emitter.emit('checkShowEmpty', { allFields: allFields.value, view: view })

  // 确保view对象包含正确的数据源信息
  if (state.datasourceType === 'rest') {
    view.datasourceType = 'rest'
    view.restConfig = state.restConfig
    view.restFields = state.restFields

    // 如果没有tableId，设置一个特殊的标识
    if (!view.tableId) {
      view.tableId = 'rest_' + view.id
    }

    // 验证REST配置完整性
    if (!state.restConfig || !state.restConfig.url) {
      ElMessage.error('请先配置REST接口URL')
      return
    }

    if (!state.restFields || state.restFields.length === 0) {
      ElMessage.error('请先配置字段映射')
      return
    }

    // 检查图表是否有字段配置
    const hasXAxis = view.xAxis && view.xAxis.length > 0
    const hasYAxis = view.yAxis && view.yAxis.length > 0

    // 指标卡组件只需要yAxis（指标）
    if (view.type === 'indicator') {
      if (!hasYAxis) {
        ElMessage.warning('指标卡组件请先将指标字段拖拽到指标区域')
        return
      }
    } else {
      // 其他图表类型需要至少一个轴
      if (!hasXAxis && !hasYAxis) {
        ElMessage.warning('请先将字段拖拽到图表的维度或指标区域')
        return
      }
    }

    console.log('更新图表数据 - REST数据源:', {
      viewId: view.id,
      viewType: view.type,
      datasourceType: view.datasourceType,
      hasRestConfig: !!view.restConfig,
      restFieldsCount: view.restFields?.length || 0,
      tableId: view.tableId,
      url: state.restConfig.url,
      dimensionCount: state.dimension?.length || 0,
      quotaCount: state.quota?.length || 0,
      xAxisCount: view.xAxis?.length || 0,
      yAxisCount: view.yAxis?.length || 0,
      isIndicator: view.type === 'indicator'
    })
  } else {
    console.log('更新图表数据 - 数据集数据源:', {
      viewId: view.id,
      datasourceType: state.datasourceType,
      tableId: view.tableId
    })
  }

  // 对于仪表盘组件，需要确保quotaData正确传递和yAxis配置
  if (view.type === 'gauge' || view.type === 'liquid') {
    console.log('仪表盘组件更新数据，检查配置:', {
      quotaDataCount: state.quotaData?.length || 0,
      quotaCount: state.quota?.length || 0,
      yAxisCount: view.yAxis?.length || 0,
      hasRestConfig: !!view.restConfig,
      restFieldsCount: view.restFields?.length || 0
    })

    // 确保仪表盘有yAxis配置
    if (!view.yAxis || view.yAxis.length === 0) {
      // 从REST字段中自动配置yAxis
      const quotaFields =
        state.restFields?.filter(f => f.groupType === 'q' && f.enabled !== false) || []
      if (quotaFields.length > 0) {
        view.yAxis = [quotaFields[0]]
        console.log('为仪表盘自动配置yAxis:', view.yAxis[0])
      }
    }

    // 触发addAxis事件，确保quotaData被正确传递
    if (view.yAxis && view.yAxis.length > 0) {
      const quotaData = cloneDeep(state.quotaData)
      emitter.emit('addAxis', {
        axisType: 'yAxis',
        axis: view.yAxis,
        editType: 'update',
        quotaData: quotaData
      })
    }
  }

  // 先调用calcData，然后根据结果决定是否需要延迟刷新
  // 对于REST数据源，不要重置drill，避免清除数据
  const shouldResetDrill = view.datasourceType !== 'rest'
  calcData(view, shouldResetDrill, 'updateQuery')

  // 对于REST数据源，添加智能延迟刷新机制
  if (view.datasourceType === 'rest') {
    // 设置一个标记，避免重复刷新
    const refreshKey = `refresh_${view.id}_${Date.now()}`

    setTimeout(() => {
      // 检查图表是否已经正常显示
      const container = document.getElementById(`container-canvas-${view.id}-common`)
      const hasChart = container && container.children.length > 0

      console.log('检查图表状态:', {
        viewId: view.id,
        containerExists: !!container,
        hasChart: hasChart,
        refreshKey: refreshKey
      })

      // 只有在图表没有正常显示时才进行延迟刷新
      if (!hasChart) {
        console.log('图表未正常显示，执行延迟刷新')

        // 使用深拷贝确保传递完整的view对象，避免属性缺失
        const completeView = cloneDeep(view)

        // 更新REST相关的配置
        completeView.datasourceType = 'rest'
        completeView.restConfig = state.restConfig
        completeView.restFields = state.restFields

        console.log('延迟刷新传递的view对象检查:', {
          hasCustomAttr: !!completeView.customAttr,
          hasTableCell: !!completeView.customAttr?.tableCell,
          hasYAxis: !!completeView.yAxis,
          yAxisLength: completeView.yAxis?.length || 0
        })

        useEmitt().emitter.emit('calcData-' + view.id, completeView)
      } else {
        console.log('图表已正常显示，跳过延迟刷新')
      }
    }, 300) // 减少延迟时间，提高响应速度
  }
}

const renderChart = view => {
  if (mobileInPc.value) {
    //移动端设计
    useEmitt().emitter.emit('onMobileStatusChange', {
      type: 'componentStyleChange',
      value: { type: 'renderChart', component: JSON.parse(JSON.stringify(view)) }
    })
  } else {
    useEmitt().emitter.emit('renderChart-' + view.id, view)
    snapshotStore.recordSnapshotCache('renderChart', view.id)
  }
}

const onAreaChange = val => {
  if (val.id === 'customRoot') {
    return
  }
  view.value.customAttr.map = { id: val.id, level: val.level }
  renderChart(view.value)
}

const onTypeChange = (render, type) => {
  const viewConf = getViewConfig(type)
  if (viewConf.isPlugin) {
    view.value.plugin = {
      isPlugin: true,
      staticMap: viewConf.staticMap
    }
    view.value.isPlugin = true
  } else {
    view.value.isPlugin = false
    delete view.value.plugin
  }
  view.value.render = render
  view.value.type = type
  emitter.emit('chart-type-change')
  emitter.emit('chart-type-change-' + view.value.id)
  // 处理配置项默认值，不同图表的同一配置项默认值不同
  const chartViewInstance = chartViewManager.getChartView(view.value.render, view.value.type)
  if (chartViewInstance) {
    view.value = chartViewInstance.setupDefaultOptions(view.value) as unknown as ChartObj
    // 处理轴
    const axisConfig = chartViewInstance.axisConfig
    keys(axisConfig).forEach((axis: AxisType) => {
      const axisArr = view.value[axis] as Axis[]
      if (!axisArr?.length) {
        return
      }
      const axisSpec = axisConfig[axis]
      const { type, limit } = axisSpec
      const removedAxis = []
      // check type
      if (type) {
        for (let i = axisArr.length - 1; i >= 0; i--) {
          if (axisArr[i].groupType !== type) {
            const [axis] = axisArr.splice(i, 1)
            removedAxis.push(axis)
          }
        }
      }
      // check limit
      if (limit && limit < axisArr.length) {
        axisArr.splice(limit).forEach(i => removedAxis.push(i))
      }
      removedAxis.length &&
        emitter.emit('removeAxis', { axisType: axis, axis: removedAxis, editType: 'remove' })
    })
    if (view.value.type === 'line') {
      if (view.value?.xAxisExt?.length && view.value?.yAxis?.length > 1) {
        const axis = view.value.yAxis.splice(1)
        emitter.emit('removeAxis', { axisType: 'yAxis', axis, editType: 'remove' })
      }
    }
    if (
      view.value.type === 'liquid' ||
      view.value.type === 'gauge' ||
      view.value.type === 'indicator'
    ) {
      removeItems('drillFields')
    }
    if (!['line', 'area', 'bar', 'bar-group'].includes(view.value.type)) {
      // 清除图表标注
      const pointElement = document.getElementById('point_' + view.value.id)
      if (pointElement) {
        pointElement.remove()
        pointElement.parentNode?.removeChild(pointElement)
      }
    }
  }
  curComponent.value.innerType = type
  calcData(view.value, true)
}

const onBasicStyleChange = (chartForm: ChartEditorForm<ChartBasicStyle>, prop: string) => {
  const { data, requestData, render } = chartForm
  const val = get(data, prop)
  set(view.value.customAttr.basicStyle, prop, val)
  if (requestData) {
    calcData(view.value)
  }
  if (render !== false) {
    renderChart(view.value)
  }
}

const onTableHeaderChange = val => {
  view.value.customAttr.tableHeader = val
  renderChart(view.value)
}
const onTableCellChange = val => {
  view.value.customAttr.tableCell = val
  renderChart(view.value)
}
const onTableTotalChange = val => {
  view.value.customAttr.tableTotal = val
  renderChart(view.value)
}

const onColorChange = val => {
  view.value.customAttr.color = val
  renderChart(view.value)
}

const onMiscChange = val => {
  view.value.customAttr.misc = val.data
  if (val.requestData) {
    calcData(view.value)
  } else {
    renderChart(view.value)
  }
}

const onLabelChange = (chartForm: ChartEditorForm<ChartLabelAttr>, prop: string) => {
  const { data, render } = chartForm
  let labelObj = data
  if (!data) {
    labelObj = chartForm as unknown as ChartLabelAttr
  }
  if (prop) {
    const val = get(labelObj, prop)
    set(view.value.customAttr.label, prop, val)
  } else {
    view.value.customAttr.label = labelObj
  }
  // for compatibility
  if (render !== false) {
    renderChart(view.value)
  }
}

const onIndicatorChange = (val, prop) => {
  if (prop === 'color' || prop === 'suffixColor') {
    view.value.customAttr.basicStyle.alpha = undefined
    if (val.indicatorName !== undefined) {
      view.value.customAttr.indicatorName = val.indicatorName
    }
  }
  view.value.customAttr.indicator = val.indicatorValue
  renderChart(view.value)
}

const onIndicatorNameChange = (val, prop) => {
  if (prop === 'color') {
    view.value.customAttr.basicStyle.alpha = undefined
    if (val.indicatorValue !== undefined) {
      view.value.customAttr.indicator = val.indicatorValue
    }
  }
  view.value.customAttr.indicatorName = val.indicatorName
  renderChart(view.value)
}

const onTooltipChange = (chartForm: ChartEditorForm<ChartTooltipAttr>, prop: string) => {
  const { data, requestData, render } = chartForm
  let tooltipObj = data
  if (!data) {
    tooltipObj = chartForm as unknown as ChartTooltipAttr
  }
  if (prop) {
    const val = get(tooltipObj, prop)
    set(view.value.customAttr.tooltip, prop, val)
  } else {
    view.value.customAttr.tooltip = tooltipObj
  }
  if (requestData) {
    calcData(view.value)
    return
  }
  // for compatibility
  if (render !== false) {
    renderChart(view.value)
  }
}

const onChangeXAxisForm = val => {
  view.value.customStyle.xAxis = val
  renderChart(view.value)
}

const onChangeYAxisForm = val => {
  view.value.customStyle.yAxis = val
  renderChart(view.value)
}

const onChangeYAxisExtForm = val => {
  view.value.customStyle.yAxisExt = val
  renderChart(view.value)
}

const onChangeMiscStyleForm = val => {
  view.value.customStyle.misc = val
  renderChart(view.value)
}

const onTextChange = val => {
  view.value.customStyle.text = val
  if (curComponent.value) {
    curComponent.value.name = view.value.title
    curComponent.value.title = view.value.title
  }
  if (mobileInPc.value) {
    //移动端设计
    useEmitt().emitter.emit('onMobileStatusChange', {
      type: 'componentStyleChange',
      value: { type: 'updateTitle', component: JSON.parse(JSON.stringify(view.value)) }
    })
  } else {
    useEmitt().emitter.emit('updateTitle-' + view.value.id)
    snapshotStore.recordSnapshotCache('renderChart', view.value.id)
  }
}

const onLegendChange = val => {
  view.value.customStyle.legend = val
  renderChart(view.value)
}

const onFunctionCfgChange = val => {
  view.value.senior.functionCfg = val
  renderChart(view.value)
}

const onBackgroundChange = val => {
  // 修复#13299
  if (curComponent.value.id === view.value?.id) {
    curComponent.value.commonBackground = val
    if (mobileInPc.value) {
      //移动端设计
      useEmitt().emitter.emit('onMobileStatusChange', {
        type: 'componentStyleChange',
        value: {
          type: 'commonBackground',
          component: JSON.parse(JSON.stringify(curComponent.value))
        }
      })
    }
  }
}

const onStyleAttrChange = val => {
  curComponent.value.style[val.property] = val.value
  if (mobileInPc.value) {
    //移动端设计
    useEmitt().emitter.emit('onMobileStatusChange', {
      type: 'componentStyleChange',
      value: { type: 'style', component: JSON.parse(JSON.stringify(curComponent.value)) }
    })
  }
}

const onAssistLineChange = val => {
  view.value.senior.assistLineCfg = val.data
  if (val.requestData) {
    calcData(view.value)
  } else {
    renderChart(view.value)
  }
}

const onThresholdChange = val => {
  view.value.senior.threshold = val
  let type = undefined
  view.value.senior.threshold?.tableThreshold?.some(item => {
    if (item.conditions.some(i => i.type === 'dynamic')) {
      type = 'calcData'
      return true
    }
    return false
  })
  if (type || view.value.type === 'rich-text') {
    calcData(view.value)
  } else {
    renderChart(view.value)
  }
}

const onMapMappingChange = val => {
  view.value.senior.areaMapping = val
  renderChart(view.value)
}

const onScrollCfgChange = val => {
  view.value.senior.scrollCfg = val
  renderChart(view.value)
}

const onBubbleAnimateChange = val => {
  view.value.senior.bubbleCfg = val
  renderChart(view.value)
}

const onTableColumnWidthChange = val => {
  if (editMode.value !== 'edit') {
    return
  }
  view.value.customAttr.basicStyle.tableFieldWidth = val
  snapshotStore.recordSnapshotCache('renderChart', view.value.id)
}

const onTablePageSizeChange = val => {
  if (editMode.value !== 'edit') {
    return
  }
  view.value.customAttr.basicStyle.tablePageSize = val
  snapshotStore.recordSnapshotCache('renderChart', view.value.id)
}

const onExtTooltipChange = val => {
  view.value.extTooltip = val
}
const onChangeQuadrantForm = val => {
  view.value.customAttr.quadrant = val
  renderChart(view.value)
}
const onChangeFlowMapLineForm = (val, prop) => {
  const value = get(val, prop)
  set(view.value.customAttr.misc.flowMapConfig.lineConfig, prop, value)
  renderChart(view.value)
}
const onChangeFlowMapPointForm = val => {
  view.value.customAttr.misc.flowMapConfig.pointConfig = val
  renderChart(view.value)
}

const showRename = val => {
  recordSnapshotInfo('render')
  state.itemForm = JSON.parse(JSON.stringify(val))
  if (!state.itemForm.chartShowName) {
    state.itemForm.chartShowName = state.itemForm.name
  }
  state.renameItem = true
}

const closeRename = () => {
  state.renameItem = false
}

const removeItems = (
  _type:
    | 'xAxis'
    | 'xAxisExt'
    | 'extStack'
    | 'yAxis'
    | 'yAxisExt'
    | 'extBubble'
    | 'customFilter'
    | 'drillFields'
    | 'flowMapStartName'
    | 'flowMapEndName'
    | 'extColor'
) => {
  recordSnapshotInfo('calcData')
  let axis = []
  switch (_type) {
    case 'xAxis':
      axis = view.value.xAxis?.splice(0)
      break
    case 'xAxisExt':
      axis = view.value.xAxisExt?.splice(0)
      break
    case 'extStack':
      axis = view.value.extStack?.splice(0)
      break
    case 'yAxis':
      axis = view.value.yAxis?.splice(0)
      break
    case 'yAxisExt':
      axis = view.value.yAxisExt?.splice(0)
      break
    case 'extBubble':
      axis = view.value.extBubble?.splice(0)
      break
    case 'customFilter':
      view.value.customFilter = {}
      return
      break
    case 'drillFields':
      axis = view.value.drillFields?.splice(0)
      break
    case 'flowMapStartName':
      axis = view.value.flowMapStartName?.splice(0)
      break
    case 'flowMapEndName':
      axis = view.value.flowMapEndName?.splice(0)
      break
    case 'extColor':
      axis = view.value.extColor?.splice(0)
      break
  }
  axis?.length && emitter.emit('removeAxis', { axisType: _type, axis, editType: 'remove' })
}

const saveRename = ref => {
  if (!ref) return
  ref.validate(valid => {
    if (valid) {
      const { renameType, index, chartShowName } = state.itemForm
      let axisType, axis
      switch (renameType) {
        case 'quota':
          axisType = 'yAxis'
          axis = view.value.yAxis[index]
          view.value.yAxis[index].chartShowName = chartShowName
          break
        case 'dimension':
          view.value.xAxis[index].chartShowName = chartShowName
          break
        case 'quotaExt':
          axisType = 'yAxisExt'
          axis = view.value.yAxisExt[index]
          view.value.yAxisExt[index].chartShowName = chartShowName
          break
        case 'dimensionExt':
          view.value.xAxisExt[index].chartShowName = chartShowName
          break
        case 'xAxisExtRight':
          view.value.extBubble[index].chartShowName = chartShowName
          break
        case 'dimensionStack':
          view.value.extStack[index].chartShowName = chartShowName
          break
        case 'extBubble':
          axisType = 'extBubble'
          axis = view.value.extBubble[index]
          view.value.extBubble[index].chartShowName = chartShowName
          break
        case 'extLabel':
          view.value.extLabel[index].chartShowName = chartShowName
          break
        case 'extTooltip':
          view.value.extTooltip[index].chartShowName = chartShowName
        case 'flowMapStartName':
          axisType = 'flowMapStartName'
          axis = view.value.flowMapStartName[index]
          view.value.flowMapStartName[index].chartShowName = chartShowName
          break
        case 'flowMapEndName':
          axisType = 'flowMapEndName'
          axis = view.value.flowMapEndName[index]
          view.value.flowMapEndName[index].chartShowName = chartShowName
          break
        case 'extColor':
          axisType = 'extColor'
          axis = view.value.extColor[index]
          view.value.extColor[index].chartShowName = chartShowName
          break
        case 'drillFields':
          axisType = 'drillFields'
          axis = view.value.drillFields[index]
          view.value.drillFields[index].chartShowName = chartShowName
        default:
          break
      }
      axisType && emitter.emit('updateAxis', { axisType, axis: [axis], editType: 'update' })
      closeRename()
    } else {
      return false
    }
  })
}

const showQuotaEditFilter = item => {
  recordSnapshotInfo('calcData')
  state.quotaItem = JSON.parse(JSON.stringify(item))
  if (!state.quotaItem.logic) {
    state.quotaItem.logic = 'and'
  }
  state.quotaFilterEdit = true
}
const closeQuotaFilter = () => {
  state.quotaFilterEdit = false
}
const saveQuotaFilter = () => {
  for (let i = 0; i < state.quotaItem.filter.length; i++) {
    const f = state.quotaItem.filter[i]
    if (!f.term.includes('null') && !f.term.includes('empty') && (!f.value || f.value === '')) {
      ElMessage.error(t('chart.filter_value_can_null'))
      return
    }
    if (!f.term.includes('null') && !f.term.includes('empty') && isNaN(f.value)) {
      ElMessage.error(t('chart.filter_value_can_not_str'))
      return
    }
  }
  if (state.quotaItem.filterType === 'quota') {
    view.value.yAxis[state.quotaItem.index].filter = state.quotaItem.filter
    view.value.yAxis[state.quotaItem.index].logic = state.quotaItem.logic
  } else if (state.quotaItem.filterType === 'quotaExt') {
    view.value.yAxisExt[state.quotaItem.index].filter = state.quotaItem.filter
    view.value.yAxisExt[state.quotaItem.index].logic = state.quotaItem.logic
  }
  closeQuotaFilter()
}
const changeFilterData = customFilter => {
  view.value.customFilter = cloneDeep(customFilter)
}
const filterTree = ref()

const openTreeFilter = () => {
  filterTree.value.init(cloneDeep(view.value.customFilter))
}
const closeResultFilter = () => {
  state.resultFilterEdit = false
}
const saveResultFilter = () => {
  if (
    ((state.filterItem.deType === 0 || state.filterItem.deType === 5) &&
      state.filterItem.filterType !== 'enum') ||
    state.filterItem.deType === 1 ||
    state.filterItem.deType === 2 ||
    state.filterItem.deType === 3
  ) {
    for (let i = 0; i < state.filterItem.filter.length; i++) {
      const f = state.filterItem.filter[i]
      if (!f.term.includes('null') && !f.term.includes('empty') && (!f.value || f.value === '')) {
        ElMessage.error(t('chart.filter_value_can_null'))
        return
      }
      if (state.filterItem.deType === 2 || state.filterItem.deType === 3) {
        if (!f.term.includes('null') && !f.term.includes('empty') && isNaN(f.value)) {
          ElMessage.error(t('chart.filter_value_can_not_str'))
          return
        }
      }
    }
  }
  view.value.customFilter[state.filterItem.index].filter = state.filterItem.filter
  view.value.customFilter[state.filterItem.index].logic = state.filterItem.logic
  view.value.customFilter[state.filterItem.index].filterType = state.filterItem.filterType
  view.value.customFilter[state.filterItem.index].enumCheckField = state.filterItem.enumCheckField
  closeResultFilter()
}

const collapseChange = type => {
  canvasCollapse.value[type] = !canvasCollapse.value[type]
}
const openHandler = ref(null)
const initOpenHandler = newWindow => {
  if (openHandler?.value) {
    const pm = {
      methodName: 'initOpenHandler',
      args: newWindow
    }
    openHandler.value.invokeMethod(pm)
  }
}
const addDsWindow = () => {
  if (!dvInfo.value.id) {
    ElMessage.warning(t('visualization.save_page_tips'))
    return
  }
  const path =
    embeddedStore.getToken && appStore.getIsIframe ? 'dataset-embedded-form' : '/dataset-form'
  let routeData = router.resolve(path)
  const openType = wsCache.get('open-backend') === '1' ? '_self' : '_blank'
  const newWindow = window.open(routeData.href, openType)
  initOpenHandler(newWindow)
}
const editDs = () => {
  const path =
    embeddedStore.getToken && appStore.getIsIframe ? 'dataset-embedded-form' : '/dataset-form'
  const openType = wsCache.get('open-backend') === '1' ? '_self' : '_blank'
  // 此处校验提前 防止router返回时找到错误的路径
  if (openType === '_self' && !dvInfo.value.id) {
    ElMessage.warning(t('visualization.save_page_tips'))
    return
  }
  let routeData = router.resolve({
    path: path,
    query: {
      id: view.value.tableId
    }
  })
  // 检查是否保存
  if (openType === '_self') {
    if (!dvInfo.value.id) {
      ElMessage.warning(t('visualization.save_page_tips'))
      return
    }
    canvasSave(() => {
      wsCache.delete('DE-DV-CATCH-' + dvInfo.value.id)
      const newWindow = window.open(routeData.href, openType)
      initOpenHandler(newWindow)
    })
  } else {
    const newWindow = window.open(routeData.href, openType)
    initOpenHandler(newWindow)
  }
}

const showQuotaEditCompare = item => {
  recordSnapshotInfo('calcData')
  state.quotaItemCompare = JSON.parse(JSON.stringify(item))
  state.showEditQuotaCompare = true
}

const closeQuotaEditCompare = () => {
  state.showEditQuotaCompare = false
}

const saveQuotaEditCompare = () => {
  // 更新指标
  if (state.quotaItemCompare.calcType === 'quota') {
    view.value.yAxis[state.quotaItemCompare.index].compareCalc = state.quotaItemCompare.compareCalc
  } else if (state.quotaItemCompare.calcType === 'quotaExt') {
    view.value.yAxisExt[state.quotaItemCompare.index].compareCalc =
      state.quotaItemCompare.compareCalc
  } else if (state.quotaItemCompare.calcType === 'extLabel') {
    view.value.extLabel[state.quotaItemCompare.index].compareCalc =
      state.quotaItemCompare.compareCalc
  } else if (state.quotaItemCompare.calcType === 'extTooltip') {
    view.value.extTooltip[state.quotaItemCompare.index].compareCalc =
      state.quotaItemCompare.compareCalc
  }
  closeQuotaEditCompare()
}
const onToggleHide = item => {
  recordSnapshotInfo('render')
  switch (item.axisType) {
    case 'dimension':
      view.value.xAxis[item.index].hide = item.hide
      break
    case 'quota':
      view.value.yAxis[item.index].hide = item.hide
      break
    default:
      break
  }
  renderChart(view.value)
}
const editSortPriority = () => {
  state.showSortPriority = true
}
const closeSortPriority = () => {
  state.showSortPriority = false
}
const saveSortPriority = () => {
  view.value.sortPriority = state.sortPriority as ChartViewField[]
  recordSnapshotInfo('render')
  closeSortPriority()
}
const onPriorityChange = val => {
  state.sortPriority = val
}
const valueFormatter = item => {
  recordSnapshotInfo('render')
  state.valueFormatterItem = JSON.parse(JSON.stringify(item))
  state.showValueFormatter = true
}
const closeValueFormatter = () => {
  state.showValueFormatter = false
}
const saveValueFormatter = () => {
  const ele = state.valueFormatterItem.formatterCfg.decimalCount
  if (
    ele === undefined ||
    ele.toString().indexOf('.') > -1 ||
    parseInt(ele).toString() === 'NaN' ||
    parseInt(ele) < 0 ||
    parseInt(ele) > 10
  ) {
    ElMessage.error(t('chart.formatter_decimal_count_error'))
    return
  }
  // 更新指标
  if (state.valueFormatterItem.formatterType === 'quota') {
    view.value.yAxis[state.valueFormatterItem.index].formatterCfg =
      state.valueFormatterItem.formatterCfg
  } else if (state.valueFormatterItem.formatterType === 'quotaExt') {
    view.value.yAxisExt[state.valueFormatterItem.index].formatterCfg =
      state.valueFormatterItem.formatterCfg
  } else if (state.valueFormatterItem.formatterType === 'dimension') {
    view.value.xAxis[state.valueFormatterItem.index].formatterCfg =
      state.valueFormatterItem.formatterCfg
  }
  closeValueFormatter()
}

const elRowStyle = computed(() => {
  return {
    height: embeddedStore.getToken ? 'calc(100% - 45px)' : 'calc(100vh - 110px)'
  }
})

const addCalcField = groupType => {
  editCalcField.value = true
  isCalcFieldAdd.value = true
  nextTick(() => {
    calcEdit.value.initEdit(
      { groupType, id: guid() },
      state.dimension,
      state.quota.filter(ele => ele.id !== '-1')
    )
  })
}
const editField = item => {
  editCalcField.value = true
  isCalcFieldAdd.value = false
  nextTick(() => {
    calcEdit.value.initEdit(
      item,
      state.dimension,
      state.quota.filter(ele => ele.id !== '-1')
    )
  })
}
const closeEditCalc = () => {
  editCalcField.value = false
}
const confirmEditCalc = () => {
  calcEdit.value.setFieldForm()
  const obj = cloneDeep(calcEdit.value.fieldForm)
  setFieldDefaultValue(obj)
  saveField(obj).then(() => {
    getFields(view.value.tableId, view.value.id, view.value.type)
    closeEditCalc()
  })
}

const getIconName = (deType, extField, dimension = false) => {
  if (extField === 2) {
    const iconFieldCalculated = dimension ? iconFieldCalculatedMap : iconFieldCalculatedQMap
    return iconFieldCalculated[deType]
  }
  return iconFieldMap[fieldType[deType]]
}

const chartFieldEdit = param => {
  state.currEditField = JSON.parse(JSON.stringify(param.item))
  switch (param.type) {
    case 'copy':
      setFieldDefaultValue(state.currEditField)
      state.currEditField.id = null
      state.currEditField.originName =
        param.item.extField === 2 ? param.item.originName : '[' + param.item.id + ']'
      state.currEditField.name = getFieldName(state.dimension.concat(state.quota), param.item.name)

      saveField(state.currEditField).then(() => {
        getFields(view.value.tableId, view.value.id, view.value.type)
      })
      break
    case 'edit':
      editField(param.item)
      break
    case 'delete':
      deleteField(param.item?.id).then(() => {
        getFields(view.value.tableId, view.value.id, view.value.type)
      })
      break
  }
}
const handleChartFieldEdit = (item, type) => {
  return {
    type: type,
    item: item
  }
}
const setFieldDefaultValue = field => {
  field.extField = 2
  field.chartId = view.value.id
  field.datasetGroupId = view.value.tableId
  field.dataeaseName = null
  field.lastSyncTime = null
  field.columnIndex = state.dimension.length + state.quota.length
  field.deExtractType = field.deType
}

const el = ref<HTMLElement | null>(null)
const elDrag = ref<HTMLElement | null>(null)
const { y, isDragging } = useDraggable(el, {
  initialValue: { x: 0, y: 400 },
  draggingElement: elDrag
})
const previewHeight = ref(0)
const calcEle = debounce(() => {
  nextTick(() => {
    previewHeight.value = (elDrag.value as HTMLDivElement).offsetHeight
    y.value = previewHeight.value / 2 + 200
  })
}, 500)

const setCacheId = debounce(() => {
  nextTick(() => {
    // 富文本不使用cacheId
    if (
      !cacheId ||
      !!view.value.tableId ||
      templateStatusShow.value ||
      ['rich-text', 'picture-group'].includes(view.value.type)
    )
      return
    view.value.tableId = cacheId as unknown as number
  })
}, 500)
watch(
  () => curComponent.value,
  val => {
    if (!val || !!previewHeight.value) return
    calcEle()
  }
)

watch(
  () => curComponent.value,
  val => {
    if (!val) return
    setCacheId()
  }
)

const fieldDHeight = computed(() => {
  const h = y.value - 200
  if (h < 53) {
    return 53
  }
  return h > previewHeight.value - 50 ? previewHeight.value - 50 : h
})

const dragVerticalTop = computed(() => {
  const h = y.value - 200
  if (h < 50) {
    return 50
  }
  return h > previewHeight.value - 53 ? previewHeight.value - 53 : h
})

const onRefreshChange = val => {
  recordSnapshotInfo('render')
  if (val === '' || parseFloat(val).toString() === 'NaN' || parseFloat(val) < 1) {
    ElMessage.error(t('chart.only_input_number'))
    return
  }
}

const isCtrl = ref(false)

const isDraggingItem = ref(false)

const activeDimension = ref<Axis[]>([])
const activeQuota = ref<Axis[]>([])

const setActive = (ele, type = 'dimension') => {
  if (isCtrl.value) {
    isCtrl.value = false
  }
  const activeChild = type === 'dimension' ? activeDimension : activeQuota
  const deactivateChild = type === 'quota' ? activeDimension : activeQuota
  deactivateChild.value = []
  activeChild.value = activeChild.value.some(item => item.id === ele.id) ? [] : [ele]
}

const setActiveCtrl = (ele, type = 'dimension') => {
  isCtrl.value = true
  const activeChild = type === 'dimension' ? activeDimension : activeQuota
  const deactivateChild = type === 'quota' ? activeDimension : activeQuota
  deactivateChild.value = []
  const index = activeChild.value.findIndex(item => item.id === ele.id)
  if (index !== -1) {
    activeChild.value.splice(index, 1)
    return
  }
  activeChild.value.push(ele)
}

const setActiveShift = (ele, type = 'dimension') => {
  const activeChild = type === 'dimension' ? activeDimension : activeQuota
  const deactivateChild = type === 'quota' ? activeDimension : activeQuota
  const dataArr = type === 'dimension' ? dimensionData : quotaData
  deactivateChild.value = []
  const dimensionDataId = dataArr.value.map(ele => ele.id)
  const dimensionDataActiveChild = activeChild.value.filter(ele => dimensionDataId.includes(ele.id))
  if (!dimensionDataActiveChild.length) {
    const index = activeChild.value.findIndex(item => item.id === ele.id)
    if (index !== -1) {
      activeChild.value.splice(index, 1)
      return
    }
    activeChild.value.push(ele)
  } else {
    const startItx = dataArr.value.findIndex(
      item => item.id === dimensionDataActiveChild[dimensionDataActiveChild.length - 1].id
    )
    const endItx = dataArr.value.findIndex(item => item.id === ele.id)
    if (startItx === endItx) return
    if (startItx > endItx) {
      activeChild.value = [...activeChild.value, ...dataArr.value.slice(endItx, startItx)]
    }
    if (startItx < endItx) {
      activeChild.value = [...activeChild.value, ...dataArr.value.slice(startItx + 1, endItx + 1)]
    }
  }
}

const isDrag = ref(false)

const dragStartD = () => {
  isDrag.value = true
  setTimeout(() => {
    isDraggingItem.value = true
  }, 0)
}

const singleDragStartD = (e: DragEvent, ele, type) => {
  const activeChild = type === 'dimension' ? activeDimension : activeQuota
  const deactivateChild = type === 'quota' ? activeDimension : activeQuota
  deactivateChild.value = []
  if (!activeChild.value.length) {
    activeChild.value = [unref(ele)]
  }
  startToMove(e, unref(activeDimension.value))
}

const dragStart = () => {
  isDrag.value = true
  setTimeout(() => {
    isDraggingItem.value = true
  }, 0)
}

const singleDragStart = (e: DragEvent, ele, type) => {
  const activeChild = type === 'dimension' ? activeDimension : activeQuota
  const deactivateChild = type === 'quota' ? activeDimension : activeQuota
  deactivateChild.value = []
  if (!activeChild.value.length) {
    activeChild.value = [ele]
  }
  e.dataTransfer.setData(
    'quota',
    JSON.stringify(
      activeQuota.value
        .filter(ele => ele.id)
        .map(ele => ({ ...cloneDeep(unref(ele)), datasetId: view.value.tableId }))
    )
  )
}

const dragEnd = () => {
  isDrag.value = false
  isDraggingItem.value = false
}

const singleDragEnd = () => {
  activeDimension.value = []
  activeQuota.value = []
  dragEnd()
}

const dragEnter = (ev: MouseEvent) => {
  ev.preventDefault()
}

const dragOver = (ev: MouseEvent) => {
  ev.preventDefault()
}

const drop = (ev: MouseEvent, type = 'xAxis') => {
  ev.preventDefault()
  const arr = activeDimension.value.length ? activeDimension.value : activeQuota.value
  for (let i = 0; i < arr.length; i++) {
    const obj = cloneDeep(arr[i])
    state.moveId = obj.id as unknown as number
    view.value[type] ??= []
    view.value[type].push(obj)
    const e = { newDraggableIndex: view.value[type].length - 1 }

    if ('drillFields' === type) {
      addDrill(e)
    } else {
      addAxis(e, type as AxisType)
    }
  }
}

const fieldLoading = ref(false)

const copyChartFieldItem = id => {
  fieldLoading.value = true
  copyChartField(id, view.value.id)
    .then(() => {
      getFields(view.value.tableId, view.value.id, view.value.type)
    })
    .catch(() => {
      fieldLoading.value = false
    })
}

const deleteChartFieldItem = id => {
  fieldLoading.value = true
  deleteChartField(id)
    .then(() => {
      getFields(view.value.tableId, view.value.id, view.value.type)
    })
    .catch(() => {
      fieldLoading.value = false
    })
}
</script>

<template>
  <div class="chart-edit" :class="'editor-' + themes" @keydown.stop @keyup.stop>
    <!--移动端设计使用 注意与pc设计同步-->
    <template v-if="mobileInPc">
      <el-container direction="vertical">
        <el-scrollbar class="drag_main_area">
          <VQueryChartStyle
            v-if="view.type === 'VQuery' && curComponent"
            :element="curComponent"
            :common-background-pop="curComponent?.commonBackground"
            :chart="view"
            :themes="themes"
          />
          <template v-else-if="view.plugin?.isPlugin">
            <plugin-component
              :jsname="view.plugin.staticMap['editor-style']"
              :view="view"
              :dimension="state.dimension"
              :quota="state.quota"
              :themes="themes"
              :emitter="emitter"
            />
          </template>
          <template v-else>
            <chart-style
              v-if="chartStyleShow && chartViewInstance"
              :properties="chartViewInstance?.properties"
              :property-inner-all="chartViewInstance?.propertyInner"
              :selector-spec="chartViewInstance?.selectorSpec"
              :common-background-pop="curComponent?.commonBackground"
              :common-border-pop="curComponent?.style"
              :event-info="curComponent?.events"
              :chart="view"
              :themes="themes"
              :dimension-data="state.dimension"
              :quota-data="state.quota"
              :all-fields="allFields"
              @onColorChange="onColorChange"
              @onMiscChange="onMiscChange"
              @onLabelChange="onLabelChange"
              @onTooltipChange="onTooltipChange"
              @onChangeXAxisForm="onChangeXAxisForm"
              @onChangeYAxisForm="onChangeYAxisForm"
              @onChangeYAxisExtForm="onChangeYAxisExtForm"
              @onTextChange="onTextChange"
              @onIndicatorChange="onIndicatorChange"
              @onIndicatorNameChange="onIndicatorNameChange"
              @onLegendChange="onLegendChange"
              @onBackgroundChange="onBackgroundChange"
              @onStyleAttrChange="onStyleAttrChange"
              @onBasicStyleChange="onBasicStyleChange"
              @onTableHeaderChange="onTableHeaderChange"
              @onTableCellChange="onTableCellChange"
              @onTableTotalChange="onTableTotalChange"
              @onChangeMiscStyleForm="onChangeMiscStyleForm"
              @onExtTooltipChange="onExtTooltipChange"
              @onChangeQuadrantForm="onChangeQuadrantForm"
              @onChangeFlowMapLineForm="onChangeFlowMapLineForm"
              @onChangeFlowMapPointForm="onChangeFlowMapPointForm"
            />
          </template>
        </el-scrollbar>
      </el-container>
    </template>

    <!--pc端设计使用-->
    <template v-if="!mobileInPc">
      <el-row v-loading="loading" class="de-chart-editor">
        <div
          class="content-area"
          :class="{
            'content-area-close': canvasCollapse.chartAreaCollapse,
            'content-area-left-open': !canvasCollapse.chartAreaCollapse
          }"
        >
          <el-icon
            :title="view.title"
            class="custom-icon"
            size="20px"
            @click="collapseChange('chartAreaCollapse')"
          >
            <Fold v-if="canvasCollapse.chartAreaCollapse" class="collapse-icon" />
            <Expand v-else class="collapse-icon" />
          </el-icon>
          <div v-if="canvasCollapse.chartAreaCollapse" class="collapse-title">
            <span style="font-size: 14px">{{ view.title }}</span>
          </div>
          <div v-if="!canvasCollapse.chartAreaCollapse" style="width: 240px" class="view-panel-row">
            <el-row class="editor-title">
              <div style="display: flex; align-items: center; width: calc(100% - 24px)">
                <div
                  id="component-name"
                  class="name-area"
                  style="max-width: 180px; text-overflow: ellipsis; white-space: nowrap"
                  :style="{ width: componentNameEdit ? '300px' : 'auto' }"
                  :class="{ 'component-name-dark': themes === 'dark' }"
                  @dblclick="editComponentName"
                >
                  {{ view.title }}
                </div>
                <el-popover show-arrow :offset="8" placement="bottom" width="200" trigger="click">
                  <template #reference>
                    <el-icon style="margin-left: 4px; cursor: pointer"
                      ><Icon><dvInfoSvg class="svg-icon" /></Icon
                    ></el-icon>
                  </template>
                  <div style="margin-bottom: 4px; font-size: 14px; color: #646a73">
                    {{ t('visualization.view_id') }}
                  </div>
                  <div style="font-size: 14px; color: #1f2329">
                    {{ view.id }}
                  </div>
                </el-popover>
              </div>
            </el-row>

            <el-row :style="elRowStyle">
              <el-scrollbar v-if="view.type === 'VQuery' && curComponent">
                <div class="query-style-tab">
                  <div style="padding-top: 1px">
                    <VQueryChartStyle
                      :element="curComponent"
                      :common-background-pop="curComponent?.commonBackground"
                      :chart="view"
                      :themes="themes"
                    />
                  </div>
                </div>
              </el-scrollbar>
              <el-tabs
                v-else
                v-model="tabActive"
                class="tab-header"
                :class="{ dark: themes === 'dark' }"
              >
                <el-tab-pane name="data" :label="t('chart.chart_data')" class="padding-tab">
                  <el-container direction="vertical">
                    <el-scrollbar class="has-footer drag_main_area attr-style theme-border-class">
                      <el-row
                        v-if="!['rich-text', 'Picture', 'picture-group'].includes(view.type)"
                        class="drag-data padding-lr"
                      >
                        <span class="data-area-label">{{ t('chart.switch_chart') }}</span>
                        <el-popover
                          :offset="4"
                          placement="bottom-end"
                          width="434"
                          trigger="click"
                          :append-to-body="true"
                          :popper-class="'chart-type-style-' + themes"
                          :persistent="false"
                        >
                          <template #reference>
                            <el-select
                              v-model="state.useless"
                              popper-class="chart-type-hide-options"
                              class="chart-type-select"
                              :suffix-icon="icon_down_outlined1"
                              :effect="themes"
                              size="small"
                            >
                              <template #prefix>
                                <Icon
                                  class-name="chart-type-select-icon"
                                  v-if="state.chartTypeOptions.length > 0 && state.chartTypeOptions[0]?.isPlugin"
                                  :static-content="state.chartTypeOptions[0]?.icon"
                                />
                                <Icon v-else-if="state.chartTypeOptions.length > 0 && state.chartTypeOptions[0]?.icon && iconChartMap[state.chartTypeOptions[0].icon]" class-name="chart-type-select-icon">
                                  <component
                                    class="svg-icon chart-type-select-icon"
                                    :is="iconChartMap[state.chartTypeOptions[0].icon]"
                                  ></component>
                                </Icon>
                              </template>
                              <template #default>
                                <el-option
                                  v-for="item in state.chartTypeOptions"
                                  :key="item?.value || item?.title"
                                  :label="item?.title"
                                  :value="item?.value"
                                />
                              </template>
                            </el-select>
                          </template>
                          <template #default>
                            <chart-type
                              :themes="themes"
                              :type="view.type"
                              @on-type-change="onTypeChange"
                            />
                          </template>
                        </el-popover>
                      </el-row>
                      <template v-if="view.plugin?.isPlugin">
                        <plugin-component
                          :jsname="view.plugin.staticMap['editor-data']"
                          :view="view"
                          :dimension="state.dimension"
                          :quota="state.quota"
                          :themes="themes"
                          :emitter="emitter"
                          @onDimensionItemChange="dimensionItemChange"
                          @onDimensionItemRemove="dimensionItemRemove"
                          @onNameEdit="showRename"
                          @onCustomSort="onCustomSort"
                          @valueFormatter="valueFormatter"
                        />
                      </template>
                      <template v-else>
                        <!--area-->
                        <el-row v-if="showAxis('area')" class="padding-lr drag-data">
                          <span class="data-area-label">
                            {{ t('chart.area') }}
                            <i class="required"></i>
                          </span>
                          <div class="area-tree-select">
                            <el-tree-select
                              ref="areaSelect"
                              v-model="state.areaId"
                              :effect="themes"
                              :data="state.worldTree"
                              :props="treeProps"
                              :filterNodeMethod="filterNode"
                              :current-node-key="state.areaId"
                              :teleported="false"
                              :default-expanded-keys="expandKeys"
                              empty-text="请选择区域"
                              node-key="id"
                              check-strictly
                              filterable
                              @node-click="onAreaChange"
                            />
                          </div>
                        </el-row>
                        <!--xAxis-->
                        <el-row v-if="showAxis('xAxis')" class="padding-lr drag-data">
                          <div class="form-draggable-title">
                            <span>
                              {{ chartViewInstance.axisConfig.xAxis.name }}
                              <i
                                v-if="!chartViewInstance.axisConfig.xAxis?.allowEmpty"
                                class="required"
                              ></i>
                            </span>
                            <el-tooltip
                              :effect="toolTip"
                              placement="top"
                              :content="t('common.delete')"
                            >
                              <el-icon
                                class="remove-icon"
                                :class="{ 'remove-icon--dark': themes === 'dark' }"
                                size="14px"
                                @click="removeItems('xAxis')"
                              >
                                <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                  ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                /></Icon>
                              </el-icon>
                            </el-tooltip>
                          </div>
                          <div
                            class="qw"
                            @drop="$event => drop($event)"
                            @dragenter="dragEnter"
                            @dragover="$event => dragOver($event)"
                          >
                            <draggable
                              :list="view.xAxis"
                              :move="onMove"
                              item-key="id"
                              group="drag"
                              animation="300"
                              class="drag-block-style"
                              :class="{ dark: themes === 'dark' }"
                              @add="addXaxis"
                            >
                              <template #item="{ element, index }">
                                <dimension-item
                                  :dimension-data="state.dimension"
                                  :quota-data="state.quota"
                                  :chart="view"
                                  :item="element"
                                  :index="index"
                                  :themes="props.themes"
                                  type="dimension"
                                  @onDimensionItemChange="dimensionItemChange"
                                  @onDimensionItemRemove="dimensionItemRemove"
                                  @onNameEdit="showRename"
                                  @onCustomSort="onCustomSort"
                                  @valueFormatter="valueFormatter"
                                  @onToggleHide="onToggleHide"
                                  @editSortPriority="editSortPriority"
                                />
                              </template>
                            </draggable>
                            <drag-placeholder :themes="themes" :drag-list="view.xAxis" />
                          </div>
                        </el-row>

                        <!--xAxisExt-->
                        <el-row v-if="showAxis('xAxisExt')" class="padding-lr drag-data">
                          <div class="form-draggable-title">
                            <span>
                              {{ chartViewInstance.axisConfig.xAxisExt.name }}
                              <i
                                v-if="!chartViewInstance.axisConfig.xAxisExt?.allowEmpty"
                                class="required"
                              ></i>
                            </span>
                            <el-tooltip
                              :effect="toolTip"
                              placement="top"
                              :content="t('common.delete')"
                            >
                              <el-icon
                                class="remove-icon"
                                :class="{ 'remove-icon--dark': themes === 'dark' }"
                                size="14px"
                                @click="removeItems('xAxisExt')"
                              >
                                <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                  ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                /></Icon>
                              </el-icon>
                            </el-tooltip>
                          </div>
                          <div
                            @drop="$event => drop($event, 'xAxisExt')"
                            @dragenter="dragEnter"
                            @dragover="$event => dragOver($event)"
                          >
                            <draggable
                              :list="view.xAxisExt"
                              :move="onMove"
                              item-key="id"
                              group="drag"
                              animation="300"
                              class="drag-block-style"
                              :class="{ dark: themes === 'dark' }"
                              @add="addXaxisExt"
                            >
                              <template #item="{ element, index }">
                                <dimension-item
                                  :dimension-data="state.dimension"
                                  :quota-data="state.quota"
                                  :chart="view"
                                  :item="element"
                                  :index="index"
                                  :themes="props.themes"
                                  type="dimensionExt"
                                  @onDimensionItemChange="dimensionItemChange"
                                  @onDimensionItemRemove="dimensionItemRemove"
                                  @onNameEdit="showRename"
                                  @onCustomSort="onExtCustomSort"
                                  @editSortPriority="editSortPriority"
                                />
                              </template>
                            </draggable>
                            <drag-placeholder :drag-list="view.xAxisExt" />
                          </div>
                        </el-row>

                        <!--flowMapStartName-->
                        <el-row v-if="showAxis('flowMapStartName')" class="padding-lr drag-data">
                          <div class="form-draggable-title">
                            <span>
                              {{ chartViewInstance.axisConfig.flowMapStartName.name }}
                              <i
                                v-if="!chartViewInstance.axisConfig.flowMapStartName?.allowEmpty"
                                class="required"
                              ></i>
                            </span>
                            <el-tooltip
                              :effect="toolTip"
                              placement="top"
                              :content="t('common.delete')"
                            >
                              <el-icon
                                class="remove-icon"
                                :class="{ 'remove-icon--dark': themes === 'dark' }"
                                size="14px"
                                @click="removeItems('flowMapStartName')"
                              >
                                <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                  ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                /></Icon>
                              </el-icon>
                            </el-tooltip>
                          </div>
                          <div
                            class="qw"
                            @drop="$event => drop($event, 'flowMapStartName')"
                            @dragenter="dragEnter"
                            @dragover="$event => dragOver($event)"
                          >
                            <draggable
                              :list="view.flowMapStartName"
                              :move="onMove"
                              item-key="id"
                              group="drag"
                              animation="300"
                              class="drag-block-style"
                              :class="{ dark: themes === 'dark' }"
                              @add="addFlowMapStartName"
                            >
                              <template #item="{ element, index }">
                                <dimension-item
                                  :dimension-data="state.dimension"
                                  :quota-data="state.quota"
                                  :chart="view"
                                  :item="element"
                                  :index="index"
                                  :themes="props.themes"
                                  type="flowMapStartName"
                                  @onDimensionItemChange="dimensionItemChange"
                                  @onDimensionItemRemove="dimensionItemRemove"
                                  @onNameEdit="showRename"
                                  @onCustomSort="onCustomFlowMapStartNameSort"
                                  @valueFormatter="valueFormatter"
                                  @editSortPriority="editSortPriority"
                                />
                              </template>
                            </draggable>
                            <drag-placeholder :themes="themes" :drag-list="view.flowMapStartName" />
                          </div>
                        </el-row>

                        <!--flowMapEndName-->
                        <el-row v-if="showAxis('flowMapEndName')" class="padding-lr drag-data">
                          <div class="form-draggable-title">
                            <span>
                              {{ chartViewInstance.axisConfig.flowMapEndName.name }}
                              <i
                                v-if="!chartViewInstance.axisConfig.flowMapEndName?.allowEmpty"
                                class="required"
                              ></i>
                            </span>
                            <el-tooltip
                              :effect="toolTip"
                              placement="top"
                              :content="t('common.delete')"
                            >
                              <el-icon
                                class="remove-icon"
                                :class="{ 'remove-icon--dark': themes === 'dark' }"
                                size="14px"
                                @click="removeItems('flowMapEndName')"
                              >
                                <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                  ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                /></Icon>
                              </el-icon>
                            </el-tooltip>
                          </div>
                          <div
                            class="qw"
                            @drop="$event => drop($event, 'flowMapEndName')"
                            @dragenter="dragEnter"
                            @dragover="$event => dragOver($event)"
                          >
                            <draggable
                              :list="view.flowMapEndName"
                              :move="onMove"
                              item-key="id"
                              group="drag"
                              animation="300"
                              class="drag-block-style"
                              :class="{ dark: themes === 'dark' }"
                              @add="addFlowMapEndName"
                            >
                              <template #item="{ element, index }">
                                <dimension-item
                                  :dimension-data="state.dimension"
                                  :quota-data="state.quota"
                                  :chart="view"
                                  :item="element"
                                  :index="index"
                                  :themes="props.themes"
                                  type="flowMapEndName"
                                  @onDimensionItemChange="dimensionItemChange"
                                  @onDimensionItemRemove="dimensionItemRemove"
                                  @onNameEdit="showRename"
                                  @onCustomSort="onCustomFlowMapEndNameSort"
                                  @valueFormatter="valueFormatter"
                                  @editSortPriority="editSortPriority"
                                />
                              </template>
                            </draggable>
                            <drag-placeholder :themes="themes" :drag-list="view.flowMapEndName" />
                          </div>
                        </el-row>

                        <!--extStack-->
                        <el-row v-if="showAxis('extStack')" class="padding-lr drag-data">
                          <div class="form-draggable-title">
                            <span>
                              {{ chartViewInstance.axisConfig.extStack.name }}
                              <i
                                v-if="!chartViewInstance.axisConfig.extStack?.allowEmpty"
                                class="required"
                              ></i>
                            </span>
                            <el-tooltip
                              :effect="toolTip"
                              placement="top"
                              :content="t('common.delete')"
                            >
                              <el-icon
                                class="remove-icon"
                                :class="{ 'remove-icon--dark': themes === 'dark' }"
                                size="14px"
                                @click="removeItems('extStack')"
                              >
                                <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                  ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                /></Icon>
                              </el-icon>
                            </el-tooltip>
                          </div>
                          <div
                            @drop="$event => drop($event, 'extStack')"
                            @dragenter="dragEnter"
                            @dragover="$event => dragOver($event)"
                          >
                            <draggable
                              :list="view.extStack"
                              :move="onMove"
                              item-key="id"
                              group="drag"
                              animation="300"
                              class="drag-block-style"
                              :class="{ dark: themes === 'dark' }"
                              @add="addExtStack"
                            >
                              <template #item="{ element, index }">
                                <dimension-item
                                  :dimension-data="state.dimension"
                                  :quota-data="state.quota"
                                  :chart="view"
                                  :item="element"
                                  :index="index"
                                  :themes="props.themes"
                                  type="dimensionStack"
                                  @onDimensionItemChange="dimensionItemChange"
                                  @onDimensionItemRemove="dimensionItemRemove"
                                  @onNameEdit="showRename"
                                  @onCustomSort="onStackCustomSort"
                                  @editSortPriority="editSortPriority"
                                />
                              </template>
                            </draggable>
                            <drag-placeholder :drag-list="view.extStack" />
                          </div>
                        </el-row>

                        <el-row v-if="showAxis('extColor')" class="padding-lr drag-data">
                          <div class="form-draggable-title">
                            <span>
                              {{ chartViewInstance.axisConfig.extColor.name }}
                              <i
                                v-if="!chartViewInstance.axisConfig.extColor?.allowEmpty"
                                class="required"
                              ></i>
                            </span>
                            <el-tooltip
                              :effect="toolTip"
                              placement="top"
                              :content="t('common.delete')"
                            >
                              <el-icon
                                class="remove-icon"
                                :class="{ 'remove-icon--dark': themes === 'dark' }"
                                size="14px"
                                @click="removeItems('extColor')"
                              >
                                <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                  ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                /></Icon>
                              </el-icon>
                            </el-tooltip>
                          </div>
                          <div
                            class="qw"
                            @drop="$event => drop($event, 'extColor')"
                            @dragenter="dragEnter"
                            @dragover="$event => dragOver($event)"
                          >
                            <draggable
                              :list="view.extColor"
                              :move="onMove"
                              item-key="id"
                              group="drag"
                              animation="300"
                              class="drag-block-style"
                              :class="{ dark: themes === 'dark' }"
                              @add="addExtColor"
                              @change="e => onAxisChange(e, 'extColor')"
                            >
                              <template #item="{ element, index }">
                                <dimension-item
                                  :dimension-data="state.dimension"
                                  :quota-data="state.quota"
                                  :chart="view"
                                  :item="element"
                                  :index="index"
                                  :themes="props.themes"
                                  type="extColor"
                                  @onDimensionItemChange="dimensionItemChange"
                                  @onDimensionItemRemove="dimensionItemRemove"
                                  @onNameEdit="showRename"
                                  @onCustomSort="onCustomExtColorSort"
                                  @valueFormatter="valueFormatter"
                                  @editSortPriority="editSortPriority"
                                />
                              </template>
                            </draggable>
                            <drag-placeholder :themes="themes" :drag-list="view.extColor" />
                          </div>
                        </el-row>

                        <template v-if="view.type !== 'bar-range'">
                          <!--yAxis-->
                          <el-row v-if="showAxis('yAxis')" class="padding-lr drag-data">
                            <div class="form-draggable-title">
                              <span class="data-area-label">
                                <span style="margin-right: 4px">
                                  {{ chartViewInstance.axisConfig.yAxis.name }}
                                  <i
                                    v-if="!chartViewInstance.axisConfig.yAxis?.allowEmpty"
                                    class="required"
                                  ></i>
                                </span>
                                <el-tooltip
                                  v-if="chartViewInstance.axisConfig.yAxis.tooltip"
                                  class="item"
                                  :effect="toolTip"
                                  placement="top"
                                >
                                  <template #content>
                                    <span> {{ chartViewInstance.axisConfig.yAxis.tooltip }}</span>
                                  </template>
                                  <el-icon
                                    class="hint-icon"
                                    :class="{ 'hint-icon--dark': themes === 'dark' }"
                                  >
                                    <Icon name="icon_info_outlined"
                                      ><icon_info_outlined class="svg-icon"
                                    /></Icon>
                                  </el-icon>
                                </el-tooltip>
                              </span>
                              <el-tooltip
                                :effect="toolTip"
                                placement="top"
                                :content="t('common.delete')"
                              >
                                <el-icon
                                  class="remove-icon"
                                  :class="{ 'remove-icon--dark': themes === 'dark' }"
                                  size="14px"
                                  @click="removeItems('yAxis')"
                                >
                                  <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                    ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                  /></Icon>
                                </el-icon>
                              </el-tooltip>
                            </div>
                            <div
                              @drop="$event => drop($event, 'yAxis')"
                              @dragenter="dragEnter"
                              @dragover="$event => dragOver($event)"
                            >
                              <draggable
                                :list="view.yAxis"
                                :move="onMove"
                                item-key="id"
                                group="drag"
                                animation="300"
                                class="drag-block-style"
                                :class="{ dark: themes === 'dark' }"
                                @add="addYaxis"
                                @change="e => onAxisChange(e, 'yAxis')"
                              >
                                <template #item="{ element, index }">
                                  <quota-item
                                    :dimension-data="state.dimension"
                                    :quota-data="state.quota"
                                    :chart="view"
                                    :item="element"
                                    :index="index"
                                    type="quota"
                                    :themes="props.themes"
                                    @onQuotaItemChange="item => quotaItemChange(item, 'yAxis')"
                                    @onQuotaItemRemove="quotaItemRemove"
                                    @onNameEdit="showRename"
                                    @editItemFilter="showQuotaEditFilter"
                                    @editItemCompare="showQuotaEditCompare"
                                    @valueFormatter="valueFormatter"
                                    @onToggleHide="onToggleHide"
                                    @editSortPriority="editSortPriority"
                                  />
                                </template>
                              </draggable>
                              <drag-placeholder
                                :margin-top="view.type === 'stock-line' ? '9px' : '0'"
                                :drag-list="view.yAxis"
                              />
                            </div>
                          </el-row>
                          <!-- xAxisExtRight -->
                          <el-row v-if="showAxis('xAxisExtRight')" class="padding-lr drag-data">
                            <div class="form-draggable-title">
                              <span>
                                {{ chartViewInstance.axisConfig.extBubble.name }}
                                <i
                                  v-if="!chartViewInstance.axisConfig.extBubble?.allowEmpty"
                                  class="required"
                                ></i>
                              </span>
                              <el-tooltip
                                :effect="toolTip"
                                placement="top"
                                :content="t('common.delete')"
                              >
                                <el-icon
                                  class="remove-icon"
                                  :class="{ 'remove-icon--dark': themes === 'dark' }"
                                  size="14px"
                                  @click="removeItems('extBubble')"
                                >
                                  <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                    ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                  /></Icon>
                                </el-icon>
                              </el-tooltip>
                            </div>
                            <div
                              @drop="$event => drop($event, 'extBubble')"
                              @dragenter="dragEnter"
                              @dragover="$event => dragOver($event)"
                            >
                              <draggable
                                :list="view.extBubble"
                                :move="onMove"
                                item-key="id"
                                group="drag"
                                animation="300"
                                class="drag-block-style"
                                :class="{ dark: themes === 'dark' }"
                                @add="addExtBubble"
                                @change="e => onAxisChange(e, 'extBubble')"
                              >
                                <template #item="{ element, index }">
                                  <dimension-item
                                    :dimension-data="state.dimension"
                                    :quota-data="state.quota"
                                    :chart="view"
                                    :item="element"
                                    :index="index"
                                    :themes="props.themes"
                                    type="xAxisExtRight"
                                    @onDimensionItemChange="dimensionItemChange"
                                    @onDimensionItemRemove="dimensionItemRemove"
                                    @onNameEdit="showRename"
                                    @onCustomSort="onExtCustomRightSort"
                                    @editSortPriority="editSortPriority"
                                  />
                                </template>
                              </draggable>
                              <drag-placeholder :drag-list="view.extBubble" />
                            </div>
                          </el-row>
                          <!--yAxisExt-->
                          <el-row v-if="showAxis('yAxisExt')" class="padding-lr drag-data">
                            <div class="form-draggable-title">
                              <span>
                                {{ chartViewInstance.axisConfig.yAxisExt.name }}
                                <i
                                  v-if="!chartViewInstance.axisConfig.yAxisExt?.allowEmpty"
                                  class="required"
                                ></i>
                              </span>
                              <el-tooltip
                                :effect="toolTip"
                                placement="top"
                                :content="t('common.delete')"
                              >
                                <el-icon
                                  class="remove-icon"
                                  :class="{ 'remove-icon--dark': themes === 'dark' }"
                                  size="14px"
                                  @click="removeItems('yAxisExt')"
                                >
                                  <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                    ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                  /></Icon>
                                </el-icon>
                              </el-tooltip>
                            </div>
                            <div
                              @drop="$event => drop($event, 'yAxisExt')"
                              @dragenter="dragEnter"
                              @dragover="$event => dragOver($event)"
                            >
                              <draggable
                                :list="view.yAxisExt"
                                :move="onMove"
                                item-key="id"
                                group="drag"
                                animation="300"
                                class="drag-block-style"
                                :class="{ dark: themes === 'dark' }"
                                @add="addYaxisExt"
                                @change="e => onAxisChange(e, 'yAxisExt')"
                              >
                                <template #item="{ element, index }">
                                  <quota-item
                                    :dimension-data="state.dimension"
                                    :quota-data="state.quota"
                                    :chart="view"
                                    :item="element"
                                    :index="index"
                                    type="quotaExt"
                                    :themes="props.themes"
                                    @onQuotaItemChange="item => quotaItemChange(item, 'yAxisExt')"
                                    @onQuotaItemRemove="quotaItemRemove"
                                    @onNameEdit="showRename"
                                    @editItemFilter="showQuotaEditFilter"
                                    @editItemCompare="showQuotaEditCompare"
                                    @valueFormatter="valueFormatter"
                                    @editSortPriority="editSortPriority"
                                  />
                                </template>
                              </draggable>
                              <drag-placeholder :drag-list="view.yAxisExt" />
                            </div>
                          </el-row>
                        </template>
                        <template v-else-if="view.type === 'bar-range'">
                          <!--yAxis-->
                          <el-row v-if="showAxis('yAxis')" class="padding-lr drag-data">
                            <div class="form-draggable-title">
                              <span>
                                {{ chartViewInstance.axisConfig.yAxis.name }}
                                <i
                                  v-if="!chartViewInstance.axisConfig.yAxis?.allowEmpty"
                                  class="required"
                                ></i>
                              </span>
                              <el-tooltip
                                :effect="toolTip"
                                placement="top"
                                :content="t('common.delete')"
                              >
                                <el-icon
                                  class="remove-icon"
                                  :class="{ 'remove-icon--dark': themes === 'dark' }"
                                  size="14px"
                                  @click="removeItems('yAxis')"
                                >
                                  <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                    ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                  /></Icon>
                                </el-icon>
                              </el-tooltip>
                            </div>
                            <div
                              @drop="$event => drop($event, 'yAxis')"
                              @dragenter="dragEnter"
                              @dragover="$event => dragOver($event)"
                            >
                              <draggable
                                :list="view.yAxis"
                                :move="onMove"
                                item-key="id"
                                group="drag"
                                animation="300"
                                class="drag-block-style"
                                :class="{ dark: themes === 'dark' }"
                                @add="addYaxis"
                                @change="e => onAxisChange(e, 'yAxis')"
                              >
                                <template #item="{ element, index }">
                                  <dimension-item
                                    v-if="element.groupType === 'd'"
                                    :dimension-data="state.dimension"
                                    :quota-data="state.quota"
                                    :chart="view"
                                    :item="element"
                                    :index="index"
                                    :themes="props.themes"
                                    type="quota"
                                    @onDimensionItemChange="dimensionItemChange"
                                    @onDimensionItemRemove="dimensionItemRemove"
                                    @onNameEdit="showRename"
                                    @onCustomSort="onExtCustomSort"
                                    @editSortPriority="editSortPriority"
                                  />
                                  <quota-item
                                    v-else-if="element.groupType === 'q'"
                                    :dimension-data="state.dimension"
                                    :quota-data="state.quota"
                                    :chart="view"
                                    :item="element"
                                    :index="index"
                                    type="quota"
                                    :themes="props.themes"
                                    @onQuotaItemChange="item => quotaItemChange(item, 'yAxis')"
                                    @onQuotaItemRemove="quotaItemRemove"
                                    @onNameEdit="showRename"
                                    @editItemFilter="showQuotaEditFilter"
                                    @editItemCompare="showQuotaEditCompare"
                                    @valueFormatter="valueFormatter"
                                    @editSortPriority="editSortPriority"
                                  />
                                </template>
                              </draggable>
                              <drag-placeholder :drag-list="view.yAxis" />
                            </div>
                          </el-row>
                          <!--yAxisExt-->
                          <el-row v-if="showAxis('yAxisExt')" class="padding-lr drag-data">
                            <div class="form-draggable-title">
                              <span>
                                {{ chartViewInstance.axisConfig.yAxisExt.name }}
                                <i
                                  v-if="!chartViewInstance.axisConfig.yAxisExt?.allowEmpty"
                                  class="required"
                                ></i>
                              </span>
                              <el-tooltip
                                :effect="toolTip"
                                placement="top"
                                :content="t('common.delete')"
                              >
                                <el-icon
                                  class="remove-icon"
                                  :class="{ 'remove-icon--dark': themes === 'dark' }"
                                  size="14px"
                                  @click="removeItems('yAxisExt')"
                                >
                                  <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                    ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                  /></Icon>
                                </el-icon>
                              </el-tooltip>
                            </div>
                            <div
                              @drop="$event => drop($event, 'yAxisExt')"
                              @dragenter="dragEnter"
                              @dragover="$event => dragOver($event)"
                            >
                              <draggable
                                :list="view.yAxisExt"
                                :move="onMove"
                                item-key="id"
                                group="drag"
                                animation="300"
                                class="drag-block-style"
                                :class="{ dark: themes === 'dark' }"
                                @add="addYaxisExt"
                                @change="e => onAxisChange(e, 'yAxisExt')"
                              >
                                <template #item="{ element, index }">
                                  <dimension-item
                                    v-if="element.groupType === 'd'"
                                    :dimension-data="state.dimension"
                                    :quota-data="state.quota"
                                    :chart="view"
                                    :item="element"
                                    :index="index"
                                    :themes="props.themes"
                                    type="quotaExt"
                                    @onDimensionItemChange="dimensionItemChange"
                                    @onDimensionItemRemove="dimensionItemRemove"
                                    @onNameEdit="showRename"
                                    @onCustomSort="onExtCustomSort"
                                    @editSortPriority="editSortPriority"
                                  />
                                  <quota-item
                                    v-else-if="element.groupType === 'q'"
                                    :dimension-data="state.dimension"
                                    :quota-data="state.quota"
                                    :chart="view"
                                    :item="element"
                                    :index="index"
                                    type="quotaExt"
                                    :themes="props.themes"
                                    @onQuotaItemChange="item => quotaItemChange(item, 'yAxisExt')"
                                    @onQuotaItemRemove="quotaItemRemove"
                                    @onNameEdit="showRename"
                                    @editItemFilter="showQuotaEditFilter"
                                    @editItemCompare="showQuotaEditCompare"
                                    @valueFormatter="valueFormatter"
                                    @editSortPriority="editSortPriority"
                                  />
                                </template>
                              </draggable>
                              <drag-placeholder :drag-list="view.yAxisExt" />
                            </div>
                          </el-row>
                        </template>
                        <!-- extBubble -->
                        <el-row v-if="showAxis('extBubble')" class="padding-lr drag-data">
                          <div class="form-draggable-title">
                            <span class="data-area-label">
                              <span style="margin-right: 4px">
                                {{ chartViewInstance.axisConfig.extBubble.name }}
                                <i
                                  v-if="!chartViewInstance.axisConfig.extBubble?.allowEmpty"
                                  class="required"
                                ></i>
                              </span>
                              <el-tooltip
                                v-if="chartViewInstance.axisConfig.extBubble.tooltip"
                                class="item"
                                :effect="toolTip"
                                placement="top"
                              >
                                <template #content>
                                  <span> {{ chartViewInstance.axisConfig.extBubble.tooltip }}</span>
                                </template>
                                <el-icon
                                  class="hint-icon"
                                  :class="{ 'hint-icon--dark': themes === 'dark' }"
                                >
                                  <Icon name="icon_info_outlined"
                                    ><icon_info_outlined class="svg-icon"
                                  /></Icon>
                                </el-icon>
                              </el-tooltip>
                            </span>
                            <el-tooltip
                              :effect="toolTip"
                              placement="top"
                              :content="t('common.delete')"
                            >
                              <el-icon
                                class="remove-icon"
                                :class="{ 'remove-icon--dark': themes === 'dark' }"
                                size="14px"
                                @click="removeItems('extBubble')"
                              >
                                <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                  ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                /></Icon>
                              </el-icon>
                            </el-tooltip>
                          </div>
                          <div
                            @drop="$event => drop($event, 'extBubble')"
                            @dragenter="dragEnter"
                            @dragover="$event => dragOver($event)"
                          >
                            <draggable
                              :list="view.extBubble"
                              :move="onMove"
                              item-key="id"
                              group="drag"
                              animation="300"
                              class="drag-block-style"
                              :class="{ dark: themes === 'dark' }"
                              @add="addExtBubble"
                              @change="e => onAxisChange(e, 'extBubble')"
                            >
                              <template #item="{ element, index }">
                                <quota-item
                                  :dimension-data="state.dimension"
                                  :quota-data="state.quota"
                                  :chart="view"
                                  :item="element"
                                  :index="index"
                                  type="extBubble"
                                  :themes="props.themes"
                                  @onQuotaItemChange="item => quotaItemChange(item, 'extBubble')"
                                  @onQuotaItemRemove="quotaItemRemove"
                                  @onNameEdit="showRename"
                                  @editItemFilter="showQuotaEditFilter"
                                  @editItemCompare="showQuotaEditCompare"
                                  @valueFormatter="valueFormatter"
                                  @editSortPriority="editSortPriority"
                                />
                              </template>
                            </draggable>
                            <drag-placeholder :drag-list="view.extBubble" />
                          </div>
                        </el-row>

                        <!--drill-->
                        <el-row v-if="showAxis('drill')" class="padding-lr drag-data">
                          <div class="form-draggable-title">
                            <span class="data-area-label">
                              <span style="margin-right: 4px">
                                {{ t('chart.drill') }} / {{ t('chart.dimension') }}
                              </span>
                              <el-tooltip class="item" :effect="toolTip" placement="top">
                                <template #content>
                                  <span> {{ t('chart.drill_dimension_tip') }}</span>
                                </template>
                                <el-icon
                                  class="hint-icon"
                                  :class="{ 'hint-icon--dark': themes === 'dark' }"
                                >
                                  <Icon name="icon_info_outlined"
                                    ><icon_info_outlined class="svg-icon"
                                  /></Icon>
                                </el-icon>
                              </el-tooltip>
                            </span>
                            <el-tooltip
                              :effect="toolTip"
                              placement="top"
                              :content="t('common.delete')"
                            >
                              <el-icon
                                class="remove-icon"
                                :class="{ 'remove-icon--dark': themes === 'dark' }"
                                size="14px"
                                @click="removeItems('drillFields')"
                              >
                                <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                  ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                /></Icon>
                              </el-icon>
                            </el-tooltip>
                          </div>
                          <div
                            @drop="$event => drop($event, 'drillFields')"
                            @dragenter="dragEnter"
                            @dragover="$event => dragOver($event)"
                          >
                            <draggable
                              :list="view.drillFields"
                              item-key="id"
                              group="drag"
                              animation="300"
                              :move="onMove"
                              class="drag-block-style"
                              :class="{ dark: themes === 'dark' }"
                              @add="addDrill"
                            >
                              <template #item="{ element, index }">
                                <drill-item
                                  :key="element.id"
                                  :index="index"
                                  :chart="view"
                                  :item="element"
                                  :dimension-data="state.dimension"
                                  :quota-data="state.quota"
                                  :themes="props.themes"
                                  @onDimensionItemChange="drillItemChange"
                                  @onDimensionItemRemove="drillItemRemove"
                                  @onNameEdit="showRename"
                                  @onCustomSort="onDrillCustomSort"
                                  @editSortPriority="editSortPriority"
                                />
                              </template>
                            </draggable>
                            <drag-placeholder :drag-list="view.drillFields" />
                          </div>
                        </el-row>

                        <!--filter-->
                        <el-row class="padding-lr drag-data no-top-border no-top-padding">
                          <div class="form-draggable-title">
                            <span>
                              {{ t('chart.result_filter') }}
                            </span>
                            <el-tooltip
                              :effect="toolTip"
                              placement="top"
                              :content="t('common.delete')"
                            >
                              <el-icon
                                class="remove-icon"
                                :class="{ 'remove-icon--dark': themes === 'dark' }"
                                size="14px"
                                @click="removeItems('customFilter')"
                              >
                                <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                                  ><icon_deleteTrash_outlined class="svg-icon inner-class"
                                /></Icon>
                              </el-icon>
                            </el-tooltip>
                          </div>

                          <div
                            class="tree-btn"
                            v-if="isFilterActive || themes === 'dark'"
                            :class="{ 'tree-btn--dark': themes === 'dark', active: isFilterActive }"
                            @click="openTreeFilter"
                          >
                            <el-icon style="margin-right: 2px; font-size: 12px">
                              <Icon class="svg-background" name="icon-filter"
                                ><iconFilter class="svg-icon svg-background"
                              /></Icon>
                            </el-icon>

                            <span>{{ $t('chart.filter') }}</span>
                          </div>
                          <el-button
                            v-else
                            class="tree-btn_secondary"
                            secondary
                            @click="openTreeFilter"
                          >
                            <template #icon>
                              <Icon><iconFilter class="svg-icon svg-background" /></Icon>
                            </template>
                            <span>{{ $t('chart.filter') }}</span>
                          </el-button>
                        </el-row>

                        <el-row v-if="showAggregate" class="refresh-area">
                          <el-form-item
                            class="form-item no-margin-bottom"
                            :class="'form-item-' + themes"
                          >
                            <el-checkbox
                              v-model="view.aggregate"
                              :effect="themes"
                              size="small"
                              @change="aggregateChange"
                            >
                              {{ t('chart.aggregate_time') }}
                            </el-checkbox>
                          </el-form-item>
                        </el-row>
                      </template>
                    </el-scrollbar>
                    <el-footer :class="{ 'refresh-active-footer': view.refreshViewEnable }">
                      <el-row class="refresh-area">
                        <el-form-item
                          class="form-item no-margin-bottom"
                          :class="'form-item-' + themes"
                        >
                          <el-checkbox
                            v-model="view.refreshViewEnable"
                            :effect="themes"
                            size="small"
                            @change="recordSnapshotInfo('render')"
                          >
                            {{ t('visualization.refresh_frequency') }}
                          </el-checkbox>
                        </el-form-item>
                        <el-row v-if="view.refreshViewEnable">
                          <el-form-item
                            class="form-item no-margin-bottom select-append"
                            :class="'form-item-' + themes"
                          >
                            <el-input
                              v-model.number="view.refreshTime"
                              :effect="themes"
                              :class="[themes === 'dark' && 'dv-dark']"
                              size="small"
                              :min="1"
                              :max="3600"
                              :disabled="!view.refreshViewEnable"
                              @change="onRefreshChange"
                            >
                              <template #append>
                                <el-select
                                  v-model="view.refreshUnit"
                                  :effect="themes"
                                  size="small"
                                  placeholder="Select"
                                  style="width: 80px"
                                  @change="recordSnapshotInfo('render')"
                                >
                                  <el-option
                                    :effect="themes"
                                    :label="t('visualization.minute')"
                                    :value="'minute'"
                                  />
                                  <el-option
                                    :effect="themes"
                                    :label="t('visualization.second')"
                                    :value="'second'"
                                  />
                                </el-select>
                              </template>
                            </el-input>
                          </el-form-item>
                        </el-row>
                      </el-row>
                      <el-row class="result-style" :class="'result-style-' + themes">
                        <div class="result-style-input">
                          <span v-if="view.type !== 'richTextView'">
                            {{ t('chart.result_count') }}
                          </span>
                          <span style="padding-left: 8px" v-if="view.type !== 'richTextView'">
                            <el-radio-group
                              v-model="view.resultMode"
                              :effect="themes"
                              class="radio-span"
                              size="small"
                              @change="recordSnapshotInfo('render')"
                            >
                              <el-radio class="margin20-radio" label="all" :effect="themes">
                                <span
                                  class="result-count-label"
                                  :class="{ dark: themes === 'dark' }"
                                >
                                  {{ t('chart.result_mode_all') }}
                                </span>
                              </el-radio>
                              <el-radio label="custom" :effect="themes">
                                <el-input-number
                                  v-model="view.resultCount"
                                  :min="1"
                                  :max="1000000"
                                  :controls="false"
                                  :effect="themes"
                                  :step-strictly="true"
                                  :value-on-clear="1000"
                                  :disabled="view.resultMode === 'all'"
                                  class="result-count"
                                  size="small"
                                  @change="recordSnapshotInfo('calcData')"
                                />
                              </el-radio>
                            </el-radio-group>
                          </span>
                        </div>

                        <el-button
                          :disabled="disableUpdate"
                          type="primary"
                          class="result-style-button"
                          @click="updateChartData(view)"
                        >
                          <span style="font-size: 12px">
                            {{ t('chart.update_chart_data') }}
                          </span>
                        </el-button>
                      </el-row>
                    </el-footer>
                  </el-container>
                </el-tab-pane>

                <el-tab-pane
                  name="style"
                  :label="t('chart.chart_style')"
                  class="padding-tab"
                  style="width: 100%"
                >
                  <el-container direction="vertical">
                    <el-scrollbar class="drag_main_area">
                      <template v-if="view.plugin?.isPlugin">
                        <plugin-component
                          :jsname="view.plugin.staticMap['editor-style']"
                          :view="view"
                          :dimension="state.dimension"
                          :quota="state.quota"
                          :themes="themes"
                          :emitter="emitter"
                        />
                      </template>
                      <template v-else>
                        <chart-style
                          v-if="chartStyleShow && chartViewInstance"
                          :properties="chartViewInstance?.properties"
                          :property-inner-all="chartViewInstance?.propertyInner"
                          :selector-spec="chartViewInstance?.selectorSpec"
                          :common-background-pop="curComponent?.commonBackground"
                          :common-border-pop="curComponent?.style"
                          :event-info="curComponent?.events"
                          :chart="view"
                          :themes="themes"
                          :dimension-data="state.dimension"
                          :quota-data="state.quota"
                          :all-fields="allFields"
                          @onColorChange="onColorChange"
                          @onMiscChange="onMiscChange"
                          @onLabelChange="onLabelChange"
                          @onTooltipChange="onTooltipChange"
                          @onChangeXAxisForm="onChangeXAxisForm"
                          @onChangeYAxisForm="onChangeYAxisForm"
                          @onChangeYAxisExtForm="onChangeYAxisExtForm"
                          @onTextChange="onTextChange"
                          @onIndicatorChange="onIndicatorChange"
                          @onIndicatorNameChange="onIndicatorNameChange"
                          @onLegendChange="onLegendChange"
                          @onBackgroundChange="onBackgroundChange"
                          @onStyleAttrChange="onStyleAttrChange"
                          @onBasicStyleChange="onBasicStyleChange"
                          @onTableHeaderChange="onTableHeaderChange"
                          @onTableCellChange="onTableCellChange"
                          @onTableTotalChange="onTableTotalChange"
                          @onChangeMiscStyleForm="onChangeMiscStyleForm"
                          @onExtTooltipChange="onExtTooltipChange"
                          @onChangeQuadrantForm="onChangeQuadrantForm"
                          @onChangeFlowMapLineForm="onChangeFlowMapLineForm"
                          @onChangeFlowMapPointForm="onChangeFlowMapPointForm"
                        />
                      </template>
                    </el-scrollbar>
                  </el-container>
                </el-tab-pane>

                <el-tab-pane
                  name="senior"
                  :label="t('chart.senior')"
                  class="padding-tab"
                  style="width: 100%"
                >
                  <el-container direction="vertical">
                    <el-scrollbar class="drag_main_area">
                      <template v-if="view.plugin?.isPlugin">
                        <plugin-component
                          :jsname="view.plugin.staticMap['editor-senior']"
                          :view="view"
                          :dimension="state.dimension"
                          :quota="state.quota"
                          :themes="themes"
                          :emitter="emitter"
                        />
                      </template>
                      <template v-else>
                        <senior
                          v-if="chartViewInstance"
                          :chart="view"
                          :quota-data="view.yAxis"
                          :quota-ext-data="view.yAxisExt"
                          :fields-data="allFields"
                          :themes="themes"
                          :properties="chartViewInstance?.properties"
                          :property-inner-all="chartViewInstance?.propertyInner"
                          :event-info="curComponent?.events"
                          @onFunctionCfgChange="onFunctionCfgChange"
                          @onAssistLineChange="onAssistLineChange"
                          @onScrollCfgChange="onScrollCfgChange"
                          @onThresholdChange="onThresholdChange"
                          @onMapMappingChange="onMapMappingChange"
                          @onBubbleAnimateChange="onBubbleAnimateChange"
                        />
                      </template>
                    </el-scrollbar>
                  </el-container>
                </el-tab-pane>
              </el-tabs>
            </el-row>
          </div>
        </div>
        <div
          class="dataset-main content-area"
          :class="{
            'content-area-close': canvasCollapse.datasetAreaCollapse,
            'content-area-right-open': !canvasCollapse.datasetAreaCollapse
          }"
        >
          <el-icon
            :title="$t('visualization.dataset')"
            class="custom-icon"
            size="20px"
            @click="collapseChange('datasetAreaCollapse')"
          >
            <Fold v-if="canvasCollapse.datasetAreaCollapse" class="collapse-icon" />
            <Expand v-else class="collapse-icon" />
          </el-icon>
          <div v-if="canvasCollapse.datasetAreaCollapse" class="collapse-title">
            <span style="font-size: 14px">{{ t('visualization.dataset') }}</span>
          </div>
          <el-container
            v-if="!canvasCollapse.datasetAreaCollapse"
            direction="vertical"
            class="dataset-area view-panel-row"
          >
            <el-header class="editor-title">
              <span style="font-size: 14px">{{ t('visualization.dataset') }}</span>
            </el-header>
            <el-main class="dataset-main-top">
              <!-- 数据源类型选择 -->
              <!-- <el-row class="data-source-type-select" style="margin-bottom: 8px">
                <el-radio-group
                  v-model="state.datasourceType"
                  size="small"
                  @change="toggleDataSourceType"
                >
                  <el-radio-button label="dataset">数据集</el-radio-button>
                  <el-radio-button label="rest">REST接口</el-radio-button>
                </el-radio-group>
              </el-row> -->

              <!-- 数据集选择 -->
              <!-- <el-row v-if="state.datasourceType === 'dataset'" class="dataset-select">
                <dataset-select
                  ref="datasetSelector"
                  v-model="view.tableId"
                  style="flex: 1"
                  :view-id="view.id"
                  :state-obj="state"
                  :themes="themes"
                  @add-ds-window="addDsWindow"
                  @on-dataset-change="changeDataset"
                />
                <el-tooltip
                  :effect="toolTip"
                  :content="$t('deDataset.edit_dataset')"
                  placement="top"
                >
                  <el-icon
                    v-if="curDatasetWeight >= 7 && !isDataEaseBi"
                    class="field-search-icon-btn"
                    :class="{ dark: themes === 'dark' }"
                    style="margin-left: 8px"
                    @click="editDs"
                  >
                    <Icon name="icon_edit_outlined" class="el-icon-arrow-down el-icon-delete"
                      ><icon_edit_outlined class="svg-icon el-icon-arrow-down el-icon-delete"
                    /></Icon>
                  </el-icon>
                </el-tooltip>
              </el-row> -->

              <!-- REST数据源配置 -->
              <el-row class="rest-config" style="margin-bottom: 8px">
                <el-button type="primary" size="small" style="width: 100%" @click="openRestConfig">
                  {{ state.restConfig ? '编辑REST配置' : '配置REST接口' }}
                </el-button>
              </el-row>
              <el-row class="dataset-search padding-lr">
                <div class="dataset-search-label" :class="{ dark: themes === 'dark' }">
                  <span>{{ t('chart.field') }}</span>
                  <span>
                    <el-tooltip
                      :effect="toolTip"
                      :content="$t('visualization.refresh')"
                      placement="top"
                    >
                      <el-icon
                        class="field-search-icon-btn"
                        :class="{ dark: themes === 'dark' }"
                        @click="getFields(view.tableId, view.id, view.type)"
                      >
                        <Icon name="icon_refresh_outlined" class="el-icon-arrow-down el-icon-delete"
                          ><icon_refresh_outlined
                            class="svg-icon el-icon-arrow-down el-icon-delete"
                        /></Icon>
                      </el-icon>
                    </el-tooltip>
                    <el-icon
                      v-if="false"
                      class="field-search-icon-btn"
                      :class="{ dark: themes === 'dark' }"
                      @click="addCalcField('d')"
                    >
                      <Icon name="icon_add_outlined" class="el-icon-arrow-down el-icon-delete"
                        ><icon_add_outlined class="svg-icon el-icon-arrow-down el-icon-delete"
                      /></Icon>
                    </el-icon>
                  </span>
                </div>
                <el-input
                  v-model="state.searchField"
                  size="middle"
                  :effect="themes"
                  class="dataset-search-input"
                  :class="{ dark: themes === 'dark' }"
                  :placeholder="t('chart.search') + ' ' + t('chart.field')"
                  clearable
                >
                  <template #prefix>
                    <el-icon class="el-input__icon">
                      <Icon name="icon_search-outline_outlined"
                        ><icon_searchOutline_outlined class="svg-icon"
                      /></Icon>
                    </el-icon>
                  </template>
                </el-input>
              </el-row>
              <div
                ref="elDrag"
                v-loading="fieldLoading && !fullscreenFlag"
                style="height: calc(100% - 137px); min-height: 120px"
              >
                <div
                  class="padding-lr field-height first right-dimension"
                  :class="{ dark: themes === 'dark', 'user-select': isDragging }"
                  :style="{
                    height: fieldDHeight + 'px'
                  }"
                >
                  <label>{{ t('chart.dimension') }}</label>
                  <el-scrollbar class="drag-list">
                    <div
                      v-for="element in dimensionData"
                      :key="element.id"
                      :draggable="true"
                      class="item father"
                      @click.ctrl="setActiveCtrl(element)"
                      @click.meta="setActiveCtrl(element)"
                      @click.exact="setActive(element)"
                      @click.shift="setActiveShift(element)"
                      @dragstart="$event => singleDragStartD($event, element, 'dimension')"
                      @dragend="singleDragEnd"
                    >
                      <div
                        class="items flex-align-center"
                        :class="[
                          'item-dimension--' + themes,
                          isDraggingItem && 'is-dragging-item',
                          activeDimension.map(itx => itx.id).includes(element.id) && 'active'
                        ]"
                      >
                        <el-icon>
                          <Icon
                            ><component
                              class="svg-icon"
                              :class="`field-icon-${
                                fieldType[[2, 3].includes(element.deType) ? 2 : 0]
                              }`"
                              :is="getIconName(element.deType, element.extField)"
                            ></component
                          ></Icon>
                        </el-icon>
                        <span
                          class="field-name ellipsis"
                          :class="{ dark: themes === 'dark' }"
                          :title="element.name"
                          >{{ element.name }}</span
                        >
                        <el-icon
                          v-if="element.id !== '-1' && !element.chartId"
                          class="field-setting child"
                          :class="{ 'remove-icon--dark': themes === 'dark' }"
                          size="14px"
                          @click.stop="copyChartFieldItem(element.id)"
                        >
                          <Icon class-name="inner-class" name="icon_copy_outlined"
                            ><icon_copy_outlined class="svg-icon inner-class"
                          /></Icon>
                        </el-icon>
                        <el-icon
                          v-if="element.id !== '-1' && element.chartId"
                          class="field-setting child"
                          :class="{ 'remove-icon--dark': themes === 'dark' }"
                          size="14px"
                          @click.stop="deleteChartFieldItem(element.id)"
                        >
                          <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                            ><icon_deleteTrash_outlined class="svg-icon inner-class"
                          /></Icon>
                        </el-icon>
                        <el-dropdown
                          v-if="element.id !== '-1' && false"
                          :effect="props.themes"
                          placement="right-start"
                          trigger="click"
                          size="small"
                          class="field-setting child"
                          @command="chartFieldEdit"
                        >
                          <span class="el-dropdown-link">
                            <el-icon class="icon-setting"><Setting /></el-icon>
                          </span>
                          <template #dropdown>
                            <el-dropdown-menu :effect="props.themes">
                              <el-dropdown-item :command="handleChartFieldEdit(element, 'copy')">
                                {{ t('common.copy') }}
                              </el-dropdown-item>
                              <span v-if="element.chartId">
                                <el-dropdown-item :command="handleChartFieldEdit(element, 'edit')">
                                  {{ t('common.edit') }}
                                </el-dropdown-item>
                                <el-dropdown-item
                                  :command="handleChartFieldEdit(element, 'delete')"
                                >
                                  {{ t('common.delete') }}
                                </el-dropdown-item>
                              </span>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                      <div
                        v-if="activeDimension.map(itx => itx.id).includes(element.id)"
                        :draggable="true"
                        class="shadow"
                        :class="isDraggingItem && 'is-dragging'"
                        @dragstart="dragStartD"
                        @dragend="dragEnd"
                      >
                        <template v-if="isDrag">
                          <div
                            v-for="ele in activeDimension"
                            :key="ele.id"
                            class="items flex-align-center"
                          >
                            <el-icon>
                              <Icon
                                ><component
                                  class="svg-icon"
                                  :class="`field-icon-${
                                    fieldType[[2, 3].includes(ele.deType) ? 2 : 0]
                                  }`"
                                  :is="iconFieldMap[fieldType[ele.deType]]"
                                ></component
                              ></Icon>
                            </el-icon>
                            <span
                              class="field-name ellipsis"
                              :class="{ dark: themes === 'dark' }"
                              >{{ ele.name }}</span
                            >
                            <el-dropdown
                              v-if="ele.id !== '-1' && false"
                              :effect="props.themes"
                              placement="right-start"
                              trigger="click"
                              size="small"
                              class="field-setting child"
                              @command="chartFieldEdit"
                            >
                              <span class="el-dropdown-link">
                                <el-icon class="icon-setting"><Setting /></el-icon>
                              </span>
                              <template #dropdown>
                                <el-dropdown-menu :effect="props.themes">
                                  <el-dropdown-item :command="handleChartFieldEdit(ele, 'copy')">
                                    {{ t('common.copy') }}
                                  </el-dropdown-item>
                                  <span v-if="ele.chartId">
                                    <el-dropdown-item :command="handleChartFieldEdit(ele, 'edit')">
                                      {{ t('common.edit') }}
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                      :command="handleChartFieldEdit(ele, 'delete')"
                                    >
                                      {{ t('common.delete') }}
                                    </el-dropdown-item>
                                  </span>
                                </el-dropdown-menu>
                              </template>
                            </el-dropdown>
                          </div>
                        </template>
                      </div>
                    </div>
                  </el-scrollbar>
                  <div
                    ref="el"
                    :style="{
                      top: dragVerticalTop + 'px'
                    }"
                    :class="['drag-vertical', isDragging && 'is-hovering']"
                  ></div>
                </div>
                <div
                  class="padding-lr field-height right-dimension"
                  :class="{ dark: themes === 'dark' }"
                >
                  <div class="divider"></div>
                  <label>{{ t('chart.quota') }}</label>
                  <el-scrollbar class="drag-list">
                    <div
                      v-for="element in quotaData"
                      :key="element.id"
                      class="item father"
                      :draggable="true"
                      @click.ctrl="setActiveCtrl(element, 'quota')"
                      @click.meta="setActiveCtrl(element, 'quota')"
                      @click.exact="setActive(element, 'quota')"
                      @click.shift="setActiveShift(element, 'quota')"
                      @dragstart="$event => singleDragStart($event, element, 'quota')"
                      @dragend="singleDragEnd"
                    >
                      <div
                        class="items flex-align-center"
                        :class="[
                          'item-dimension--' + themes,
                          isDraggingItem && 'is-dragging-item',
                          activeQuota.map(itx => itx.id).includes(element.id) && 'active'
                        ]"
                      >
                        <el-icon>
                          <Icon :class-name="`field-icon-${fieldType[element.deType]}`"
                            ><component
                              class="svg-icon"
                              :class="`field-icon-${fieldType[element.deType]}`"
                              :is="getIconName(element.deType, element.extField, true)"
                            ></component
                          ></Icon>
                        </el-icon>
                        <span
                          class="field-name ellipsis"
                          :class="{ dark: themes === 'dark' }"
                          :title="element.name"
                          >{{ element.name }}</span
                        >
                        <el-icon
                          v-if="element.id !== '-1' && !element.chartId"
                          class="field-setting child"
                          :class="{ 'remove-icon--dark': themes === 'dark' }"
                          size="14px"
                          @click.stop="copyChartFieldItem(element.id)"
                        >
                          <Icon class-name="inner-class" name="icon_copy_outlined"
                            ><icon_copy_outlined class="svg-icon inner-class"
                          /></Icon>
                        </el-icon>
                        <el-icon
                          v-if="element.id !== '-1' && element.chartId"
                          class="field-setting child"
                          :class="{ 'remove-icon--dark': themes === 'dark' }"
                          size="14px"
                          @click.stop="deleteChartFieldItem(element.id)"
                        >
                          <Icon class-name="inner-class" name="icon_delete-trash_outlined"
                            ><icon_deleteTrash_outlined class="svg-icon inner-class"
                          /></Icon>
                        </el-icon>
                        <el-dropdown
                          v-if="element.id !== '-1' && false"
                          :effect="props.themes"
                          placement="right-start"
                          trigger="click"
                          size="small"
                          class="field-setting child"
                          @command="chartFieldEdit"
                        >
                          <span class="el-dropdown-link">
                            <el-icon class="icon-setting"><Setting /></el-icon>
                          </span>
                          <template #dropdown>
                            <el-dropdown-menu :effect="props.themes">
                              <el-dropdown-item :command="handleChartFieldEdit(element, 'copy')">
                                {{ t('common.copy') }}
                              </el-dropdown-item>
                              <span v-if="element.chartId">
                                <el-dropdown-item :command="handleChartFieldEdit(element, 'edit')">
                                  {{ t('common.edit') }}
                                </el-dropdown-item>
                                <el-dropdown-item
                                  :command="handleChartFieldEdit(element, 'delete')"
                                >
                                  {{ t('common.delete') }}
                                </el-dropdown-item>
                              </span>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                      <div
                        v-if="activeQuota.map(itx => itx.id).includes(element.id)"
                        :draggable="true"
                        class="shadow"
                        :class="isDraggingItem && 'is-dragging'"
                        @dragstart="dragStart"
                        @dragend="dragEnd"
                      >
                        <template v-if="isDrag">
                          <div
                            v-for="ele in activeQuota"
                            :key="ele.id"
                            class="items flex-align-center"
                          >
                            <el-icon>
                              <Icon :class-name="`field-icon-${fieldType[ele.deType]}`"
                                ><component
                                  class="svg-icon"
                                  :class-name="`field-icon-${fieldType[ele.deType]}`"
                                  :is="iconFieldMap[fieldType[ele.deType]]"
                                ></component
                              ></Icon>
                            </el-icon>
                            <span
                              class="field-name ellipsis"
                              :class="{ dark: themes === 'dark' }"
                              >{{ ele.name }}</span
                            >
                            <el-dropdown
                              v-if="ele.id !== '-1' && false"
                              :effect="props.themes"
                              placement="right-start"
                              trigger="click"
                              size="small"
                              class="field-setting child"
                              @command="chartFieldEdit"
                            >
                              <span class="el-dropdown-link">
                                <el-icon class="icon-setting"><Setting /></el-icon>
                              </span>
                              <template #dropdown>
                                <el-dropdown-menu :effect="props.themes">
                                  <el-dropdown-item :command="handleChartFieldEdit(ele, 'copy')">
                                    {{ t('common.copy') }}
                                  </el-dropdown-item>
                                  <span v-if="ele.chartId">
                                    <el-dropdown-item :command="handleChartFieldEdit(ele, 'edit')">
                                      {{ t('common.edit') }}
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                      :command="handleChartFieldEdit(ele, 'delete')"
                                    >
                                      {{ t('common.delete') }}
                                    </el-dropdown-item>
                                  </span>
                                </el-dropdown-menu>
                              </template>
                            </el-dropdown>
                          </div>
                        </template>
                      </div>
                    </div>
                  </el-scrollbar>
                </div>
              </div>
            </el-main>
          </el-container>
        </div>
      </el-row>
    </template>
    <chart-template-info v-if="templateStatusShow" :themes="themes"></chart-template-info>
    <!--显示名修改-->
    <el-dialog
      v-model="state.renameItem"
      :title="t('chart.show_name_set')"
      :visible="state.renameItem"
      width="420px"
      :close-on-click-modal="false"
    >
      <div @keydown.stop @keyup.stop>
        <el-form
          ref="renameForm"
          label-width="80px"
          require-asterisk-position="right"
          :model="state.itemForm"
          :rules="itemFormRules"
          label-position="top"
          @submit.prevent
        >
          <el-form-item :label="t('dataset.field_origin_name')" class="name-edit-form">
            <span class="text">{{ state.itemForm.name }}</span>
          </el-form-item>
          <el-form-item
            :label="t('chart.show_name')"
            class="name-edit-form no-margin-bottom"
            prop="chartShowName"
          >
            <el-input v-model="state.itemForm.chartShowName" class="text" clearable />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeRename">{{ t('chart.cancel') }} </el-button>
          <el-button type="primary" @click="saveRename(renameForm)"
            >{{ t('chart.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!--指标过滤器-->
    <el-dialog
      v-if="state.quotaFilterEdit"
      v-model="state.quotaFilterEdit"
      :title="t('chart.add_filter')"
      :visible="state.quotaFilterEdit"
      :close-on-click-modal="false"
      width="600px"
      class="dialog-css"
    >
      <quota-filter-editor :item="state.quotaItem" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeQuotaFilter">{{ t('chart.cancel') }} </el-button>
          <el-button type="primary" @click="saveQuotaFilter">{{ t('chart.confirm') }} </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-if="state.resultFilterEdit"
      v-model="state.resultFilterEdit"
      :title="t('chart.add_filter')"
      :visible="state.resultFilterEdit"
      :close-on-click-modal="false"
      width="600px"
      class="dialog-css"
    >
      <result-filter-editor :chart="state.chartForFilter" :item="state.filterItem" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeResultFilter">{{ t('chart.cancel') }} </el-button>
          <el-button type="primary" @click="saveResultFilter">{{ t('chart.confirm') }} </el-button>
        </div>
      </template>
    </el-dialog>

    <!--同环比设置-->
    <el-dialog
      v-if="state.showEditQuotaCompare"
      v-model="state.showEditQuotaCompare"
      :title="t('chart.yoy_setting')"
      :visible="state.showEditQuotaCompare"
      :close-on-click-modal="false"
      width="600px"
      class="dialog-css"
    >
      <compare-edit
        :compare-item="state.quotaItemCompare"
        :chart="view"
        :dimension-data="dimensionData"
        :quota-data="quotaData"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeQuotaEditCompare">{{ t('chart.cancel') }} </el-button>
          <el-button type="primary" @click="saveQuotaEditCompare">
            {{ t('chart.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!--数值格式化-->
    <el-dialog
      v-if="state.showValueFormatter"
      v-model="state.showValueFormatter"
      :title="t('chart.value_formatter') + ' - ' + state.valueFormatterItem.name"
      :visible="state.showValueFormatter"
      :close-on-click-modal="false"
      width="420px"
      class="dialog-css"
    >
      <value-formatter-edit :formatter-item="state.valueFormatterItem" :chart="view" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeValueFormatter">{{ t('chart.cancel') }} </el-button>
          <el-button type="primary" @click="saveValueFormatter"
            >{{ t('chart.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!--xAxis自定义排序-->
    <el-dialog
      v-if="state.showCustomSort"
      v-model="state.showCustomSort"
      :title="t('chart.custom_sort') + t('chart.sort')"
      :visible="state.showCustomSort"
      :close-on-click-modal="false"
      width="372px"
      class="dialog-css custom_sort_dialog"
    >
      <custom-sort-edit
        :chart="view"
        :field-type="customSortAxis"
        :field="state.customSortField"
        @on-sort-change="customSortChange"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeCustomSort">{{ t('chart.cancel') }} </el-button>
          <el-button type="primary" @click="saveCustomSort">{{ t('chart.confirm') }} </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="state.showSortPriority"
      :close-on-click-modal="false"
      width="372px"
      class="dialog-css custom_sort_dialog"
      destroy-on-close
    >
      <template #header>
        <span style="font-size: 15px; font-weight: bold; color: #1f2329">
          {{ t('chart.sort_priority') }}
        </span>
        <span style="color: #1f2329">({{ t('chart.sort_priority_tip') }})</span>
      </template>
      <sort-priority-edit :chart="view" @on-priority-change="onPriorityChange" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeSortPriority">{{ t('chart.cancel') }} </el-button>
          <el-button type="primary" @click="saveSortPriority">{{ t('chart.confirm') }} </el-button>
        </div>
      </template>
    </el-dialog>

    <!--图表计算字段-->
    <el-dialog
      v-model="editCalcField"
      width="1000px"
      :title="isCalcFieldAdd ? t('dataset.add_calc_field') : t('dataset.edit_calc_field')"
      :close-on-click-modal="false"
    >
      <calc-field-edit ref="calcEdit" />
      <template #footer>
        <el-button secondary @click="closeEditCalc()">{{ t('dataset.cancel') }} </el-button>
        <el-button type="primary" @click="confirmEditCalc()">{{ t('dataset.confirm') }} </el-button>
      </template>
    </el-dialog>

    <!-- REST数据源配置弹窗 -->
    <el-dialog
      v-model="state.showRestConfig"
      title="REST数据源配置"
      width="800px"
      :close-on-click-modal="false"
    >
      <rest-data-source
        ref="restDataSourceRef"
        v-model="state.restConfig"
        :themes="themes"
        @onConfigChange="onRestConfigChange"
        @onFieldsChange="onRestFieldsChange"
      />
      <template #footer>
        <el-button @click="state.showRestConfig = false">取消</el-button>
        <el-button type="primary" @click="saveRestConfig">确定</el-button>
      </template>
    </el-dialog>
  </div>
  <FilterTree ref="filterTree" @filter-data="changeFilterData" />
  <XpackComponent ref="openHandler" jsname="L2NvbXBvbmVudC9lbWJlZGRlZC1pZnJhbWUvT3BlbkhhbmRsZXI=" />
  <Teleport v-if="componentNameEdit" :to="'#component-name'">
    <input
      ref="componentNameInput"
      v-model="inputComponentName.name"
      :effect="themes"
      width="100%"
      @change="onComponentNameChange"
      @blur="closeEditComponentName"
    />
  </Teleport>
</template>

<style lang="less" scoped>
@import '@/style/mixin.less';

.right-dimension {
  .item {
    width: 100%;
    height: 28px;
    position: relative;
    overflow: hidden;

    & + .item {
      margin-top: 2px;
    }

    .items {
      width: 100%;
      height: 28px;
      border-radius: 4px;
      border: 1px solid transparent;
      color: #a6a6a6;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      float: left;
      padding: 4px 10px;
      position: relative;
      cursor: pointer;
      &:hover {
        background: #1f23291a;
      }

      &.item-dimension--dark:hover {
        background: #ebebeb1a;
      }

      .ed-icon {
        font-size: 14px;
      }
      &.active {
        border-color: var(--ed-color-primary, #3370ff);
      }
    }

    .is-dragging-item {
      z-index: 10;
    }

    .shadow {
      position: absolute;
      width: 100%;
      top: 0;
      left: 0;
      cursor: pointer;
      min-height: 100%;
      z-index: 5;
      .items {
        border-color: var(--ed-color-primary, #3370ff);
        & + .items {
          margin-top: 2px;
        }
      }

      &.is-dragging {
        float: left;
        position: relative;
      }
    }
  }
}
.collapse-icon {
  color: @canvas-main-font-color;
}

.hint-icon {
  cursor: pointer;
  font-size: 14px;
  color: #646a73;

  &.hint-icon--dark {
    color: #a6a6a6;
  }
}

.editor-light {
  border-left: solid 1px @side-outline-border-color-light !important;
  color: @canvas-main-font-color-light!important;
  background-color: @side-area-background-light!important;
  :deep(.ed-tabs__header) {
    border-top: solid 1px @side-outline-border-color-light !important;
  }
  :deep(.drag_main_area) {
    border-top: solid 1px @side-outline-border-color-light !important;
  }
  :deep(.drag-data) {
    border-top: solid 1px @side-outline-border-color-light !important;
  }
  :deep(.result-style) {
    border-top: 1px solid @side-outline-border-color-light !important;
  }
  :deep(.dataset-select) {
    border-top: 1px solid @side-outline-border-color-light !important;
  }
  :deep(.dataset-main) {
    border-left: 1px solid @side-outline-border-color-light !important;
  }
  :deep(input) {
    font-size: 12px;
    font-weight: 400;
  }
  :deep(.divider) {
    background-color: @side-outline-border-color-light !important;
  }
  :deep(.item-span-style) {
    color: @canvas-main-font-color-light!important;
  }

  :deep(.editor-title) {
    color: #1f2329 !important;
  }
  :deep(.collapse-title) {
    color: #1f2329 !important;
  }
  :deep(.collapse-icon) {
    color: #646a73 !important;
  }
  .query-style-tab {
    width: 100%;
    border-top: solid 1px @side-outline-border-color-light !important;

    .tab-container {
      .border-bottom-tab(8px);
    }

    margin-left: 0px !important;

    :deep(.ed-tabs__header) {
      border-top: none !important;
    }

    :deep(.ed-tabs__nav-wrap::after) {
      background-color: rgba(31, 35, 41, 0.15);
    }
    :deep(.ed-tabs__nav-scroll) {
      .ed-tabs__item {
        height: 35px;
        line-height: 35px;
        color: var(--ed-color-primary, #3370ff);
        font-family: var(--de-custom_font, 'PingFang');
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
      }
    }
  }
  .query-style-tab {
    width: 100%;
    border-top: solid 1px @side-outline-border-color-light !important;

    .tab-container {
      .border-bottom-tab(8px);
    }

    margin-left: 0px !important;

    :deep(.ed-tabs__header) {
      border-top: none !important;
    }

    :deep(.ed-tabs__nav-wrap::after) {
      background-color: rgba(31, 35, 41, 0.15);
    }
    :deep(.ed-tabs__nav-scroll) {
      .ed-tabs__item {
        height: 35px;
        line-height: 35px;
        color: var(--ed-color-primary, #3370ff);
        font-family: var(--de-custom_font, 'PingFang');
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
      }
    }
  }
}

// editor form 全局样式
.editor-dark {
  border-left: solid 1px @main-collapse-border-dark !important;
  .dataset-selector {
    :deep(.ed-input__inner),
    :deep(.ed-input__wrapper),
    :deep(.ed-input.is-disabled .ed-input__wrapper) {
      color: var(--ed-color-white);
      background-color: @side-content-background;
      border: none;
    }
    :deep(.ed-input__inner) {
      border: none;
    }
    :deep(.ed-input__wrapper) {
      box-shadow: 0 0 0 1px hsla(0, 0%, 100%, 0.15) inset !important;
    }
    :deep(.ed-input__wrapper:hover) {
      box-shadow: 0 0 0 1px var(--ed-color-primary, #3370ff) inset !important;
    }
  }
  .query-style-tab {
    width: 100%;
    border-top: solid 1px @main-collapse-border-dark !important;

    .tab-container {
      .border-bottom-tab(8px);
    }

    margin-left: 0px !important;

    :deep(.ed-tabs__header) {
      border-top: none !important;
    }

    :deep(.ed-tabs__nav-wrap::after) {
      background-color: rgba(31, 35, 41, 0.15);
    }
    :deep(.ed-tabs__nav-scroll) {
      .ed-tabs__item {
        height: 35px;
        line-height: 35px;
        color: var(--ed-color-primary, #3370ff);
        font-family: var(--de-custom_font, 'PingFang');
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
      }
    }
  }
}

.chart-edit {
  position: relative;
  transition: 0.5s;
  height: 100%;
  color: white;
  background-color: @side-area-background;
}
.ed-row {
  display: block;
}

span {
  font-size: 12px;
}

.de-chart-editor {
  height: 100%;
  overflow-y: hidden;
  width: 100%;
  display: flex;
  transition: 0.5s;
  .padding-lr {
    padding: 0 16px;

    &.no-top-border {
      border-top: none !important;
    }
    &.no-top-padding {
      padding-top: 0 !important;

      :deep(.drag-placeholder-style) {
        top: calc(50% - 8px);
      }
    }
  }
  .view-title-name {
    display: -moz-inline-box;
    display: inline-block;
    width: 130px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-left: 38px;
  }

  .view-panel-row {
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;

    :deep(.ed-collapse-item__content) {
      padding: 16px 10px 0;
    }
  }

  .tab-header {
    --ed-tabs-header-height: 34px;
    --custom-tab-color: #646a73;

    :deep(.ed-tabs__nav-wrap::after) {
      background-color: unset;
    }

    &.dark {
      --custom-tab-color: #a6a6a6;
    }

    height: 100%;
    :deep(.ed-tabs__header) {
      border-top: solid 1px @side-outline-border-color;
    }
    :deep(.ed-tabs__item) {
      font-weight: 400;
      font-size: 12px;
      padding: 0 8px !important;
      margin-right: 12px;
    }

    :deep(.ed-tabs__item:not(.is-active)) {
      color: var(--custom-tab-color);
    }

    :deep(.ed-tabs__item.is-active) {
      font-weight: 500;
    }

    :deep(.ed-tabs__nav-scroll) {
      padding-left: 0 !important;
    }

    :deep(.ed-tabs__header) {
      margin: 0 !important;
    }

    :deep(.ed-tabs__content) {
      height: calc(100% - 35px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }

  .field-height {
    height: 50%;

    label {
      color: #646a73;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px;
    }

    &.first {
      border-top: none !important;
      position: relative;
    }

    .drag-vertical {
      width: 100%;
      height: 4px;
      position: absolute;
      left: 0;
      cursor: row-resize;

      &.is-hovering::after,
      &:hover::after {
        width: calc(100% - 32px);
        height: 1px;
        content: '';
        position: absolute;
        left: 16px;
        top: 0;
        background: var(--ed-color-primary, #3370ff);
      }
    }

    &.dark {
      label {
        color: #a6a6a6;
      }
    }

    .divider {
      width: 100%;
      height: 1px;
      padding: 0 16px;
      background-color: rgba(255, 255, 255, 0.15);
    }
  }

  .drag-list {
    height: calc(100% - 26px);
    min-height: 24px;
    //overflow: auto;
    padding: 2px 0;
  }

  .item-dimension {
    padding: 4px 10px;
    margin: 0 2px;
    text-align: left;
    color: #606266;
    display: block;
    word-break: break-all;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    position: relative;
    border-radius: 4px;
    border: 1px solid transparent;

    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    vertical-align: middle;

    height: 28px;

    cursor: pointer;

    &:hover {
      background: rgba(31, 35, 41, 0.1);
    }

    &.item-dimension--dark {
      &:hover {
        background: rgba(235, 235, 235, 0.1);
      }
    }

    &.sortable-chosen {
      border: 1px solid var(--ed-color-primary, #3370ff);
      background: #fff;

      &:hover {
        background: #fff;
      }

      &.item-dimension--dark {
        background: #1a1a1a;
        &:hover {
          background: #1a1a1a;
        }
      }
    }
  }

  .father .child {
    visibility: hidden;
  }

  .father:hover .child {
    visibility: visible;
  }

  .field-name {
    display: inline-block;
    width: 90px;
    word-break: break-all;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding-left: 4px;
    font-weight: 400;
    color: #646a73;

    &.dark {
      color: #a6a6a6;
    }
  }

  .padding-tab {
    padding: 0;
    height: 100%;
    width: 100%;
    display: flex;

    :deep(.ed-scrollbar) {
      &.has-footer {
        height: calc(100% - 81px);
      }
    }

    :deep(.ed-footer) {
      padding: 0;
      height: 114px;
    }
  }

  .result-count {
    width: 60px;

    :deep(.ed-input__wrapper) {
      padding: 1px 2px;
    }
  }

  .result-count :deep(input) {
    padding: 0 4px;
  }

  .data-area-label {
    text-align: left;
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    :deep(.required::after) {
      content: '*';
      color: var(--ed-color-danger);
      margin-left: 4px;
      font-family: var(--de-custom_font, 'PingFang');
      font-style: normal;
      font-weight: 400;
    }
  }

  .form-draggable-title {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;

    span {
      cursor: default;
    }

    :deep(.required::after) {
      content: '*';
      color: var(--ed-color-danger);
      margin-left: 2px;
      font-family: var(--de-custom_font, 'PingFang');
      font-style: normal;
      font-weight: 400;
    }

    .remove-icon {
      color: #646a73;
      cursor: pointer;
      margin-top: 2px;
      margin-right: 2px;

      &.remove-icon--dark {
        color: #a6a6a6;
      }

      .inner-class {
        font-size: 14px;
      }
    }
  }

  .drag-block-style {
    padding: 2px 0 0 0;
    width: 100%;
    min-height: 32px;
    border-radius: 4px;
    overflow-x: hidden;
    overflow-y: hidden;
    display: block;
    align-items: center;
    border: 1px dashed #bbbfc4;
    background-color: rgba(31, 35, 41, 0.05);
    margin-top: 8px;

    &.dark {
      border: 1px dashed #5f5f5f;
      background-color: rgba(235, 235, 235, 0.05);
    }

    &:has(span) {
      background-color: transparent !important;
    }
  }

  .draggable-group {
    display: block;
    width: 100%;
    height: calc(100% - 6px);
  }

  .ed-input-refresh-time {
    width: calc(50% - 4px) !important;
  }

  .ed-input-refresh-unit {
    margin-left: 8px;
    width: calc(50% - 4px) !important;
  }

  .ed-input-refresh-loading {
    margin-left: 4px;
    font-size: 12px !important;
  }

  .drag-data {
    padding-top: 8px;
    padding-bottom: 16px;

    .tree-btn {
      width: 100%;
      margin-top: 8px;
      background: #fff;
      height: 28px;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      display: flex;
      color: #cccccc;
      align-items: center;
      cursor: pointer;
      justify-content: center;
      font-size: 12px;
      &.tree-btn--dark {
        background: rgba(235, 235, 235, 0.05);
        border-color: #5f5f5f;
      }

      &.active {
        color: #3370ff;
        border-color: #3370ff;
      }
    }

    :deep(.tree-btn_secondary) {
      width: 100%;
      margin-top: 8px;
      line-height: 28px;
      height: 28px;
      font-size: 12px;

      & > [class*='ed-icon'] + span {
        margin-left: 2px !important;
      }
    }

    &.no-top-border {
      border-top: none !important;
    }
    &.no-top-padding {
      padding-top: 0 !important;
    }
    &:nth-child(n + 2) {
      border-top: 1px solid @side-outline-border-color;
    }
    &:first-child {
      border-top: none !important;
    }
  }

  .editor-title {
    color: @dv-canvas-main-font-color;
    font-weight: 500;
    height: @component-toolbar-height;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
    line-height: 22px;

    span {
      width: calc(100% - 24px);
      overflow-x: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
    }
  }

  .ed-tabs {
    --el-tabs-header-height: 38px !important;
  }

  .switch-chart {
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: space-between;
  }

  .dataset-selector :deep(.ed-input__inner) {
    height: 24px;
    width: 110px;
  }

  .result-style {
    position: absolute;
    bottom: 0;
    width: 100%;
    border-top: 1px solid @side-outline-border-color;
  }
  .result-style-dark {
    :deep(.ed-button) {
      color: #ffffff;
      background-color: var(--ed-color-primary, #3370ff);
      border: none;
      border-radius: 0;
    }
    :deep(.ed-button:hover) {
      background-color: var(--ed-color-primary-light-3);
    }
    :deep(.ed-button:active) {
      background-color: var(--ed-color-primary);
    }
  }
  .result-style-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    padding: 0 6px;

    .margin20-radio {
      margin-right: 20px;
    }

    .result-count-label {
      color: #1f2329;
      font-size: 12px;
      font-weight: 400;

      &.dark {
        color: #fff;
      }
    }
  }
  .result-style-button {
    height: 40px;
    width: 100%;
    border-radius: 0;
  }

  .switch-chart-dark {
    :deep(.ed-button) {
      color: #ffffff;
      background-color: #1a1a1a;
      border: 1px solid hsla(0, 0%, 100%, 0.15);
      border-radius: 2px;
    }
    :deep(.ed-button:hover) {
      border: 1px solid var(--ed-color-primary, #3370ff);
    }
  }

  .dataset-search {
    height: 51px;
    width: 100%;
  }
  .dataset-search-label {
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #1f2329;
    font-weight: 500;

    &.dark {
      color: #ebebeb;
    }
  }
  .field-search-icon-btn {
    font-size: 16px;
    color: #646a73;
    cursor: pointer;
    position: relative;
    &:hover {
      &::after {
        content: '';
        position: absolute;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        top: -4px;
        left: -4px;
        background: rgba(31, 35, 41, 0.1);
      }
    }

    &.dark {
      color: #a6a6a6;
      &:hover {
        &::after {
          background: rgba(235, 235, 235, 0.1);
        }
      }
    }
  }

  .dataset-search-input {
    font-size: 12px;

    :deep(.ed-input__inner) {
      background-color: @side-area-background-light;
      color: @canvas-main-font-color-light;
    }
    :deep(.ed-input__wrapper) {
      box-shadow: none !important;
      border-bottom: 1px solid rgba(31, 35, 41, 0.15);
      background-color: @side-area-background-light;
      border-radius: 0;
      padding: 1px 4px;
    }

    &.dark {
      :deep(.ed-input__inner) {
        background-color: @side-area-background;
        color: #ffffff;
      }
      :deep(.ed-input__wrapper) {
        border-bottom: 1px solid hsla(0, 0%, 100%, 0.15);
        background-color: @side-area-background;
      }
    }
  }
}

.chart_type_area {
  height: 54px;
  overflow: auto;
  padding: 8px;
}

.drag_main_area {
  border-top: 1px solid @side-outline-border-color;
  overflow: auto;
  height: calc(100% - 1px);
  :deep(.is-horizontal) {
    display: none !important;
  }
  :deep(.ed-scrollbar__wrap) {
    overflow-x: hidden;
  }
}

.collapse-title {
  color: @dv-canvas-main-font-color;
  width: 35px;
  text-align: center;
  padding: 5px;
  margin-top: 35px;
  span {
    writing-mode: vertical-rl;
    text-orientation: mixed;
  }
}

.custom-icon {
  position: absolute;
  right: 5px;
  top: 10px;
  cursor: pointer;
  z-index: 2;
}
:deep(.ed-collapse) {
  width: 100%;
  border-top: unset;
}
:deep(.ed-form-item) {
  .ed-radio.ed-radio--small .ed-radio__inner {
    width: 14px;
    height: 14px;
  }
  .ed-input__inner {
    font-size: 12px;
    font-weight: 400;
  }
  .ed-input {
    --ed-input-height: 28px;

    .ed-input__suffix {
      height: 26px;
    }
  }
  .ed-input-number {
    width: 100%;

    .ed-input-number__decrease {
      --ed-input-number-controls-height: 13px;
    }
    .ed-input-number__increase {
      --ed-input-number-controls-height: 13px;
    }

    .ed-input__inner {
      text-align: start;
    }
  }
  .ed-select {
    width: 100%;
    .ed-input__inner {
      height: 26px;
    }
  }
  .ed-checkbox {
    .ed-checkbox__label {
      font-size: 12px;
    }
  }
  .ed-color-picker {
    .ed-color-picker__mask {
      height: 26px;
      width: calc(100% - 2px) !important;
    }
  }
  .ed-radio {
    height: 20px;
    .ed-radio__label {
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
  }
}
:deep(.ed-form-item__label) {
  color: @canvas-main-font-color;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;

  height: unset;
  line-height: 20px;
  margin-bottom: 8px;
}
:deep(.form-item-dark) {
  .ed-form-item__label {
    color: @canvas-main-font-color-dark;
  }

  &.select-append {
    .ed-input-group__append {
      background-color: transparent;
    }
    .dv-dark {
      & > .ed-input__wrapper {
        background-color: #1a1a1a;
      }
      .ed-input-group__append .ed-select {
        margin: 0 -20px;
      }
    }
  }
}

:deep(.form-item-light) {
  &.select-append {
    .ed-input-group__append {
      padding: 0 20px;
    }
  }
}
:deep(.ed-checkbox__label) {
  color: #1f2329;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
:deep(.ed-checkbox--dark) {
  .ed-checkbox__label {
    color: @dv-canvas-main-font-color;
  }
}
:deep(.ed-checkbox) {
  color: @canvas-main-font-color;
  font-size: 12px;
}
.dataset-area {
  width: 180px;
  position: relative;
}
.dataset-main {
  border-left: 1px solid @side-outline-border-color;
}

.content-area {
  height: 100%;
  position: relative;
  transition: 0.5s;
  overflow-x: hidden;
}

.content-area-close {
  width: 35px;
}

.content-area-left-open {
  width: 240px;
}

.content-area-right-open {
  width: 179px;
}

.dataset-main-top {
  padding: 0;

  &::-webkit-scrollbar {
    display: none;
  }
}

.dataset-select {
  padding: 8px;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  z-index: 1000;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}
.style-collapse {
  :deep(.ed-collapse-item__header),
  :deep(.ed-collapse-item__wrap) {
    border-bottom: none !important;
  }
  :deep(.ed-collapse-item__content) {
    padding-left: 0 !important;
    padding-bottom: 10px !important;
  }

  &.data-tab-collapse {
    border-bottom: none;
    border-top: 1px solid var(--ed-collapse-border-color);

    :deep(.ed-collapse-item.ed-collapse--dark .ed-collapse-item__wrap) {
      background-color: #1a1a1a;
    }

    :deep(.ed-collapse-item__wrap) {
      border-top: none !important;
    }
    :deep(.ed-collapse-item__content) {
      padding: 0 !important;
      border-top: none !important;
    }
    :deep(.ed-collapse-item__header) {
      background-color: transparent;
      border-bottom: none !important;
    }
  }
}
.field-setting {
  position: absolute;
  right: 8px;
  color: #646a73;
  &.remove-icon--dark {
    color: #a6a6a6;
  }
}
.father .child {
  visibility: hidden;
}

.father:hover .child {
  visibility: visible;
}

.icon-setting {
  color: #a6a6a6;
}

.label-icon {
  top: 2px;
  margin-left: 6px;
  font-size: 14px;
  cursor: pointer;
}
.area-tree-select {
  margin-top: 8px;
  :deep(.ed-select) {
    display: block;
  }
  :deep(div.ed-select--dark div[data-key='customRoot'] li.is-disabled span) {
    color: white;
  }
  :deep(div[data-key='customRoot'] li.is-disabled span) {
    color: black;
  }
}

.chart-type-select {
  width: 100%;
  margin-top: 8px;
  :deep(.ed-select__prefix) {
    padding: 0;
    margin: 0;
    &::after {
      display: none;
    }
    height: 20px;
    .chart-type-select-icon {
      width: 23px;
      height: 16px;
    }
  }
  :deep(.ed-input) {
    height: 28px;
  }
}
.name-edit-form {
  margin-bottom: 16px !important;

  .text {
    font-family: var(--de-custom_font, 'PingFang');
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;

    --ed-input-height: 32px;

    :deep(.ed-input__inner) {
      font-family: var(--de-custom_font, 'PingFang');
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }

  :deep(.ed-form-item__label) {
    color: #1f2329;

    font-family: var(--de-custom_font, 'PingFang');
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }

  &.no-margin-bottom {
    margin-bottom: 0 !important;
  }
}
</style>

<style lang="less">
.ed-dropdown__popper.ed-popper.is-dark:has(.dark-dimension-quota) {
  border: none;
}
:deep(.ed-select-dropdown__item) {
  display: flex;
  align-items: center;
}
.chart-type-hide-options {
  display: none;
}

.custom_sort_dialog {
  max-height: calc(100vh - 100px);
  min-height: 152px;

  display: flex;
  flex-direction: column;
  margin: 0;
  position: absolute !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  .ed-dialog__body {
    flex: 1;
  }
}

.refresh-active-footer {
  height: 150px !important;
}

.refresh-area {
  width: 100%;
  padding: 0 8px;

  .no-margin-bottom {
    margin-bottom: 8px;
  }
}

.name-area {
  position: relative;
  line-height: 24px;
  height: 24px;
  font-size: 14px !important;
  overflow: hidden;
  cursor: pointer;
  input {
    position: absolute;
    left: 0;
    width: 100%;
    outline: none;
    border: 1px solid #295acc;
    border-radius: 4px;
    padding: 0 4px;
    height: 100%;
  }
}

.component-name-dark {
  input {
    position: absolute;
    left: 0;
    width: 100%;
    color: @dv-canvas-main-font-color;
    background-color: #050e21;
    outline: none;
    border: 1px solid #295acc;
    border-radius: 4px;
    padding: 0 4px;
    height: 100%;
  }
}
</style>
