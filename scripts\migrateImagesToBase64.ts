/**
 * 图片迁移脚本：将现有的图片URL转换为base64格式
 * Image Migration Script: Convert existing image URLs to base64 format
 */

import { fileToBase64, isBase64Image } from '../src/utils/imageBase64Utils'

interface MigrationConfig {
  // 是否实际执行迁移（false为预览模式）
  dryRun: boolean
  // 图片质量
  quality: number
  // 最大尺寸
  maxWidth: number
  maxHeight: number
  // 并发数量
  concurrency: number
}

interface MigrationResult {
  total: number
  success: number
  failed: number
  skipped: number
  errors: string[]
}

/**
 * 从URL获取图片文件
 */
async function fetchImageFromUrl(url: string): Promise<File | null> {
  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const blob = await response.blob()
    const filename = url.split('/').pop() || 'image'
    return new File([blob], filename, { type: blob.type })
  } catch (error) {
    console.error(`获取图片失败 ${url}:`, error)
    return null
  }
}

/**
 * 将图片URL转换为base64
 */
async function convertUrlToBase64(
  url: string, 
  config: MigrationConfig
): Promise<{ success: boolean; data?: string; error?: string }> {
  try {
    // 如果已经是base64，跳过
    if (isBase64Image(url)) {
      return { success: true, data: url }
    }

    // 获取图片文件
    const file = await fetchImageFromUrl(url)
    if (!file) {
      return { success: false, error: '无法获取图片文件' }
    }

    // 转换为base64
    const base64 = await fileToBase64(file)
    return { success: true, data: base64 }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

/**
 * 扫描对象中的图片URL
 */
function findImageUrls(obj: any, path: string = ''): Array<{ path: string; url: string }> {
  const urls: Array<{ path: string; url: string }> = []
  
  if (typeof obj === 'string') {
    // 检查是否是图片URL
    if (obj.includes('static-resource') || obj.startsWith('http')) {
      urls.push({ path, url: obj })
    }
  } else if (Array.isArray(obj)) {
    obj.forEach((item, index) => {
      urls.push(...findImageUrls(item, `${path}[${index}]`))
    })
  } else if (obj && typeof obj === 'object') {
    Object.keys(obj).forEach(key => {
      const newPath = path ? `${path}.${key}` : key
      urls.push(...findImageUrls(obj[key], newPath))
    })
  }
  
  return urls
}

/**
 * 更新对象中的图片URL
 */
function updateImageUrls(obj: any, urlMap: Map<string, string>): any {
  if (typeof obj === 'string') {
    return urlMap.get(obj) || obj
  } else if (Array.isArray(obj)) {
    return obj.map(item => updateImageUrls(item, urlMap))
  } else if (obj && typeof obj === 'object') {
    const result = {}
    Object.keys(obj).forEach(key => {
      result[key] = updateImageUrls(obj[key], urlMap)
    })
    return result
  }
  
  return obj
}

/**
 * 迁移单个配置对象
 */
async function migrateConfigObject(
  config: any,
  migrationConfig: MigrationConfig
): Promise<{ config: any; result: MigrationResult }> {
  const result: MigrationResult = {
    total: 0,
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  // 查找所有图片URL
  const imageUrls = findImageUrls(config)
  result.total = imageUrls.length

  if (imageUrls.length === 0) {
    return { config, result }
  }

  console.log(`发现 ${imageUrls.length} 个图片URL`)

  // 转换URL映射
  const urlMap = new Map<string, string>()
  
  // 批量处理（控制并发）
  const chunks = []
  for (let i = 0; i < imageUrls.length; i += migrationConfig.concurrency) {
    chunks.push(imageUrls.slice(i, i + migrationConfig.concurrency))
  }

  for (const chunk of chunks) {
    const promises = chunk.map(async ({ url }) => {
      if (isBase64Image(url)) {
        result.skipped++
        return { url, base64: url }
      }

      const conversion = await convertUrlToBase64(url, migrationConfig)
      if (conversion.success) {
        result.success++
        return { url, base64: conversion.data }
      } else {
        result.failed++
        result.errors.push(`${url}: ${conversion.error}`)
        return { url, base64: url } // 保持原URL
      }
    })

    const results = await Promise.all(promises)
    results.forEach(({ url, base64 }) => {
      urlMap.set(url, base64)
    })
  }

  // 更新配置对象
  const updatedConfig = migrationConfig.dryRun 
    ? config 
    : updateImageUrls(config, urlMap)

  return { config: updatedConfig, result }
}

/**
 * 主迁移函数
 */
export async function migrateImagesToBase64(
  dashboardConfigs: any[],
  config: Partial<MigrationConfig> = {}
): Promise<{ configs: any[]; totalResult: MigrationResult }> {
  const migrationConfig: MigrationConfig = {
    dryRun: false,
    quality: 0.8,
    maxWidth: 1920,
    maxHeight: 1080,
    concurrency: 5,
    ...config
  }

  console.log('开始图片迁移...')
  console.log('配置:', migrationConfig)

  const totalResult: MigrationResult = {
    total: 0,
    success: 0,
    failed: 0,
    skipped: 0,
    errors: []
  }

  const migratedConfigs = []

  for (let i = 0; i < dashboardConfigs.length; i++) {
    const dashboardConfig = dashboardConfigs[i]
    console.log(`\n处理仪表板 ${i + 1}/${dashboardConfigs.length}`)

    const { config: migratedConfig, result } = await migrateConfigObject(
      dashboardConfig,
      migrationConfig
    )

    migratedConfigs.push(migratedConfig)

    // 累计结果
    totalResult.total += result.total
    totalResult.success += result.success
    totalResult.failed += result.failed
    totalResult.skipped += result.skipped
    totalResult.errors.push(...result.errors)

    console.log(`  - 总计: ${result.total}, 成功: ${result.success}, 失败: ${result.failed}, 跳过: ${result.skipped}`)
  }

  console.log('\n迁移完成!')
  console.log('总体结果:', totalResult)

  if (totalResult.errors.length > 0) {
    console.log('\n错误详情:')
    totalResult.errors.forEach(error => console.log(`  - ${error}`))
  }

  return { configs: migratedConfigs, totalResult }
}

/**
 * 命令行使用示例
 */
if (require.main === module) {
  // 示例用法
  const exampleConfigs = [
    {
      background: '/static-resource/bg1.jpg',
      components: [
        {
          propValue: { url: '/static-resource/pic1.png' },
          commonBackground: { outerImage: '/static-resource/bg2.jpg' }
        }
      ]
    }
  ]

  migrateImagesToBase64(exampleConfigs, {
    dryRun: true, // 预览模式
    concurrency: 3
  }).then(({ configs, totalResult }) => {
    console.log('迁移结果:', totalResult)
    console.log('迁移后的配置:', JSON.stringify(configs, null, 2))
  })
}
