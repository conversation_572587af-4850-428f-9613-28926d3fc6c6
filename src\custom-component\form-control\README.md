# 表单控件使用说明

## 概述

表单控件是一个功能完整的表单组件，支持多种字段类型、数据验证、REST API提交等功能。

## 功能特性

### 支持的字段类型
- **文本输入框** (`text`) - 单行文本输入
- **多行文本** (`textarea`) - 多行文本输入
- **数字输入框** (`number`) - 数字输入，支持最小值/最大值限制
- **下拉选择** (`select`) - 单选下拉框
- **单选按钮组** (`radio`) - 单选按钮组
- **复选框组** (`checkbox`) - 多选复选框组
- **日期选择器** (`date`) - 日期选择
- **邮箱输入** (`email`) - 邮箱格式验证
- **密码输入** (`password`) - 密码输入框

### 验证功能
- 必填验证
- 长度验证（最小/最大长度）
- 正则表达式验证
- 内置邮箱格式验证
- 实时验证和错误提示

### 提交功能
- REST API提交
- 支持GET/POST/PUT/DELETE方法
- 自定义请求头和参数
- 全局变量引用支持
- 成功/失败消息自定义
- 提交后重置表单
- 成功后跳转功能

### 布局配置
- 多列布局（1-4列）
- 标签位置配置（顶部/左侧/右侧）
- 组件尺寸配置
- 边框和间距设置

## 使用方法

### 1. 添加表单控件
在仪表板编辑模式下，从左侧组件面板拖拽"表单控件"到画布上。

### 2. 配置基础信息
在右侧属性面板的"基础配置"中设置：
- 表单标题
- 表单描述
- 提交按钮文本
- 重置按钮文本

### 3. 配置字段
在"字段配置"面板中：
1. 点击"添加字段"按钮
2. 在弹出的对话框中配置字段属性：
   - 字段类型
   - 字段标签
   - 字段名称（用于数据提交）
   - 占位符
   - 默认值
   - 验证规则
   - 选项配置（适用于select/radio/checkbox类型）

### 4. 配置提交设置
在"提交配置"面板中设置：
- 启用/禁用提交功能
- 提交URL
- 请求方法
- 请求头（支持自定义Header）
- 请求参数（支持全局变量引用）
- 超时时间
- 成功/失败消息
- 提交后是否重置表单
- 成功后跳转URL

### 5. 配置布局
在"布局配置"面板中调整：
- 表单列数
- 标签位置
- 标签宽度
- 组件尺寸
- 边框显示
- 字段间距

## 全局变量支持

表单控件支持在以下配置中使用全局变量：
- 提交URL
- 请求头值
- 请求参数值
- 跳转URL

使用格式：`${variableName}` 或 `{{variableName}}`

示例：
```
提交URL: ${apiBaseUrl}/form/submit
请求头: Authorization: Bearer ${userToken}
参数值: userId: ${currentUserId}
```

## 数据提交格式

表单提交的数据格式为JSON对象，字段名称对应配置的字段名称：

```json
{
  "name": "张三",
  "email": "<EMAIL>",
  "age": 25,
  "gender": "male",
  "hobbies": ["reading", "sports"],
  "birthdate": "1998-01-01"
}
```

## 验证规则

### 内置验证
- 必填验证：字段值不能为空
- 邮箱验证：符合邮箱格式
- 数字验证：必须为有效数字

### 自定义验证
- 最小长度：字符串最小长度限制
- 最大长度：字符串最大长度限制
- 正则表达式：自定义格式验证
- 自定义错误消息：验证失败时的提示文本

## 最佳实践

### 字段设计
1. 使用有意义的字段名称，便于后端处理
2. 设置合适的占位符，提示用户输入格式
3. 为必填字段设置明确的标识
4. 选择合适的字段类型，提升用户体验

### 验证配置
1. 为重要字段设置验证规则
2. 提供清晰的错误提示信息
3. 避免过于严格的验证规则

### 提交配置
1. 使用HTTPS协议保证数据安全
2. 设置合理的超时时间
3. 配置友好的成功/失败消息
4. 利用全局变量实现动态配置

### 布局优化
1. 根据字段数量选择合适的列数
2. 保持标签位置的一致性
3. 设置合适的字段间距
4. 考虑移动端适配

## 故障排除

### 常见问题
1. **表单无法提交**
   - 检查提交URL是否正确
   - 确认网络连接正常
   - 查看浏览器控制台错误信息

2. **验证不生效**
   - 确认字段配置正确
   - 检查验证规则设置
   - 查看字段名称是否唯一

3. **全局变量无法解析**
   - 确认变量名称正确
   - 检查变量是否已定义
   - 验证变量引用格式

### 调试技巧
1. 打开浏览器开发者工具查看网络请求
2. 检查控制台日志获取详细错误信息
3. 使用简单的测试接口验证配置
4. 逐步添加字段和验证规则，定位问题

## 技术支持

如果遇到问题或需要新功能，请联系开发团队或提交Issue。
