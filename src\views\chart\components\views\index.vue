<script lang="ts" setup>
import icon_info_outlined from '@/assets/svg/icon_info_outlined.svg'
import icon_linkRecord_outlined from '@/assets/svg/icon_link-record_outlined.svg'
import icon_viewinchat_outlined from '@/assets/svg/icon_viewinchat_outlined.svg'
import icon_drilling_outlined from '@/assets/svg/icon_drilling_outlined.svg'
import { useI18n } from '@/hooks/web/useI18n'
import ChartComponentG2Plot from './components/ChartComponentG2Plot.vue'
import DeIndicator from '@/custom-component/indicator/DeIndicator.vue'
import { useAppearanceStoreWithOut } from '@/store/modules/appearance'
import { useAppStoreWithOut } from '@/store/modules/app'
import { useEmbedded } from '@/store/modules/embedded'
import { XpackComponent } from '@/components/plugin'
import { PluginComponent } from '@/components/plugin'
import {
  computed,
  CSSProperties,
  nextTick,
  onBeforeMount,
  onMounted,
  PropType,
  provide,
  reactive,
  ref,
  shallowRef,
  toRefs,
  watch
} from 'vue'
import { useEmitt } from '@/hooks/web/useEmitt'
import { hexColorToRGBA, parseJson } from '@/views/chart/components/js/util.js'
import {
  CHART_FONT_FAMILY_MAP,
  DEFAULT_TITLE_STYLE
} from '@/views/chart/components/editor/util/chart'
import DrillPath from '@/views/chart/components/views/components/DrillPath.vue'
import { ElIcon, ElInput, ElMessage } from 'element-plus-secondary'
import { useFilter } from '@/hooks/web/useFilter'
import { useCache } from '@/hooks/web/useCache'

import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import { cloneDeep } from 'lodash-es'
import ChartComponentS2 from '@/views/chart/components/views/components/ChartComponentS2.vue'
import { ChartLibraryType } from '@/views/chart/components/js/panel/types'
import chartViewManager from '@/views/chart/components/js/panel'
import { storeToRefs } from 'pinia'
import { checkAddHttp, setIdValueTrans } from '@/utils/canvasUtils'
import { Base64 } from 'js-base64'
import DeRichTextView from '@/custom-component/rich-text/DeRichTextView.vue'
import DeButtonView from '@/custom-component/button/DeButtonView.vue'
import DeNestedMenuView from '@/custom-component/nested-menu/DeNestedMenuView.vue'
import DePictureGroup from '@/custom-component/picture-group/Component.vue'
import ChartEmptyInfo from '@/views/chart/components/views/components/ChartEmptyInfo.vue'
import { snapshotStoreWithOut } from '@/store/modules/data-visualization/snapshot'
import { viewFieldTimeTrans } from '@/utils/viewUtils'
import { CHART_TYPE_CONFIGS } from '@/views/chart/components/editor/util/chart'
import request from '@/config/axios'
import { store } from '@/store'
import { clearExtremum } from '@/views/chart/components/js/extremumUitl'
import DePreviewPopDialog from '@/components/visualization/DePreviewPopDialog.vue'
import { useRoute } from 'vue-router'
const route = useRoute()
const { wsCache } = useCache()
const chartComponent = ref<any>()
const { t } = useI18n()
const dvMainStore = dvMainStoreWithOut()
const { emitter } = useEmitt()
const dePreviewPopDialogRef = ref(null)
let innerRefreshTimer = null
let innerSearchCount = 0
const appStore = useAppStoreWithOut()
const appearanceStore = useAppearanceStoreWithOut()
const isDataEaseBi = computed(() => appStore.getIsDataEaseBi)
const isIframe = computed(() => appStore.getIsIframe)

const emit = defineEmits(['onPointClick', 'onComponentEvent'])

const {
  nowPanelJumpInfo,
  publicLinkStatus,
  dvInfo,
  curComponent,
  canvasStyleData,
  mobileInPc,
  inMobile,
  editMode
} = storeToRefs(dvMainStore)

const props = defineProps({
  // 公共参数集
  commonParams: {
    type: Object,
    required: false
  },
  active: {
    type: Boolean,
    default: false
  },
  element: {
    type: Object,
    default() {
      return {
        propValue: null
      }
    }
  },
  view: {
    type: Object as PropType<ChartObj>,
    default() {
      return {
        propValue: null
      }
    }
  },
  themes: {
    type: String,
    required: false,
    default: 'dark'
  },
  showPosition: {
    type: String,
    required: false,
    default: 'preview'
  },
  // 仪表板刷新计时器
  searchCount: {
    type: Number,
    required: false,
    default: 0
  },
  disabled: {
    type: Boolean,
    required: false,
    default: false
  },
  scale: {
    type: Number,
    required: false,
    default: 1
  },
  suffixId: {
    type: String,
    required: false,
    default: 'common'
  },
  fontFamily: {
    type: String,
    required: false,
    default: 'inherit'
  },
  optType: {
    type: String,
    required: false
  }
})
const dynamicAreaId = ref('')
const { view, showPosition, element, active, searchCount, scale, suffixId } = toRefs(props)
const titleShow = computed(() => {
  return (
    !['rich-text', 'picture-group', 'button', 'nested-menu'].includes(element.value.innerType) &&
    state.title_show &&
    showPosition.value !== 'viewDialog'
  )
})
const snapshotStore = snapshotStoreWithOut()

const state = reactive({
  initReady: true, //curComponent 切换期间 不接收外部的calcData 和 renderChart 事件
  title_show: true,
  title_remark: {
    show: false,
    remark: ''
  },
  title_class: {
    fontSize: '18px',
    color: '#303133',
    textAlign: 'left',
    fontStyle: 'normal',
    fontWeight: 'normal',
    background: '',
    fontFamily: '',
    textShadow: 'none',
    letterSpacing: '0px',
    fontSynthesis: 'style weight',
    width: 'fit-content',
    maxWidth: '100%',
    wordBreak: 'break-word',
    whiteSpace: 'pre-wrap'
  } as CSSProperties,
  drillFilters: [],
  viewInfoData: null,
  drillClickDimensionList: []
})

const drillClickLength = computed(() => state.drillClickDimensionList.length)

const titleAlign = computed<string>(() => {
  if (!titleShow.value) {
    return 'flex-start'
  }

  if (state.title_class.textAlign === 'center') {
    return 'center'
  } else if (state.title_class.textAlign === 'right') {
    return 'flex-end'
  }

  return 'flex-start'
})

const trackMenu = computed<Array<string>>(() => {
  return chartComponent?.value?.trackMenu ?? []
})

const hasLinkIcon = computed(() => {
  return trackMenu.value.indexOf('linkage') > -1 || trackMenu.value.indexOf('linkageAndDrill') > -1
})
const hasJumpIcon = computed(() => {
  return trackMenu.value.indexOf('jump') > -1 && !mobileInPc.value
})
const hasDrillIcon = computed(() => {
  return trackMenu.value.indexOf('drill') > -1 || trackMenu.value.indexOf('linkageAndDrill') > -1
})

const loading = ref(false)

const resultMode = computed(() => {
  return canvasStyleData.value.dashboard?.resultMode || null
})

const resultCount = computed(() => {
  return canvasStyleData.value.dashboard?.resultCount || null
})

const embeddedStore = useEmbedded()
// 编辑状态下 不启动刷新
const buildInnerRefreshTimer = (
  refreshViewEnable = false,
  refreshUnit = 'minute',
  refreshTime = 5
) => {
  if (showPosition.value === 'preview' && !innerRefreshTimer && refreshViewEnable) {
    innerRefreshTimer && clearInterval(innerRefreshTimer)
    const timerRefreshTime = refreshUnit === 'second' ? refreshTime * 1000 : refreshTime * 60000
    innerRefreshTimer = setInterval(() => {
      clearViewLinkage()
      queryData()
      innerSearchCount++
    }, timerRefreshTime)
  }
}

// 清除相同sourceViewId 的 联动条件
const clearViewLinkage = () => {
  dvMainStore.clearViewLinkage(element.value.id)
  useEmitt().emitter.emit('clearPanelLinkage', { viewId: element.value.id })
}

watch([() => scale.value], () => {
  initTitle()
})

watch([() => searchCount.value], () => {
  // 内部计时器启动 忽略外部计时器
  if (!innerRefreshTimer) {
    queryData()
  }
})
// 仪表板的查询结果设置变化 图表数据需要刷新
watch([() => resultCount.value], () => {
  queryData()
})

watch([() => resultMode.value], () => {
  queryData()
})

watch([() => scale.value], () => {
  nextTick(() => {
    chartComponent?.value?.renderChart?.(view.value)
  })
})

watch([() => curComponent.value], () => {
  if (curComponent.value && curComponent.value.id === view.value.id) {
    state.initReady = false
    nextTick(() => {
      state.initReady = true
    })
  }
})

const chartExtRequest = shallowRef(null)
provide('chartExtRequest', chartExtRequest)

const initTitle = () => {
  if (view.value.customStyle) {
    const customStyle = view.value.customStyle
    if (customStyle.text) {
      state.title_show = customStyle.text.show
      state.title_class.fontSize = customStyle.text.fontSize * scale.value + 'px'
      state.title_class.color = customStyle.text.color
      state.title_class.textAlign = customStyle.text.hPosition as CSSProperties['textAlign']
      state.title_class.fontStyle = customStyle.text.isItalic ? 'italic' : 'normal'
      state.title_class.fontWeight = customStyle.text.isBolder ? 'bold' : 'normal'
      if (!!appearanceStore.fontList.length) {
        appearanceStore.fontList.forEach(ele => {
          CHART_FONT_FAMILY_MAP[ele.name] = ele.name
        })
      }
      state.title_class.fontFamily = customStyle.text.fontFamily
        ? CHART_FONT_FAMILY_MAP[customStyle.text.fontFamily]
        : DEFAULT_TITLE_STYLE.fontFamily
      if (!CHART_FONT_FAMILY_MAP[customStyle.text.fontFamily]) {
        state.title_class.fontFamily = appearanceStore.fontList.find(ele => ele.isDefault)?.name
        customStyle.text.fontFamily = state.title_class.fontFamily
      }
      appearanceStore.setCurrentFont(state.title_class.fontFamily)
      state.title_class.letterSpacing =
        (customStyle.text.letterSpace
          ? customStyle.text.letterSpace
          : DEFAULT_TITLE_STYLE.letterSpace) + 'px'
      state.title_class.textShadow = customStyle.text.fontShadow ? '2px 2px 4px' : 'none'
    }
    if (customStyle.background) {
      state.title_class.background = hexColorToRGBA(
        customStyle.background.color,
        customStyle.background.alpha
      )
    }

    state.title_remark.show = customStyle.text.show && customStyle.text.remarkShow
    state.title_remark.remark = customStyle.text.remark
  }
}

const drillJump = (index: number) => {
  state.drillClickDimensionList.splice(index)
  view.value.chartExtRequest = filter()
  calcData(view.value)
}

const onPointClick = param => {
  try {
    const msg = {
      sourceDvId: dvInfo.value.id,
      sourceViewId: view.value.id,
      message: Base64.encode(JSON.stringify(param))
    }
    emit('onPointClick', msg)
  } catch (e) {
    console.warn('de_inner_params send error')
  }
}

const chartClick = param => {
  // 下钻字段第一个没有在维度中不允许下钻
  const xIds = view.value.xAxis.map(ele => ele.id)
  if (xIds.indexOf(props.view.drillFields[0].id) == -1) {
    ElMessage.error(t('chart.drill_field_error'))
    return
  }
  if (view.value.type === 'circle-packing' && param.data.name === t('commons.all')) {
    ElMessage.error(t('chart.last_layer'))
    return
  }
  if (state.drillClickDimensionList.length < props.view.drillFields.length - 1) {
    state.drillClickDimensionList.push({
      dimensionList: param.data.dimensionList,
      extra: param.extra
    })
    view.value.chartExtRequest = filter()
    calcData(view.value)
  } else if (props.view.drillFields.length > 0) {
    ElMessage.error(t('chart.last_layer'))
  }
}

// 仪表板和大屏所有额外过滤参数都在此处
const filter = (firstLoad?: boolean) => {
  const { filter } = useFilter(view.value.id, firstLoad)
  const result = {
    user: wsCache.get('user.uid'),
    filter,
    linkageFilters: element.value.linkageFilters,
    outerParamsFilters: element.value.outerParamsFilters,
    webParamsFilters: element.value.webParamsFilters,
    drill: state.drillClickDimensionList,
    resultCount: resultCount.value,
    resultMode: resultMode.value
  }
  // 定时报告相关勿动
  if (route.path === '/preview' && route.query.taskId) {
    const sceneId = view.value['sceneId']
    const filterJson = window[`de-report-filter-${sceneId}`]
    let filterObj = {}
    if (filterJson) {
      filterObj = JSON.parse(filterJson)
    }
    filterObj[view.value.id] = result
    window[`de-report-filter-${sceneId}`] = JSON.stringify(filterObj)
  }
  return result
}

const onDrillFilters = param => {
  state.drillFilters = param ? param : []
}
const openHandler = ref(null)
const initOpenHandler = newWindow => {
  if (openHandler?.value) {
    const pm = {
      methodName: 'initOpenHandler',
      args: newWindow
    }
    openHandler.value.invokeMethod(pm)
  }
}

const divEmbedded = type => {
  useEmitt().emitter.emit('changeCurrentComponent', type)
}

const windowsJump = (url, jumpType, size = 'middle') => {
  try {
    let newWindow
    if ('newPop' === jumpType) {
      dePreviewPopDialogRef.value.previewInit({ url, size })
    } else if ('_self' === jumpType) {
      newWindow = window.open(url, jumpType)
      if (inMobile.value) {
        window.location.reload()
      }
    } else {
      newWindow = window.open(url, jumpType)
    }
    initOpenHandler(newWindow)
  } catch (e) {
    console.warn(t('visualization.url_check_error') + ':' + url)
  }
}

const jumpClick = param => {
  let dimension, jumpInfo, sourceInfo
  // 如果有名称name 获取和name匹配的dimension 否则倒序取最后一个能匹配的
  if (param.name) {
    param.dimensionList.forEach(dimensionItem => {
      if (dimensionItem.id === param.name || dimensionItem.value === param.name) {
        dimension = dimensionItem
        sourceInfo = param.viewId + '#' + dimension.id
        jumpInfo = nowPanelJumpInfo.value[sourceInfo]
      }
    })
  } else {
    for (let i = param.dimensionList.length - 1; i >= 0; i--) {
      dimension = param.dimensionList[i]
      sourceInfo = param.viewId + '#' + dimension.id
      jumpInfo = nowPanelJumpInfo.value[sourceInfo]
      if (jumpInfo) {
        break
      }
    }
  }
  if (jumpInfo) {
    // 维度日期类型转换
    viewFieldTimeTrans(dvMainStore.getViewDataDetails(param.viewId), param)
    param.sourceDvId = dvInfo.value.id
    param.sourceViewId = param.viewId
    param.sourceFieldId = dimension.id
    let embeddedBaseUrl = ''
    const divSelf = isDataEaseBi.value && jumpInfo.jumpType === '_self'
    const iframeSelf = isIframe.value && jumpInfo.jumpType === '_self'
    if (isDataEaseBi.value) {
      embeddedBaseUrl = embeddedStore.baseUrl
    }
    const jumpInfoParam = `&jumpInfoParam=${encodeURIComponent(
      Base64.encode(JSON.stringify(param))
    )}`

    // 内部仪表板跳转
    if (jumpInfo.linkType === 'inner') {
      if (jumpInfo.targetDvId) {
        const editPreviewParams = ['canvas', 'edit-preview'].includes(showPosition.value)
          ? '&editPreview=true'
          : ''
        const filterOuterParams = {}
        const curFilter = dvMainStore.getLastViewRequestInfo(param.viewId)
        const targetViewInfoList = jumpInfo.targetViewInfoList
        if (
          curFilter &&
          curFilter.filter &&
          curFilter.filter.length > 0 &&
          targetViewInfoList &&
          targetViewInfoList.length > 0
        ) {
          // do filter
          curFilter.filter.forEach(filterItem => {
            targetViewInfoList.forEach(targetViewInfo => {
              if (targetViewInfo.sourceFieldActiveId === filterItem.filterId) {
                filterOuterParams[targetViewInfo.outerParamsName] = {
                  operator: filterItem.operator,
                  value: filterItem.value
                }
              }
            })
          })
        }
        let attachParamsInfo
        if (Object.keys(filterOuterParams).length > 0) {
          filterOuterParams['outerParamsVersion'] = 'v2'
          attachParamsInfo =
            '&attachParams=' + encodeURIComponent(Base64.encode(JSON.stringify(filterOuterParams)))
        }
        // 携带外部参数
        if (publicLinkStatus.value) {
          // 判断是否有公共链接ID
          if (jumpInfo.publicJumpId) {
            let url = `${embeddedBaseUrl}#/de-link/${jumpInfo.publicJumpId}?fromLink=true&dvType=${jumpInfo.targetDvType}`
            if (attachParamsInfo) {
              url = url + attachParamsInfo + jumpInfoParam + editPreviewParams
            } else {
              url = url + '&ignoreParams=true' + jumpInfoParam + editPreviewParams
            }
            const currentUrl = window.location.href
            localStorage.setItem('beforeJumpUrl', currentUrl)
            windowsJump(url, jumpInfo.jumpType, jumpInfo.windowSize)
          } else {
            ElMessage.warning(t('visualization.public_link_tips'))
          }
        } else {
          let url = `${embeddedBaseUrl}#/preview?dvId=${jumpInfo.targetDvId}&fromLink=true&dvType=${jumpInfo.targetDvType}`
          if (attachParamsInfo) {
            url = url + attachParamsInfo + jumpInfoParam + editPreviewParams
          } else {
            url = url + '&ignoreParams=true' + jumpInfoParam + editPreviewParams
          }
          const currentUrl = window.location.href
          localStorage.setItem('beforeJumpUrl', currentUrl)
          if (divSelf || iframeSelf) {
            embeddedStore.setDvId(jumpInfo.targetDvId)
            embeddedStore.setJumpInfoParam(encodeURIComponent(Base64.encode(JSON.stringify(param))))
            divEmbedded('Preview')
            return
          }
          windowsJump(url, jumpInfo.jumpType, jumpInfo.windowSize)
        }
      } else {
        ElMessage.warning('未指定跳转仪表板')
      }
    } else {
      const colList = [...param.dimensionList, ...param.quotaList]
      let url = setIdValueTrans('id', 'value', jumpInfo.content, colList)
      url = checkAddHttp(url)

      if (isIframe.value || isDataEaseBi.value) {
        embeddedStore.clearState()
      }
      if (divSelf) {
        embeddedStore.setOuterUrl(url)
        divEmbedded('Iframe')
        return
      }

      windowsJump(url, jumpInfo.jumpType, jumpInfo.windowSize)
    }
  } else {
  }
}

const queryData = (firstLoad = false) => {
  if (loading.value) {
    return
  }
  const searched = dvMainStore.firstLoadMap.includes(element.value.id)
  const queryFilter = filter(searched ? false : firstLoad)
  let params = cloneDeep(view.value)
  params['chartExtRequest'] = queryFilter
  chartExtRequest.value = queryFilter
  calcData(params)
}

const calcData = params => {
  console.log('图表组件 calcData 调用:', {
    viewId: params.id,
    datasourceType: params.datasourceType,
    hasData: !!params.data,
    hasRestConfig: !!params.restConfig,
    params: params
  })

  dvMainStore.setLastViewRequestInfo(params.id, params.chartExtRequest)
  if (chartComponent?.value) {
    loading.value = true
    if (view.value.isPlugin) {
      chartComponent?.value?.invokeMethod({
        methodName: 'calcData',
        args: [
          params,
          () => {
            loading.value = false
          }
        ]
      })
    } else {
      chartComponent?.value?.calcData?.(params, () => {
        loading.value = false
      })
    }
  }
}

/**
 * 自动初始化REST组件数据
 * 在组件挂载时自动调用REST接口并为组件赋值
 */
const autoInitRestComponent = async () => {
  try {
    const { callRestApi, preprocessRestData } = await import('@/utils/restApi')

    const restConfig = view.value.restConfig
    const restFields = view.value.restFields

    if (!restConfig || !restConfig.url) {
      console.warn('REST配置不完整，跳过自动初始化')
      return
    }

    if (!restFields || restFields.length === 0) {
      console.warn('REST字段配置不完整，跳过自动初始化')
      return
    }

    console.log(`开始自动初始化REST组件 ${view.value.id}:`, {
      url: restConfig.url,
      method: restConfig.method,
      fieldsCount: restFields.length
    })

    // 检查是否包含全局变量并确保已解析
    const hasGlobalVariables = /\$\{[^}]+\}|\{\{[^}]+\}\}/.test(restConfig.url) ||
      (restConfig.headers && restConfig.headers.some(h => /\$\{[^}]+\}|\{\{[^}]+\}\}/.test(h.value))) ||
      (restConfig.params && restConfig.params.some(p => /\$\{[^}]+\}|\{\{[^}]+\}\}/.test(p.value)))

    if (hasGlobalVariables) {
      const { dvMainStoreWithOut } = await import('@/store/modules/data-visualization/dvMain')
      const dvMainStore = dvMainStoreWithOut()
      console.log(`组件 ${view.value.id} 包含全局变量引用，当前全局变量值:`, dvMainStore.globalVariableValues)

      // 确保全局变量已解析
      dvMainStore.resolveGlobalVariables()
      console.log(`重新解析后的全局变量值:`, dvMainStore.globalVariableValues)
    }

    loading.value = true

    // 调用REST API获取数据
    const restData = await callRestApi(restConfig, {
      enableMockData: false,
      logPrefix: `组件${view.value.id}`
    })

    // 预处理数据
    const processedData = preprocessRestData(restData, restConfig)

    console.log(`组件 ${view.value.id} REST数据获取成功:`, {
      originalDataType: typeof restData,
      processedDataLength: processedData.length,
      sampleData: processedData.slice(0, 2)
    })

    // 转换数据为图表格式
    const chartData = transformRestDataToChartFormat(processedData, restFields, view.value)

    console.log(`组件 ${view.value.id} 数据转换完成:`, {
      tableRowCount: chartData.tableRow?.length || 0,
      fieldsCount: chartData.fields?.length || 0,
      seriesCount: chartData.series?.length || 0,
      chartDataCount: chartData.data?.length || 0,
      sampleTableRow: chartData.tableRow?.slice(0, 2),
      sampleChartData: chartData.data?.slice(0, 3),
      fields: chartData.fields,
      currentXAxis: view.value.xAxis,
      currentYAxis: view.value.yAxis
    })

    // 检查并自动配置轴字段（如果尚未配置）
    const needsAxisConfig = !view.value.xAxis?.length || !view.value.yAxis?.length
    if (needsAxisConfig) {
      console.log(`组件 ${view.value.id} 需要自动配置轴字段`)
      autoConfigureAxisFields(view.value, chartData.fields)
    }

    // 确保图表有必要的格式化配置，避免unitLanguage错误
    ensureFormatterConfig(view.value)

    // 将数据赋值给组件并触发渲染
    const viewWithData = {
      ...view.value,
      data: chartData,
      datasourceType: 'rest'
    }

    // 设置组件状态为ready，确保chartAreaShow返回true
    element.value['state'] = 'ready'

    // 对于表格控件，需要确保数据格式正确
    if (view.value.type && (view.value.type.includes('table') || view.value.type.includes('Table'))) {
      console.log(`表格控件 ${view.value.id} 数据格式检查:`, {
        hasTableRow: !!chartData.tableRow,
        tableRowLength: chartData.tableRow?.length || 0,
        hasFields: !!chartData.fields,
        fieldsLength: chartData.fields?.length || 0,
        sampleRow: chartData.tableRow?.[0]
      })
    }

    // 直接调用calcData进行渲染
    calcData(viewWithData)

    console.log(`组件 ${view.value.id} REST数据自动初始化完成，状态设置为ready`)
  } catch (error) {
    console.error(`组件 ${view.value.id} REST自动初始化失败:`, error)
    loading.value = false
    // 如果REST初始化失败，回退到正常的查询流程
    queryData(!showPosition.value.includes('viewDialog'))
  }
}

/**
 * 自动配置图表轴字段
 * @param view 视图对象
 * @param fields 字段列表
 */
const autoConfigureAxisFields = (view: any, fields: any[]) => {
  if (!fields || fields.length === 0) {
    console.warn('没有可用字段进行轴配置')
    return
  }

  // 查找维度字段（文本、时间类型）用于X轴
  const dimensionFields = fields.filter(field => field.deType === 0 || field.deType === 1)
  // 查找指标字段（数值类型）用于Y轴
  const measureFields = fields.filter(field => field.deType === 2)

  console.log('自动配置轴字段:', {
    totalFields: fields.length,
    dimensionFields: dimensionFields.length,
    measureFields: measureFields.length,
    fields: fields.map(f => ({ name: f.name, deType: f.deType }))
  })

  // 配置X轴（维度）
  if (!view.xAxis?.length && dimensionFields.length > 0) {
    view.xAxis = [dimensionFields[0]]
    console.log('自动配置X轴字段:', dimensionFields[0].name)
  }

  // 配置Y轴（指标）
  if (!view.yAxis?.length && measureFields.length > 0) {
    view.yAxis = [measureFields[0]]
    console.log('自动配置Y轴字段:', measureFields[0].name)
  }

  // 如果没有合适的维度字段，使用第一个字段作为X轴
  if (!view.xAxis?.length && fields.length > 0) {
    view.xAxis = [fields[0]]
    console.log('使用第一个字段作为X轴:', fields[0].name)
  }

  // 如果没有合适的指标字段，使用最后一个字段作为Y轴
  if (!view.yAxis?.length && fields.length > 1) {
    view.yAxis = [fields[fields.length - 1]]
    console.log('使用最后一个字段作为Y轴:', fields[fields.length - 1].name)
  }
}

/**
 * 将REST数据转换为图表组件可用的格式
 * @param processedData 预处理后的数据
 * @param restFields REST字段配置
 * @param view 视图配置
 * @returns 图表数据格式
 */
const transformRestDataToChartFormat = (processedData: any[], restFields: any[], view: any) => {
  if (!processedData || processedData.length === 0) {
    return { tableRow: [], fields: [], series: [] }
  }

  // 获取启用的字段
  const enabledFields = restFields.filter(field => field.enabled !== false)

  // 调试字段配置
  console.log('transformRestDataToChartFormat 接收到的字段配置:', {
    totalFields: restFields.length,
    enabledFields: enabledFields.length,
    nameFieldConfig: restFields.find(f => f.name === 'name'),
    allFieldTypes: restFields.map(f => ({ name: f.name, type: f.type, enabled: f.enabled }))
  })

  // 强制修正name字段的类型配置
  const correctedFields = enabledFields.map(field => {
    if (field.name === 'name' && field.type === 'number') {
      console.log('检测到name字段类型错误，强制修正为string类型')
      return { ...field, type: 'string' }
    }
    return field
  })

  // 转换数据行 - 同时生成数组和对象格式
  const tableRow = processedData.map((item, itemIndex) => {
    const row = []
    const rowObj = {}

    correctedFields.forEach((field, fieldIndex) => {
      let rawValue

      // 如果数据是数组格式，使用字段索引或配置的索引
      if (Array.isArray(item)) {
        const index = field.columnIndex !== undefined ? field.columnIndex : fieldIndex
        rawValue = index < item.length ? item[index] : undefined

        console.log(`数组数据字段映射 - 字段: ${field.name}, 索引: ${index}, 值: ${rawValue}`)
      } else {
        // 如果数据是对象格式，使用路径获取
        const fieldPath = field.restPath || field.path || field.name || field.dataeaseName
        rawValue = getValueByPath(item, fieldPath)

        // console.log(`对象数据字段映射 - 字段: ${field.name}, 路径: ${fieldPath}, 值: ${rawValue}`)
      }

      const value = convertFieldValue(rawValue, field.type, field.name)

      // 为第一行添加详细的字段转换调试
      if (itemIndex === 0) {
        console.log(`字段转换调试 - ${field.name}:`, {
          fieldType: field.type,
          rawValue: rawValue,
          rawValueType: typeof rawValue,
          convertedValue: value,
          convertedValueType: typeof value,
          fieldPath: field.restPath || field.path || field.name || field.dataeaseName
        })
      }

      row.push(value)
      // S2表格需要对象格式，使用字段名作为key
      rowObj[field.name] = value
    })

    // 为第一行添加调试信息
    if (itemIndex === 0) {
      console.log('第一行数据对象格式:', rowObj)
      console.log('第一行数据数组格式:', row)
    }

    // 返回对象格式，S2表格需要这种格式
    return rowObj
  })

  // 同时生成数组格式，用于兼容性
  const tableRowArray = processedData.map((item, itemIndex) => {
    const row = []
    enabledFields.forEach((field, fieldIndex) => {
      let rawValue

      if (Array.isArray(item)) {
        const index = field.columnIndex !== undefined ? field.columnIndex : fieldIndex
        rawValue = index < item.length ? item[index] : undefined
      } else {
        const fieldPath = field.restPath || field.path || field.name || field.dataeaseName
        rawValue = getValueByPath(item, fieldPath)
      }

      const value = convertFieldValue(rawValue, field.type, field.name)
      row.push(value)
    })
    return row
  })

  // 转换字段定义
  const fields = correctedFields.map(field => ({
    id: field.id,
    name: field.name,
    dataeaseName: field.name,
    type: getDeTypeFromRestType(field.type),
    deType: getDeTypeFromRestType(field.type),
    deExtractType: getDeTypeFromRestType(field.type),
    extField: 0,
    checked: true,
    columnIndex: field.columnIndex || 0,
    lastSyncTime: Date.now()
  }))

  // 为指标卡等组件生成series数据
  const series = correctedFields
    .filter(field => field.type === 'number')
    .map((field) => ({
      id: field.id,
      name: field.name,
      data: tableRow.map(row => row[field.name] || 0)
    }))

  // 为G2Plot图表生成特定的数据格式
  const chartData = generateChartData(processedData, enabledFields, view)

  return {
    tableRow,
    fields,
    series,
    sourceData: processedData,
    data: chartData // 添加G2Plot需要的数据格式
  }
}

/**
 * 确保图表有必要的格式化配置，避免unitLanguage等属性未定义的错误
 * @param view 视图对象
 */
const ensureFormatterConfig = (view: any) => {
  try {
    // 确保customStyle存在
    if (!view.customStyle) {
      view.customStyle = {}
    }

    // 解析customStyle（如果是字符串）
    let customStyle = view.customStyle
    if (typeof customStyle === 'string') {
      try {
        customStyle = JSON.parse(customStyle)
        view.customStyle = customStyle
      } catch (e) {
        console.warn('解析customStyle失败，使用默认配置')
        customStyle = {}
        view.customStyle = customStyle
      }
    }

    // 创建默认的格式化配置
    const createDefaultFormatter = () => ({
      type: 'auto',
      unitLanguage: 'ch',
      unit: 1,
      suffix: '',
      decimalCount: 2,
      thousandSeparator: true
    })

    // 确保轴配置存在
    if (!customStyle.xAxis) {
      customStyle.xAxis = {
        axisLabelFormatter: createDefaultFormatter()
      }
    } else if (!customStyle.xAxis.axisLabelFormatter) {
      customStyle.xAxis.axisLabelFormatter = createDefaultFormatter()
    } else {
      // 确保现有配置包含所有必要属性
      const formatter = customStyle.xAxis.axisLabelFormatter
      if (formatter.unitLanguage === undefined) formatter.unitLanguage = 'ch'
      if (formatter.unit === undefined) formatter.unit = 1
      if (formatter.type === undefined) formatter.type = 'auto'
      if (formatter.suffix === undefined) formatter.suffix = ''
      if (formatter.decimalCount === undefined) formatter.decimalCount = 2
      if (formatter.thousandSeparator === undefined) formatter.thousandSeparator = true
    }

    if (!customStyle.yAxis) {
      customStyle.yAxis = {
        axisLabelFormatter: createDefaultFormatter()
      }
    } else if (!customStyle.yAxis.axisLabelFormatter) {
      customStyle.yAxis.axisLabelFormatter = createDefaultFormatter()
    } else {
      // 确保现有配置包含所有必要属性
      const formatter = customStyle.yAxis.axisLabelFormatter
      if (formatter.unitLanguage === undefined) formatter.unitLanguage = 'ch'
      if (formatter.unit === undefined) formatter.unit = 1
      if (formatter.type === undefined) formatter.type = 'auto'
      if (formatter.suffix === undefined) formatter.suffix = ''
      if (formatter.decimalCount === undefined) formatter.decimalCount = 2
      if (formatter.thousandSeparator === undefined) formatter.thousandSeparator = true
    }

    console.log(`组件 ${view.id} 格式化配置检查完成`)
  } catch (error) {
    console.error(`组件 ${view.id} 格式化配置检查失败:`, error)
  }
}

/**
 * 为G2Plot图表生成数据格式
 * @param processedData 处理后的原始数据
 * @param enabledFields 启用的字段配置
 * @param view 视图配置，用于获取实际配置的轴字段
 * @returns G2Plot图表数据
 */
const generateChartData = (processedData: any[], enabledFields: any[], view?: any) => {
  if (!processedData || processedData.length === 0 || !enabledFields || enabledFields.length === 0) {
    return []
  }

  // 获取图表实际配置的轴字段
  const configuredXAxisFields = view?.xAxis || []
  const configuredYAxisFields = view?.yAxis || []

  console.log('图表轴配置检查:', {
    viewId: view?.id,
    configuredXAxisCount: configuredXAxisFields.length,
    configuredYAxisCount: configuredYAxisFields.length,
    configuredXAxisNames: configuredXAxisFields.map(f => f.name || f.dataeaseName),
    configuredYAxisNames: configuredYAxisFields.map(f => f.name || f.dataeaseName)
  })

  // 如果图表配置了轴字段，优先使用配置的字段
  let dimensionFields, measureFields

  if (configuredXAxisFields.length > 0 && configuredYAxisFields.length > 0) {
    // 使用图表配置的轴字段
    dimensionFields = configuredXAxisFields
    measureFields = configuredYAxisFields
    console.log('使用图表配置的轴字段')
  } else {
    // 回退到自动识别字段类型
    dimensionFields = enabledFields.filter(field => field.type === 'string' || field.type === 'date')
    measureFields = enabledFields.filter(field => field.type === 'number')
    console.log('使用自动识别的字段类型')
  }

  console.log('生成图表数据:', {
    totalData: processedData.length,
    dimensionFields: dimensionFields.length,
    measureFields: measureFields.length,
    dimensionFieldNames: dimensionFields.map(f => f.name || f.dataeaseName),
    measureFieldNames: measureFields.map(f => f.name || f.dataeaseName)
  })

  if (dimensionFields.length === 0 || measureFields.length === 0) {
    console.warn('缺少必要的维度或指标字段')
    return []
  }

  const chartData = []
  const dimensionField = dimensionFields[0] // 使用第一个维度字段作为X轴

  processedData.forEach(item => {
    const dimensionValue = getValueByPath(item, dimensionField.restPath || dimensionField.path || dimensionField.name || dimensionField.dataeaseName)

    // 为每个配置的指标字段生成一条数据
    measureFields.forEach(measureField => {
      const measureValue = getValueByPath(item, measureField.restPath || measureField.path || measureField.name || measureField.dataeaseName)

      chartData.push({
        field: String(dimensionValue || ''), // X轴值
        value: Number(measureValue || 0), // Y轴值
        category: measureField.name || measureField.dataeaseName // 系列名称
      })
    })
  })

  console.log('生成的图表数据样例:', {
    totalCount: chartData.length,
    sampleData: chartData.slice(0, 3),
    uniqueCategories: [...new Set(chartData.map(d => d.category))]
  })
  return chartData
}

/**
 * 根据路径获取对象值
 * @param obj 对象或数组
 * @param path 路径，支持点分隔或数组索引
 * @returns 值
 */
const getValueByPath = (obj: any, path: string): any => {
  if (!obj || !path) return undefined

  // 如果obj是数组且path是数字，直接按索引访问
  if (Array.isArray(obj) && /^\d+$/.test(path)) {
    const index = parseInt(path)
    return index < obj.length ? obj[index] : undefined
  }

  const keys = path.split('.')
  let result = obj

  for (const key of keys) {
    if (result && typeof result === 'object') {
      // 如果是数组且key是数字，按索引访问
      if (Array.isArray(result) && /^\d+$/.test(key)) {
        const index = parseInt(key)
        result = index < result.length ? result[index] : undefined
      } else if (key in result) {
        result = result[key]
      } else {
        return undefined
      }
    } else {
      return undefined
    }
  }

  return result
}

/**
 * 转换字段值
 * @param value 原始值
 * @param type 字段类型
 * @returns 转换后的值
 */
const convertFieldValue = (value: any, type: string, fieldName?: string): any => {
  if (value === null || value === undefined) {
    return null
  }

  switch (type) {
    case 'number':
      const num = parseFloat(value)
      return isNaN(num) ? 0 : num
    case 'date':
      return value instanceof Date ? value.getTime() : new Date(value).getTime()
    case 'boolean':
      return Boolean(value)
    case 'string':
    default:
      // 对于字符串类型，强制转换为字符串并处理特殊情况
      if (typeof value === 'string') {
        return value
      } else if (typeof value === 'number') {
        // 对于数字，转换为字符串时保持原始格式
        // 特殊处理：如果字段名包含"name"且是5位数字，可能需要前导零
        const strValue = String(value)
        if (type === 'string' && fieldName && fieldName.toLowerCase().includes('name')) {
          // 如果是5位数字，可能原本有前导零
          if (value >= 1000 && value <= 99999) {
            return strValue.padStart(5, '0')
          }
        }
        return strValue
      } else {
        return String(value)
      }
  }
}

/**
 * 将REST字段类型转换为DataEase字段类型
 * @param restType REST字段类型
 * @returns DataEase字段类型
 */
const getDeTypeFromRestType = (restType: string): number => {
  switch (restType) {
    case 'string':
      return 0 // 文本
    case 'date':
      return 1 // 时间
    case 'number':
      return 2 // 整型数值
    case 'boolean':
      return 4 // 布尔
    default:
      return 0
  }
}

const showChartView = (...libs: ChartLibraryType[]) => {
  if (view.value?.render && view.value?.type) {
    const chartView = chartViewManager.getChartView(view.value.render, view.value.type)
    return chartView && libs?.includes(chartView.library)
  } else {
    return false
  }
}

onBeforeMount(() => {
  if (!showPosition.value.includes('viewDialog')) {
    nextTick(() => {
      // 监听查询事件
      useEmitt({
        name: `query-data-${view.value.id}`,
        callback: queryData
      })

      // 监听查询参数更新事件（用于表单查询组件联动）
      useEmitt({
        name: `update-query-params-${view.value.id}`,
        callback: function (queryParams) {
          console.log('表格组件接收到查询参数更新:', {
            viewId: view.value.id,
            queryParams,
            isRestDataSource: view.value.datasourceType === 'rest'
          })

          // 只有REST数据源才处理查询参数更新
          if (view.value.datasourceType === 'rest' && view.value.restConfig) {
            // 更新REST配置中的查询参数
            if (!view.value.restConfig.params) {
              view.value.restConfig.params = []
            }

            // 记录当前查询参数的key，用于后续清理
            const currentQueryParamKeys = queryParams ? queryParams.map(p => p.key) : []

            // 清除之前的表单查询参数（保留非表单查询的参数）
            if (!view.value.restConfig._formQueryParamKeys) {
              view.value.restConfig._formQueryParamKeys = []
            }

            // 移除之前的表单查询参数
            view.value.restConfig.params = view.value.restConfig.params.filter(param =>
              !view.value.restConfig._formQueryParamKeys.includes(param.key)
            )

            // 更新表单查询参数key列表
            view.value.restConfig._formQueryParamKeys = [...currentQueryParamKeys]

            // 添加新的查询参数
            if (queryParams && Array.isArray(queryParams)) {
              queryParams.forEach(param => {
                if (param.key && param.value !== undefined && param.value !== null && param.value !== '') {
                  view.value.restConfig.params.push({
                    key: param.key,
                    value: String(param.value)
                  })
                }
              })
            }

            console.log('表格组件REST配置已更新:', {
              viewId: view.value.id,
              updatedParams: view.value.restConfig.params,
              formQueryParamKeys: view.value.restConfig._formQueryParamKeys
            })

            // 清除现有数据，强制重新获取
            if (view.value.data) {
              console.log('清除表格组件现有数据，准备重新获取')
              view.value.data = null
              // 清除缓存的allRows数据
              if (view.value.data && (view.value.data as any).allRows) {
                delete (view.value.data as any).allRows
              }
            }
          }
        }
      })
    })
  }
})
// 部分场景不需要更新图表，例如放大页面
const listenerEnable = computed(() => {
  return !showPosition.value.includes('viewDialog')
})
// 存储所有数据集字段，用于判断图表拖入的字段是否存在
const viewAllDatasetFields = new Map()
const showEmpty = ref(false)
const checkFieldIsAllowEmpty = (allField?) => {
  showEmpty.value = false
  if (view.value?.render && view.value?.type) {
    const chartView = chartViewManager.getChartView(view.value.render, view.value.type)
    // 插件
    if (!chartView) {
      return
    }
    const map = parseJson(view.value.customAttr).map
    if (['bubble-map', 'map'].includes(view.value?.type) && !map?.id) {
      showEmpty.value = true
      return
    }
    const axisConfigMap = new Map(Object.entries(chartView.axisConfig))
    // 验证拖入的字段是否包含在当前数据集字段中，如果有一个不在数据集字段中，则显示空图表
    let includeDatasetField = false
    if (allField && allField.length > 0) {
      viewAllDatasetFields.set(view.value.id, allField)
      outerLoop: for (const [key, value] of axisConfigMap) {
        // 只判断必须的
        if (value['allowEmpty']) continue
        if (!view.value?.[key]?.length) continue
        for (const item of view.value[key]) {
          if (!allField.find(field => field.id === item.id)) {
            includeDatasetField = true
            break outerLoop
          }
        }
      }
    }
    if (includeDatasetField) {
      showEmpty.value = true
      return
    }
    for (const [key, value] of axisConfigMap) {
      // 跳过允许为空的配置项
      if (value['allowEmpty']) continue

      // 如果有数据集字段并且字段值存在且不为空
      if (viewAllDatasetFields.get(view.value?.id)) {
        if (view.value?.[key]?.length) {
          // 检查图表字段是否有不在数据集中
          for (const item of view.value[key]) {
            if (!viewAllDatasetFields.get(view.value?.id).find(field => field.id === item.id)) {
              includeDatasetField = true
              break
            }
          }
        }
        // 如果有不在数据集中
        if (includeDatasetField) {
          showEmpty.value = true
          break
        }
      }

      // 如果没有限制长度，且值为空，标记为空并跳出
      if (!value['limit'] && view.value?.[key]?.length === 0) {
        showEmpty.value = true
        break
      }

      // 如果有限制长度，且字段长度不足，标记为空并跳出
      if (
        value['limit'] &&
        (!view.value?.[key] || view.value?.[key]?.length < parseInt(value['limit']))
      ) {
        showEmpty.value = true
        break
      }

      // 如果是table-info类型且字段为空，标记为空并跳出
      if (view.value?.type === 'table-info' && view.value?.[key]?.length === 0) {
        showEmpty.value = true
        break
      }
    }
  }
}
const changeChartType = () => {
  checkFieldIsAllowEmpty()
}
const changeDataset = () => {
  checkFieldIsAllowEmpty()
}

const loadPlugin = ref(false)

onMounted(() => {
  if (!view.value.isPlugin) {
    state.drillClickDimensionList = view.value?.chartExtRequest?.drill ?? []

    // 检查是否是REST数据源组件，如果是则自动获取数据
    // 增强条件判断，支持更多的REST数据源标识方式
    const isRestDataSource =
      view.value.datasourceType === 'rest' ||
      ((view.value as any).restConfig && (view.value as any).restFields)

    if (isRestDataSource && (view.value as any).restConfig && (view.value as any).restFields) {
      console.log('检测到REST组件，自动初始化数据:', {
        componentId: view.value.id,
        componentType: view.value.type,
        datasourceType: view.value.datasourceType,
        url: (view.value as any).restConfig.url,
        fieldsCount: (view.value as any).restFields.length
      })
      autoInitRestComponent()
    } else if (isRestDataSource) {
      // 如果是REST数据源但配置不完整，延迟检查
      console.log('检测到REST组件但配置不完整，延迟检查:', {
        componentId: view.value.id,
        componentType: view.value.type,
        hasRestConfig: !!(view.value as any).restConfig,
        hasRestFields: !!(view.value as any).restFields,
        restConfigUrl: (view.value as any).restConfig?.url
      })

      // 延迟100ms再次检查，给配置加载一些时间
      setTimeout(() => {
        if ((view.value as any).restConfig && (view.value as any).restFields) {
          console.log('延迟检查成功，开始初始化REST组件:', view.value.id)
          autoInitRestComponent()
        } else {
          console.log('延迟检查仍然失败，使用默认查询:', view.value.id)
          queryData(!showPosition.value.includes('viewDialog'))
        }
      }, 100)
    } else {
      queryData(!showPosition.value.includes('viewDialog'))
    }
  } else {
    const searched = dvMainStore.firstLoadMap.includes(element.value.id)
    const queryFilter = filter(!searched)
    view.value['chartExtRequest'] = queryFilter
    chartExtRequest.value = queryFilter
    loadPlugin.value = true
  }
  if (!listenerEnable.value) {
    return
  }
  useEmitt({
    name: 'checkShowEmpty',
    callback: param => {
      if (param.view?.id === view.value.id) {
        checkFieldIsAllowEmpty(param.allFields)
      }
    }
  })
  useEmitt({ name: 'chart-type-change', callback: changeChartType })
  useEmitt({ name: 'dataset-change', callback: changeDataset })
  useEmitt({
    name: 'clearPanelLinkage',
    callback: function (param) {
      if (param.viewId === 'all' || param.viewId === element.value.id) {
        chartComponent?.value?.clearLinkage?.()
      }
    }
  })
  useEmitt({
    name: 'snapshotChangeToView',
    callback: function (cacheViewInfo) {
      initTitle()
      nextTick(() => {
        if (
          cacheViewInfo.snapshotCacheViewCalc.includes(view.value.id) ||
          cacheViewInfo.snapshotCacheViewCalc.includes('all')
        ) {
          view.value.chartExtRequest = filter(false)
          calcData(view.value)
        } else if (
          cacheViewInfo.snapshotCacheViewRender.includes(view.value.id) ||
          cacheViewInfo.snapshotCacheViewRender.includes('all')
        ) {
          chartComponent?.value?.renderChart?.(view.value)
        }
      })
    }
  })

  useEmitt({
    name: 'calcData-' + view.value.id,
    callback: function (val) {
      console.log('图表包装器接收到 calcData 事件:', {
        viewId: view.value.id,
        hasVal: !!val,
        valKeys: val ? Object.keys(val) : [],
        valData: val?.data,
        initReady: state.initReady
      })

      if (!state.initReady) {
        console.log('图表包装器未初始化完成，跳过处理')
        return
      }
      initTitle()
      nextTick(() => {
        view.value.chartExtRequest = filter(false)
        const targetVal = val || view.value
        console.log('图表包装器调用 calcData:', {
          targetValKeys: Object.keys(targetVal),
          hasData: !!targetVal.data,
          dataContent: targetVal.data
        })
        calcData(targetVal)
      })
    }
  })

  useEmitt({
    name: 'calcData-all',
    callback: function () {
      if (!state.initReady) {
        return
      }
      initTitle()
      nextTick(() => {
        view.value.chartExtRequest = filter(false)
        calcData(view.value)
      })
    }
  })
  useEmitt({
    name: 'renderChart-' + view.value.id,
    callback: function (val) {
      if (!state.initReady) {
        return
      }
      initTitle()
      const viewInfo = val ? val : view.value
      nextTick(() => {
        if (view.value?.plugin?.isPlugin) {
          chartComponent?.value?.invokeMethod({
            methodName: 'renderChart',
            args: [viewInfo]
          })
          return
        }
        chartComponent?.value?.renderChart?.(viewInfo)
      })
    }
  })
  useEmitt({
    name: 'resetDrill-' + view.value.id,
    callback: function (val) {
      nextTick(() => {
        drillJump(val)
      })
    }
  })
  useEmitt({
    name: 'tabCanvasChange-' + element.value.canvasId,
    callback: function () {
      if (!state.initReady && !view.value.type.includes('table')) {
        return
      }
      setTimeout(function () {
        chartComponent?.value?.renderChart?.(view.value)
      }, 200)
    }
  })
  useEmitt({
    name: 'updateTitle-' + view.value.id,
    callback: () => {
      initTitle()
    }
  })
  useEmitt({
    name: 'chart-type-change-' + view.value.id,
    callback: () => {
      const chart = cloneDeep(view.value)
      chart.container =
        'container-' + showPosition.value + '-' + view.value.id + '-' + suffixId.value
      clearExtremum(chart)
    }
  })

  const { refreshViewEnable, refreshUnit, refreshTime } = view.value
  buildInnerRefreshTimer(refreshViewEnable, refreshUnit, refreshTime)

  initTitle()
})

// 1.开启仪表板刷新 2.首次加载（searchCount =0 ）3.正在请求数据 则显示加载状态
const loadingFlag = computed(() => {
  // 按钮组件不需要loading状态
  if (view.value?.type === 'button') {
    return false
  }
  // UserView组件的特定类型不需要loading状态
  if (element.value?.innerType && ['button', 'nested-menu'].includes(element.value.innerType)) {
    return false
  }
  return (
    (canvasStyleData.value.refreshViewLoading ||
      (searchCount.value === 0 && innerSearchCount === 0)) &&
    loading.value
  )
})

const chartAreaShow = computed(() => {

  if (view.value.tableId) {
    if (element.value['state'] === undefined || element.value['state'] === 'ready') {
      return true
    }
  }
  // 添加REST数据源的支持
  if (view.value.datasourceType === 'rest' && (view.value as any).restConfig && (view.value as any).restFields) {
    if (element.value['state'] === undefined || element.value['state'] === 'ready') {
      return true
    }
  }
  if (['rich-text', 'picture-group', 'button'].includes(view.value.type)) {
    return true
  }
  // 检查UserView组件的innerType
  if (element.value?.innerType && ['rich-text', 'button', 'nested-menu'].includes(element.value.innerType)) {
    return true
  }
  if (view.value?.isPlugin) {
    return true
  }
  if (view.value['dataFrom'] === 'template' || view.value['dataFrom'] === 'calc') {
    return true
  }
  if (view.value.customAttr?.map?.id) {
    const MAP_CHARTS = ['map', 'bubble-map', 'flow-map']
    if (MAP_CHARTS.includes(view.value.type)) {
      return true
    }
  }
  return false
})

const titleInputRef = ref()
const titleEditStatus = ref(false)
function changeEditTitle() {
  if (!props.active) {
    return
  }
  if (!titleEditStatus.value) {
    titleEditStatus.value = true
    nextTick(() => {
      titleInputRef.value?.focus()
      element.value['editing'] = true
    })
  }
}

function onLeaveTitleInput() {
  element.value['editing'] = false
  titleEditStatus.value = false
}

//v-click-outside 指令
const vClickOutside = {
  beforeMount(el, binding) {
    // 在元素上绑定一个事件监听器
    el.clickOutsideEvent = function (event) {
      // 判断点击事件是否发生在元素外部
      if (!(el === event.target || el.contains(event.target))) {
        // 如果是外部点击，则执行绑定的函数
        binding.value(event)
      }
    }
    // 在全局添加点击事件监听器
    document.addEventListener('click', el.clickOutsideEvent)
  },
  unmounted(el) {
    // 在组件销毁前，移除事件监听器以避免内存泄漏
    document.removeEventListener('click', el.clickOutsideEvent)
  }
}

function onTitleChange() {
  element.value.name = view.value.title
  element.value.label = view.value.title
  snapshotStore.recordSnapshotCache('onTitleChange')
}

const toolTip = computed(() => {
  return props.themes === 'dark' ? 'light' : 'dark'
})

const marginBottom = computed<string | 0>(() => {
  if (!titleShow.value) {
    return 0
  }
  if (titleShow.value || trackMenu.value.length > 0 || state.title_remark.show) {
    return 12 * scale.value + 'px'
  }
  return 0
})

const iconSize = computed<string>(() => {
  return 16 * scale.value + 'px'
})
/**
 * 修改透明度
 * 边框透明度为0时会是存色，顾配置低透明度
 * @param {boolean} isBorder 是否为边框
 */
const modifyAlpha = isBorder => {
  const { backgroundColor, backgroundType, backgroundImageEnable, backgroundColorSelect } =
    element.value.commonBackground
  // 透明
  const transparent = 'rgba(0,0,0,0.01)'
  // 背景图时，设置透明度为0.01
  if (backgroundType === 'outerImage' && backgroundImageEnable) return transparent
  // hex转rgba
  if (backgroundColor.includes('#'))
    return isBorder || !backgroundColorSelect ? transparent : backgroundColor
  const match = backgroundColor.match(/rgba\((\d+), (\d+), (\d+), (\d+|0.\d+)\)/)
  if (!match) return backgroundColor
  const [r, g, b, a] = match.slice(1).map(Number)
  // 边框或者不设置背景色时，设置透明度为0.01，否则原透明度
  return `rgba(${r}, ${g}, ${b}, ${!backgroundColorSelect || isBorder ? 0.01 : a})`
}

const titleIconStyle = computed(() => {
  const bgColor = modifyAlpha(false)
  const borderColor = modifyAlpha(true)
  // 不显示标题时，图标的样式
  const style = {
    position: 'absolute',
    border: `1px solid ${borderColor}`,
    'background-color': bgColor,
    'border-radius': '2px',
    padding: '0 2px 0 2px',
    'z-index': 1,
    top: '2px',
    left: '2px',
    ...(trackMenu.value.length ? {} : { display: 'none' })
  }
  return {
    color: canvasStyleData.value.component.seniorStyleSetting.linkageIconColor,
    ...(titleShow.value ? {} : style)
  }
})
const chartHover = ref(false)
const showActionIcons = computed(() => {
  if (!chartHover.value) {
    return false
  }
  return trackMenu.value.length > 0 || state.title_remark.show
})
const chartConfigs = ref(CHART_TYPE_CONFIGS)
const pluginLoaded = computed(() => {
  let result = false
  chartConfigs.value.forEach(cat => {
    result = cat.details.find(chart => view.value?.type === chart.value) !== undefined
  })
  return result
})
// TODO 统一加载
const loadPluginCategory = data => {
  data.forEach(item => {
    const { category, title, render, chartValue, chartTitle, icon, staticMap } = item
    const node = {
      render,
      category,
      icon,
      value: chartValue,
      title: chartTitle,
      isPlugin: true,
      staticMap
    }
    if (view.value?.type === node.value) {
      view.value.plugin = {
        isPlugin: true,
        staticMap
      }
    }
    const stack = [...chartConfigs.value]
    let findParent = false
    while (stack?.length) {
      const parent = stack.pop()
      if (parent.category === category) {
        const chart = parent.details.find(chart => chart.value === node.value)
        if (!chart) {
          parent.details.push(node)
        }
        findParent = true
      }
    }
    if (!findParent) {
      stack.push({
        category,
        title,
        display: 'show',
        details: [node]
      })
    }
  })
}

const allEmptyCheck = computed(() => {
  return ['rich-text', 'picture-group'].includes(element.value.innerType)
})
/**
 * 标题提示的最大宽度
 */
const titleTooltipWidth = computed(() => {
  if (inMobile.value) {
    return `${screen.width - 10}px`
  }
  if (mobileInPc.value) {
    return '270px'
  }
  return '500px'
})
const clearG2Tooltip = () => {
  const g2TooltipWrapper = document.getElementById('g2-tooltip-wrapper')
  if (g2TooltipWrapper) {
    for (const ele of g2TooltipWrapper.children) {
      ele.style.display = 'none'
    }
  }
}
</script>

<template>

  <div
    class="chart-area report-load"
    :class="{
      'report-load-finish': !loadingFlag,
      'button-container': view?.type === 'button'
    }"
    v-loading="loadingFlag"
    element-loading-background="rgba(0,0,0,0)"
    @mouseover="chartHover = true"
    @mouseleave="chartHover = false"
  >
    <div
      class="title-container"
      :style="{ 'justify-content': titleAlign, 'margin-bottom': marginBottom }"
    >
      <template v-if="!titleEditStatus">
        <p v-if="titleShow" :style="state.title_class" @dblclick="changeEditTitle">
          {{ view.title }}
        </p>
      </template>
      <template v-else>
        <el-input
          style="flex: 1"
          :effect="canvasStyleData.dashboard.themeColor"
          ref="titleInputRef"
          v-model="view.title"
          @keydown.stop
          @keydown.enter="onLeaveTitleInput"
          v-click-outside="onLeaveTitleInput"
          @change="onTitleChange"
        />
      </template>
      <transition name="fade">
        <div
          class="icons-container"
          :class="{ 'is-editing': titleEditStatus }"
          :style="titleIconStyle"
          v-show="showActionIcons"
        >
          <el-tooltip :effect="toolTip" placement="top" v-if="state.title_remark.show">
            <template #content>
              <div
                :style="{
                  maxWidth: titleTooltipWidth,
                  wordBreak: 'break-all',
                  wordWrap: 'break-word',
                  whiteSpace: 'pre-wrap'
                }"
                v-html="state.title_remark.remark"
              ></div>
            </template>
            <el-icon :size="iconSize" class="inner-icon">
              <Icon name="icon_info_outlined"><icon_info_outlined class="svg-icon" /></Icon>
            </el-icon>
          </el-tooltip>
          <el-tooltip :effect="toolTip" placement="top" content="已设置联动" v-if="hasLinkIcon">
            <el-icon :size="iconSize" class="inner-icon">
              <Icon name="icon_link-record_outlined"
                ><icon_linkRecord_outlined class="svg-icon"
              /></Icon>
            </el-icon>
          </el-tooltip>
          <el-tooltip
            :effect="toolTip"
            placement="top"
            :content="t('visualization.jump_set_tips')"
            v-if="hasJumpIcon"
          >
            <el-icon :size="iconSize" class="inner-icon">
              <Icon name="icon_viewinchat_outlined"
                ><icon_viewinchat_outlined class="svg-icon"
              /></Icon>
            </el-icon>
          </el-tooltip>
          <el-tooltip
            :effect="toolTip"
            placement="top"
            :content="t('visualization.drill_set_tips')"
            v-if="hasDrillIcon"
          >
            <el-icon :size="iconSize" class="inner-icon">
              <Icon name="icon_drilling_outlined"><icon_drilling_outlined class="svg-icon" /></Icon>
            </el-icon>
          </el-tooltip>
        </div>
      </transition>
    </div>
    <!--这里去渲染不同图库的图表-->
    <div v-if="allEmptyCheck || (chartAreaShow && !showEmpty)" style="flex: 1; overflow: hidden">
      <plugin-component
        v-if="view.plugin?.isPlugin && loadPlugin"
        :jsname="view.plugin.staticMap['index']"
        :scale="scale"
        :dynamic-area-id="dynamicAreaId"
        :view="view"
        :show-position="showPosition"
        :element="element"
        :request="request"
        :emitter="emitter"
        :store="store"
        :suffixId="suffixId"
        ref="chartComponent"
        @onChartClick="chartClick"
        @onPointClick="onPointClick"
        @onDrillFilters="onDrillFilters"
        @onJumpClick="jumpClick"
        @resetLoading="() => (loading = false)"
      />
      <de-picture-group
        v-else-if="showChartView(ChartLibraryType.PICTURE_GROUP)"
        :themes="canvasStyleData.dashboard.themeColor"
        ref="chartComponent"
        :element="element"
        :active="active"
        :view="view"
        :show-position="showPosition"
        :suffixId="suffixId"
      >
      </de-picture-group>
      <de-rich-text-view
        v-else-if="element.innerType === 'rich-text'"
        :scale="scale"
        :themes="canvasStyleData.dashboard.themeColor"
        ref="chartComponent"
        :element="element"
        :disabled="!['canvas', 'canvasDataV'].includes(showPosition) || disabled"
        :active="active"
        :show-position="showPosition"
        :edit-mode="editMode"
        :suffixId="suffixId"
      />
      <de-button-view
        v-else-if="element.innerType === 'button'"
        :scale="scale"
        :themes="canvasStyleData.dashboard.themeColor"
        ref="chartComponent"
        :element="element"
        :active="active"
        :show-position="showPosition"
        :suffixId="suffixId"
        @onComponentEvent="() => emit('onComponentEvent')"
        @onPointClick="onPointClick"
      />

      <de-nested-menu-view
        v-else-if="element.innerType === 'nested-menu'"
        :scale="scale"
        :themes="canvasStyleData.dashboard.themeColor"
        ref="chartComponent"
        :element="element"
        :active="active"
        :show-position="showPosition"
        :suffixId="suffixId"
        @onComponentEvent="() => emit('onComponentEvent')"
        @onPointClick="onPointClick"
      />

      <de-indicator
        :scale="scale"
        v-else-if="showChartView(ChartLibraryType.INDICATOR)"
        :themes="canvasStyleData.dashboard.themeColor"
        ref="chartComponent"
        :view="view"
        :element="element"
        :show-position="showPosition"
        :suffixId="suffixId"
        :font-family="fontFamily"
        :common-params="commonParams"
        @touchstart="clearG2Tooltip"
        @onChartClick="chartClick"
        @onPointClick="onPointClick"
        @onDrillFilters="onDrillFilters"
        @onJumpClick="jumpClick"
        @onComponentEvent="() => emit('onComponentEvent')"
      />
      <chart-component-g2-plot
        :scale="scale"
        :dynamic-area-id="dynamicAreaId"
        :view="view"
        :show-position="showPosition"
        :element="element"
        :suffixId="suffixId"
        :font-family="fontFamily"
        :active="active"
        v-else-if="
          showChartView(ChartLibraryType.G2_PLOT, ChartLibraryType.L7_PLOT, ChartLibraryType.L7)
        "
        ref="chartComponent"
        @onChartClick="chartClick"
        @onPointClick="onPointClick"
        @onDrillFilters="onDrillFilters"
        @onJumpClick="jumpClick"
        @resetLoading="() => (loading = false)"
      />
      <chart-component-s2
        :view="view"
        :scale="scale"
        :show-position="showPosition"
        :element="element"
        :drill-length="drillClickLength"
        :font-family="fontFamily"
        v-else-if="showChartView(ChartLibraryType.S2)"
        ref="chartComponent"
        @onPointClick="onPointClick"
        @onChartClick="chartClick"
        @onDrillFilters="onDrillFilters"
        @onJumpClick="jumpClick"
        :suffixId="suffixId"
      />
    </div>
    <chart-empty-info
      v-if="(!chartAreaShow || showEmpty) && !allEmptyCheck"
      :themes="canvasStyleData.dashboard.themeColor"
      :view-icon="view.type"
      @touchstart="clearG2Tooltip"
    ></chart-empty-info>
    <drill-path
      :disabled="optType === 'enlarge'"
      :drill-filters="state.drillFilters"
      @onDrillJump="drillJump"
    />
    <XpackComponent
      ref="openHandler"
      jsname="L2NvbXBvbmVudC9lbWJlZGRlZC1pZnJhbWUvT3BlbkhhbmRsZXI="
    />
    <XpackComponent
      v-if="!pluginLoaded && view.isPlugin"
      jsname="L2NvbXBvbmVudC9wbHVnaW5zLWhhbmRsZXIvVmlld0NhdGVnb3J5SGFuZGxlcg=="
      @load-plugin-category="loadPluginCategory"
    />
    <DePreviewPopDialog ref="dePreviewPopDialogRef"></DePreviewPopDialog>
  </div>
</template>

<style lang="less" scoped>
.chart-area {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &.button-container {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }
}
.title-container {
  position: relative;
  margin: 0;
  width: 100%;

  display: inline-flex;
  flex-wrap: nowrap;
  justify-content: center;

  gap: 8px;

  .icons-container {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
    gap: 8px;

    color: #646a73;

    &.icons-container__dark {
      color: #a6a6a6;
    }

    &.is-editing {
      gap: 6px;
    }

    .inner-icon {
      cursor: pointer;
    }
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
