<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>REST组件自动初始化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .test-description {
            margin-bottom: 15px;
            color: #666;
            line-height: 1.6;
        }
        .test-steps {
            background: white;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }
        .step {
            margin-bottom: 10px;
            padding: 8px 0;
        }
        .step-number {
            display: inline-block;
            width: 25px;
            height: 25px;
            background: #3498db;
            color: white;
            text-align: center;
            line-height: 25px;
            border-radius: 50%;
            margin-right: 10px;
            font-size: 12px;
        }
        .expected-result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #27ae60;
            margin-top: 15px;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .highlight {
            background: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 REST组件自动初始化功能测试指南</h1>
            <p>验证仪表板编辑和预览页面中REST组件的自动数据获取功能</p>
        </div>

        <div class="test-section">
            <div class="test-title">📋 测试场景1：仪表板编辑页面</div>
            <div class="test-description">
                测试在仪表板编辑页面中，配置了REST接口的组件是否能在页面加载时自动调用接口并显示数据。
            </div>
            <div class="test-steps">
                <div class="step">
                    <span class="step-number">1</span>
                    打开仪表板编辑页面 <span class="highlight">/dashboard?resourceId=xxx</span>
                </div>
                <div class="step">
                    <span class="step-number">2</span>
                    添加一个图表组件（如柱状图、折线图等）
                </div>
                <div class="step">
                    <span class="step-number">3</span>
                    在数据配置面板中，配置REST接口：
                    <div class="code-block">
URL: https://jsonplaceholder.typicode.com/posts
Method: GET
数据路径: (留空，直接使用根数组)
字段映射:
- id (number) -> id
- title (string) -> title  
- userId (number) -> userId</div>
                </div>
                <div class="step">
                    <span class="step-number">4</span>
                    保存REST配置并关闭配置对话框
                </div>
                <div class="step">
                    <span class="step-number">5</span>
                    刷新页面或重新打开仪表板
                </div>
            </div>
            <div class="expected-result">
                <strong>✅ 预期结果：</strong>
                <ul>
                    <li>页面加载完成后，图表组件应该自动显示从REST接口获取的数据</li>
                    <li>控制台应该显示类似日志：<code>"检测到REST组件，自动初始化数据"</code></li>
                    <li>控制台应该显示：<code>"组件 xxx REST数据自动初始化完成"</code></li>
                    <li>图表应该正常渲染，显示posts数据</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">👁️ 测试场景2：仪表板预览页面</div>
            <div class="test-description">
                测试在仪表板预览页面中，配置了REST接口的组件是否能在页面加载时自动调用接口并显示数据。
            </div>
            <div class="test-steps">
                <div class="step">
                    <span class="step-number">1</span>
                    使用测试场景1中已配置REST的仪表板
                </div>
                <div class="step">
                    <span class="step-number">2</span>
                    点击预览按钮或直接访问预览URL
                </div>
                <div class="step">
                    <span class="step-number">3</span>
                    观察页面加载过程和组件渲染情况
                </div>
            </div>
            <div class="expected-result">
                <strong>✅ 预期结果：</strong>
                <ul>
                    <li>预览页面加载完成后，REST组件应该自动显示数据</li>
                    <li>数据应该与编辑页面中显示的一致</li>
                    <li>控制台应该显示REST数据获取的相关日志</li>
                    <li>组件应该正常交互（如果支持的话）</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 测试场景3：多种组件类型</div>
            <div class="test-description">
                测试不同类型的组件（图表、表格、指标卡等）是否都支持REST自动初始化。
            </div>
            <div class="test-steps">
                <div class="step">
                    <span class="step-number">1</span>
                    在同一个仪表板中添加多种组件类型：
                    <ul style="margin-left: 40px; margin-top: 10px;">
                        <li>柱状图/折线图 (ChartComponentG2Plot)</li>
                        <li>表格组件 (ChartComponentS2)</li>
                        <li>指标卡组件 (DeIndicator)</li>
                    </ul>
                </div>
                <div class="step">
                    <span class="step-number">2</span>
                    为每个组件配置相同的REST接口
                </div>
                <div class="step">
                    <span class="step-number">3</span>
                    保存并刷新页面
                </div>
            </div>
            <div class="expected-result">
                <strong>✅ 预期结果：</strong>
                <ul>
                    <li>所有配置了REST的组件都应该自动获取并显示数据</li>
                    <li>不同组件类型应该根据其特性正确渲染数据</li>
                    <li>控制台应该显示每个组件的初始化日志</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">⚠️ 测试场景4：错误处理</div>
            <div class="test-description">
                测试当REST接口出现问题时，组件是否能正确处理错误情况。
            </div>
            <div class="test-steps">
                <div class="step">
                    <span class="step-number">1</span>
                    配置一个无效的REST URL（如：<code>https://invalid-url.com/api</code>）
                </div>
                <div class="step">
                    <span class="step-number">2</span>
                    保存配置并刷新页面
                </div>
                <div class="step">
                    <span class="step-number">3</span>
                    观察组件行为和控制台输出
                </div>
            </div>
            <div class="expected-result">
                <strong>✅ 预期结果：</strong>
                <ul>
                    <li>组件应该显示空状态或错误提示，而不是崩溃</li>
                    <li>控制台应该显示错误日志：<code>"组件 xxx REST自动初始化失败"</code></li>
                    <li>页面其他功能应该正常工作</li>
                    <li>如果可能，组件应该回退到正常的查询流程</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 关键检查点</div>
            <div class="test-description">
                在测试过程中需要特别关注的关键点：
            </div>
            <div class="test-steps">
                <div class="step">
                    <span class="step-number">✓</span>
                    <strong>控制台日志：</strong> 查看是否有 "检测到REST组件，自动初始化数据" 的日志
                </div>
                <div class="step">
                    <span class="step-number">✓</span>
                    <strong>数据获取：</strong> 确认REST API被正确调用（可在Network面板查看）
                </div>
                <div class="step">
                    <span class="step-number">✓</span>
                    <strong>数据渲染：</strong> 组件应该显示从REST接口获取的真实数据
                </div>
                <div class="step">
                    <span class="step-number">✓</span>
                    <strong>性能表现：</strong> 页面加载速度应该合理，不应该有明显延迟
                </div>
                <div class="step">
                    <span class="step-number">✓</span>
                    <strong>错误恢复：</strong> 当REST调用失败时，组件应该优雅降级
                </div>
            </div>
        </div>
    </div>
</body>
</html>
