# REST数据源自定义字段配置功能

## 功能概述

本次实现了REST接口数据集配置功能的重大改进，支持从REST API响应中自定义指标项和维度项，并能将这些字段关联到图表组件上。

## 主要功能

### 1. REST数据源配置
- **接口配置**：支持配置REST API的URL、请求方法、请求头、参数等
- **数据路径**：支持指定JSON路径来提取嵌套数据（如：data.items）
- **分页支持**：支持配置分页参数
- **超时控制**：可配置请求超时时间

### 2. 智能字段检测
- **自动检测**：测试连接后自动分析API响应，检测字段结构
- **类型推断**：根据数据值自动推断字段类型（字符串、数值、日期、布尔）
- **分组推断**：基于字段名和数据类型智能推断维度/指标分组
- **启发式规则**：使用启发式规则优化字段分类（如包含"count"的字段默认为指标）

### 3. 自定义字段配置
- **字段编辑器**：提供可视化的字段配置表格
- **字段属性**：支持修改字段名称、类型、分组、JSON路径、聚合方式等
- **启用控制**：可以启用/禁用特定字段
- **自定义名称**：支持为字段设置自定义显示名称

### 4. 数据预览功能
- **实时预览**：基于当前字段配置实时预览数据
- **数据验证**：验证字段映射是否正确
- **限制显示**：预览前10条数据，避免性能问题

### 5. 图表集成
- **无缝集成**：REST字段可直接拖拽到图表的维度和指标区域
- **数据转换**：自动将REST数据转换为图表可用的格式
- **实时更新**：字段配置变更后图表自动更新

## 技术实现

### 前端组件
1. **RestDataSource.vue** - 主要的REST数据源配置组件
2. **RestFieldEditor.vue** - 字段配置编辑器组件
3. **图表编辑器集成** - 在主编辑器中集成REST数据源支持

### 核心功能
1. **直接API调用** - 前端直接调用REST API，支持跨域
2. **数据转换** - 将REST响应转换为DataEase字段格式
3. **字段映射** - 支持复杂的JSON路径字段映射
4. **错误处理** - 完善的错误处理和用户提示

## 使用方法

### 1. 配置REST数据源
1. 在图表编辑器中选择"REST数据源"
2. 配置API接口信息（URL、方法、请求头等）
3. 点击"测试连接"验证接口可用性

### 2. 配置字段映射
1. 测试连接成功后，系统自动检测字段
2. 点击"使用检测字段"或"自定义字段"进行配置
3. 在字段编辑器中调整字段属性：
   - 修改字段名称和显示名称
   - 设置字段类型（字符串、数值、日期、布尔）
   - 选择分组类型（维度/指标）
   - 配置聚合方式（仅指标）
   - 设置JSON路径

### 3. 预览数据
1. 配置完字段后，点击"数据预览"
2. 查看字段映射效果和数据样例
3. 根据预览结果调整字段配置

### 4. 创建图表
1. 将配置好的字段拖拽到图表的维度/指标区域
2. 系统自动获取REST数据并渲染图表
3. 支持所有标准图表类型

## 示例配置

### API接口示例
```
URL: https://api.example.com/sales/data
方法: GET
请求头: 
  - Content-Type: application/json
  - Authorization: Bearer your-token
数据路径: data.items
```

### 字段配置示例
```
检测到的字段：
- id (数值) -> 维度
- product_name (字符串) -> 维度  
- sales_amount (数值) -> 指标 (求和)
- sale_date (日期) -> 维度
- category (字符串) -> 维度
```

## 注意事项

1. **跨域问题**：确保API服务器支持CORS或配置代理
2. **数据格式**：API应返回JSON格式数据
3. **性能考虑**：大数据量时建议在API端进行分页
4. **错误处理**：网络错误时会使用模拟数据进行演示

## 后续优化

1. **缓存机制**：添加数据缓存减少API调用
2. **批量操作**：支持批量编辑字段属性
3. **模板保存**：支持保存和复用字段配置模板
4. **高级映射**：支持更复杂的数据转换规则
5. **实时数据**：支持WebSocket等实时数据源

## 故障排除

### 常见问题
1. **测试连接失败**：检查URL是否正确，网络是否可达
2. **字段检测失败**：检查API响应格式，确认数据路径配置
3. **数据预览为空**：检查字段路径映射是否正确
4. **图表无数据**：确认字段已启用且配置正确

### 调试方法
1. 查看浏览器控制台错误信息
2. 使用"数据预览"功能验证字段映射
3. 检查API响应格式是否符合预期
