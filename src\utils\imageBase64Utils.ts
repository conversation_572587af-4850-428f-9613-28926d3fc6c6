/**
 * 图片Base64处理工具函数
 * 用于将图片文件转换为base64格式，避免上传到服务器
 */

/**
 * 将文件转换为base64格式
 * @param file 文件对象
 * @returns Promise<string> base64字符串
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      resolve(reader.result as string)
    }
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

/**
 * 压缩图片并转换为base64
 * @param file 文件对象
 * @param quality 压缩质量 0-1，默认0.8
 * @param maxWidth 最大宽度，默认1920
 * @param maxHeight 最大高度，默认1080
 * @returns Promise<string> 压缩后的base64字符串
 */
export function compressImageToBase64(
  file: File,
  quality: number = 0.8,
  maxWidth: number = 1920,
  maxHeight: number = 1080,
  backgroundColor: string = '#FFFFFF'
): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img

      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height)
        width = width * ratio
        height = height * ratio
      }

      canvas.width = width
      canvas.height = height

      if (ctx) {
        // 如果原图是PNG或有透明背景，先填充指定背景色
        if (file.type === 'image/png' || file.type === 'image/gif') {
          ctx.fillStyle = backgroundColor
          ctx.fillRect(0, 0, width, height)
        }

        // 绘制压缩后的图片
        ctx.drawImage(img, 0, 0, width, height)
      }

      // 根据原始文件类型选择输出格式
      let outputFormat = 'image/jpeg'
      let outputQuality = quality

      // 如果是PNG且需要保持透明度，使用PNG格式
      if (file.type === 'image/png') {
        // 检查是否有透明像素
        const imageData = ctx?.getImageData(0, 0, width, height)
        const hasTransparency = imageData?.data.some((_, index) =>
          index % 4 === 3 && imageData.data[index] < 255
        )

        if (hasTransparency) {
          outputFormat = 'image/png'
          outputQuality = undefined // PNG不使用质量参数
        }
      }

      // 转换为base64
      const base64 = canvas.toDataURL(outputFormat, outputQuality)
      resolve(base64)
    }

    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}

/**
 * 检查是否为base64图片数据
 * @param str 字符串
 * @returns boolean
 */
export function isBase64Image(str: string): boolean {
  if (!str || typeof str !== 'string') return false
  return str.startsWith('data:image/')
}

/**
 * 检测图片是否有透明背景
 * @param file 图片文件
 * @returns Promise<boolean>
 */
export function hasTransparentBackground(file: File): Promise<boolean> {
  return new Promise((resolve, reject) => {
    // 只有PNG和GIF可能有透明背景
    if (file.type !== 'image/png' && file.type !== 'image/gif') {
      resolve(false)
      return
    }

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      canvas.width = img.width
      canvas.height = img.height

      if (ctx) {
        ctx.drawImage(img, 0, 0)
        const imageData = ctx.getImageData(0, 0, img.width, img.height)

        // 检查是否有透明像素（alpha < 255）
        for (let i = 3; i < imageData.data.length; i += 4) {
          if (imageData.data[i] < 255) {
            resolve(true)
            return
          }
        }
      }

      resolve(false)
    }

    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}

/**
 * 获取base64图片的大小（字节）
 * @param base64 base64字符串
 * @returns number 大小（字节）
 */
export function getBase64Size(base64: string): number {
  if (!isBase64Image(base64)) return 0
  
  // 移除data:image/xxx;base64,前缀
  const base64Data = base64.split(',')[1]
  if (!base64Data) return 0
  
  // base64编码后的大小约为原始大小的4/3
  return Math.round((base64Data.length * 3) / 4)
}

/**
 * 格式化文件大小显示
 * @param bytes 字节数
 * @returns string 格式化后的大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 验证图片文件
 * @param file 文件对象
 * @param maxSize 最大文件大小（字节），默认15MB
 * @returns {valid: boolean, message?: string}
 */
export function validateImageFile(file: File, maxSize: number = 15 * 1024 * 1024) {
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    return { valid: false, message: '请上传图片文件' }
  }

  // 检查文件大小
  if (file.size > maxSize) {
    return { 
      valid: false, 
      message: `图片大小不能超过${formatFileSize(maxSize)}` 
    }
  }

  // 检查支持的格式
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml']
  if (!supportedTypes.includes(file.type)) {
    return { 
      valid: false, 
      message: '仅支持 JPG、PNG、GIF、SVG 格式的图片' 
    }
  }

  return { valid: true }
}

/**
 * 处理图片上传（转换为base64）
 * @param file 文件对象
 * @param options 选项
 * @returns Promise<{success: boolean, data?: string, message?: string}>
 */
export async function handleImageUpload(
  file: File,
  options: {
    compress?: boolean
    quality?: number
    maxWidth?: number
    maxHeight?: number
    maxSize?: number
    preserveTransparency?: boolean
    backgroundColor?: string
  } = {}
) {
  try {
    // 验证文件
    const validation = validateImageFile(file, options.maxSize)
    if (!validation.valid) {
      return { success: false, message: validation.message }
    }

    let base64: string

    if (options.compress !== false && file.type !== 'image/svg+xml') {
      // 检查是否需要保持透明度
      let shouldPreserveTransparency = options.preserveTransparency
      if (shouldPreserveTransparency === undefined) {
        // 自动检测是否有透明背景
        shouldPreserveTransparency = await hasTransparentBackground(file)
      }

      // 如果需要保持透明度且是PNG，直接转换不压缩
      if (shouldPreserveTransparency && file.type === 'image/png') {
        base64 = await fileToBase64(file)
      } else {
        // 压缩图片，使用指定背景色
        const bgColor = options.backgroundColor || '#FFFFFF'
        base64 = await compressImageToBase64(
          file,
          options.quality || 0.8,
          options.maxWidth || 1920,
          options.maxHeight || 1080,
          bgColor
        )
      }
    } else {
      // 直接转换为base64
      base64 = await fileToBase64(file)
    }

    return {
      success: true,
      data: base64,
      message: `图片处理成功，大小：${formatFileSize(getBase64Size(base64))}`
    }
  } catch (error) {
    console.error('图片处理失败:', error)
    return {
      success: false,
      message: '图片处理失败，请重试'
    }
  }
}

/**
 * 图片URL转换函数（兼容base64和传统URL）
 * @param url 图片URL或base64
 * @returns string 处理后的URL
 */
export function imageUrlTransform(url: string): string {
  if (!url) return ''
  
  // 如果是base64图片，直接返回
  if (isBase64Image(url)) {
    return url
  }
  
  // 如果是传统的static-resource路径，保持原有逻辑
  // 这里可以调用原有的imgUrlTrans函数
  return url
}
