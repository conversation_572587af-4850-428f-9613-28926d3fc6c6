# 表单查询组件配置指南

## 概述

表单查询组件需要与配置了REST数据源的表格组件配合使用。在使用表单查询功能之前，必须先正确配置目标表格组件的REST数据源和字段映射。

## 配置步骤

### 第一步：配置表格组件的REST数据源

1. **选择表格组件**
   - 在仪表板中添加一个表格组件（table-normal、table-info等）
   - 双击表格组件进入编辑模式

2. **配置REST接口**
   - 在左侧面板中找到"配置REST接口"按钮
   - 点击按钮打开REST配置对话框

3. **填写接口信息**
   ```
   URL: http://your-api-domain.com/api/users
   方法: GET
   请求头: 
     - Content-Type: application/json
     - Authorization: Bearer your-token (如果需要)
   查询参数: (暂时留空，表单查询会动态添加)
   超时时间: 30秒
   数据路径: data (根据API响应结构调整)
   ```

4. **测试连接**
   - 点击"测试连接"按钮
   - 确保API调用成功并返回数据

### 第二步：配置字段映射

1. **自动检测字段**
   - 测试连接成功后，系统会自动检测API返回的字段
   - 查看"检测到的字段"列表

2. **配置字段属性**
   - 点击"使用检测字段"或"自定义字段"
   - 在字段编辑器中配置每个字段：
     - **字段名称**: 显示在表格中的列名
     - **类型**: string(文本)、number(数字)、date(日期)、boolean(布尔)
     - **分组**: d(维度) 或 q(指标)
     - **JSON路径**: 字段在API响应中的路径，如 `name`、`user.email`
     - **启用状态**: 勾选需要显示的字段

3. **保存配置**
   - 配置完成后点击"保存"
   - 确认表格组件能正常显示数据

### 第三步：配置表单查询组件

1. **添加表单查询组件**
   - 从工具栏拖拽"表单查询"组件到仪表板

2. **配置基础信息**
   - 设置表单标题
   - 在"目标组件"中选择刚才配置的表格组件

3. **添加查询字段**
   - 点击"添加字段"按钮
   - 配置查询字段属性：
     - **字段类型**: 选择合适的输入控件
     - **字段标签**: 显示给用户的标签
     - **字段名称**: 用于数据收集
     - **参数映射**: 配置API参数名（重要！）

### 第四步：参数映射配置

这是最关键的一步，确保表单字段正确映射到API参数：

1. **普通字段映射**
   ```
   表单字段名: username
   参数映射: user_name
   实际API调用: ?user_name=输入值
   ```

2. **日期范围映射**
   ```
   表单字段名: dateRange
   开始参数名: start_date
   结束参数名: end_date
   实际API调用: ?start_date=2024-01-01&end_date=2024-01-31
   ```

## 常见问题解决

### 问题1: "REST字段配置不完整"错误

**原因**: 表格组件没有配置REST字段映射

**解决方案**:
1. 双击表格组件进入编辑模式
2. 点击"配置REST接口"按钮
3. 完成REST接口配置和字段映射
4. 保存配置后重试查询

### 问题2: 查询参数累积

**原因**: 之前版本的bug，现已修复

**解决方案**: 
- 确保使用最新版本的代码
- 每次查询会自动清除之前的查询参数

### 问题3: 查询后数据不更新

**原因**: 表格组件缓存了数据

**解决方案**:
- 现已修复，查询时会自动清除缓存
- 强制重新获取最新数据

### 问题4: 参数名不匹配

**原因**: 表单字段的参数映射与API期望的参数名不一致

**解决方案**:
1. 检查API文档，确认正确的参数名
2. 在表单查询组件的字段配置中，正确设置"参数映射"
3. 确保参数名与后端API完全一致

## 示例配置

### API接口示例
```json
GET /api/users?name=张三&status=active&page=1&size=10

Response:
{
  "code": 0,
  "data": {
    "total": 100,
    "records": [
      {
        "id": 1,
        "name": "张三",
        "email": "<EMAIL>",
        "status": "active",
        "createTime": "2024-01-15"
      }
    ]
  }
}
```

### REST配置示例
```
URL: /api/users
方法: GET
数据路径: data.records
```

### 字段映射示例
```
字段名: ID, 类型: number, 分组: 维度, 路径: id
字段名: 姓名, 类型: string, 分组: 维度, 路径: name
字段名: 邮箱, 类型: string, 分组: 维度, 路径: email
字段名: 状态, 类型: string, 分组: 维度, 路径: status
字段名: 创建时间, 类型: date, 分组: 维度, 路径: createTime
```

### 表单查询配置示例
```
字段1: 
  - 类型: 文本输入
  - 标签: 用户名
  - 字段名: username
  - 参数映射: name

字段2:
  - 类型: 下拉选择
  - 标签: 状态
  - 字段名: userStatus
  - 参数映射: status
  - 选项: [{"label": "启用", "value": "active"}, {"label": "禁用", "value": "inactive"}]
```

## 注意事项

1. **数据源类型**: 确保表格组件的数据源类型设置为"rest"
2. **字段映射完整性**: 所有需要显示的字段都必须配置映射
3. **参数名一致性**: 表单查询的参数映射必须与API期望的参数名完全一致
4. **数据路径正确性**: REST配置中的数据路径必须正确指向数据数组
5. **权限验证**: 确保API接口有正确的权限配置

## 调试技巧

1. **查看控制台日志**: 打开浏览器开发者工具，查看详细的错误信息
2. **检查网络请求**: 在Network标签中查看实际的API请求和响应
3. **验证API响应**: 确保API返回的数据格式符合预期
4. **测试单独调用**: 先在REST配置中测试API调用，确保基础功能正常

按照以上步骤正确配置后，表单查询功能就能正常工作了。
