<template>
  <div class="attr-list de-collapse-style">
    <el-collapse v-model="activeName" @change="onChange">
      <!-- 基础配置 -->
      <el-collapse-item title="基础配置" name="basicConfig">
        <el-form label-position="top" size="small">
          <el-form-item label="列表样式">
            <el-select
              v-model="propValue.listStyle"
              placeholder="请选择列表样式"
              @change="handleListStyleChange"
            >
              <el-option label="通知公告" value="notice" />
              <el-option label="时间轴" value="timeline" />
              <el-option label="任务列表" value="task" />
              <el-option label="预警列表" value="warning" />
            </el-select>
          </el-form-item>

          <el-form-item label="数据源">
            <el-select
              v-model="propValue.dataSource"
              placeholder="请选择数据源"
              @change="handleDataSourceChange"
            >
              <el-option label="静态数据" value="static" />
              <el-option label="REST接口" value="rest" />
            </el-select>
          </el-form-item>

          <el-form-item label="显示头部">
            <el-switch
              v-model="propValue.showHeader"
              @change="handleChange"
            />
          </el-form-item>

          <el-form-item v-if="propValue.showHeader" label="列表标题">
            <el-input
              v-model="propValue.title"
              placeholder="请输入列表标题"
              @input="handleChange"
            />
          </el-form-item>

          <el-form-item label="空数据文本">
            <el-input
              v-model="propValue.emptyText"
              placeholder="请输入空数据提示文本"
              @input="handleChange"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 显示配置 -->
      <el-collapse-item title="显示配置" name="displayConfig">
        <el-form label-position="top" size="small">
          <el-form-item label="显示图标">
            <el-switch
              v-model="propValue.showIcon"
              @change="handleChange"
            />
          </el-form-item>
          
          <el-form-item v-if="propValue.showIcon" label="图标大小">
            <el-input-number
              v-model="propValue.iconSize"
              :min="12"
              :max="64"
              @change="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="显示描述">
            <el-switch
              v-model="propValue.showDescription"
              @change="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="显示时间">
            <el-switch
              v-model="propValue.showTime"
              @change="handleChange"
            />
          </el-form-item>
          
          <el-form-item label="显示操作">
            <el-switch
              v-model="propValue.showAction"
              @change="handleChange"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 操作配置 -->
      <el-collapse-item v-if="propValue.showAction" title="操作配置" name="actionConfig">
        <el-form label-position="top" size="small">
          <el-form-item label="操作类型">
            <el-select
              v-model="propValue.actionType"
              @change="handleChange"
            >
              <el-option label="按钮" value="button" />
              <el-option label="图标" value="icon" />
              <el-option label="文本" value="text" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="操作文本">
            <el-input
              v-model="propValue.actionText"
              placeholder="请输入操作文本"
              @input="handleChange"
            />
          </el-form-item>
          
          <el-form-item v-if="propValue.actionType === 'button'" label="按钮类型">
            <el-select
              v-model="propValue.buttonType"
              @change="handleChange"
            >
              <el-option label="主要" value="primary" />
              <el-option label="成功" value="success" />
              <el-option label="警告" value="warning" />
              <el-option label="危险" value="danger" />
              <el-option label="信息" value="info" />
              <el-option label="默认" value="" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="propValue.actionType === 'button'" label="按钮大小">
            <el-select
              v-model="propValue.buttonSize"
              @change="handleChange"
            >
              <el-option label="大" value="large" />
              <el-option label="默认" value="default" />
              <el-option label="小" value="small" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="propValue.actionType === 'icon'" label="图标大小">
            <el-input-number
              v-model="propValue.actionIconSize"
              :min="12"
              :max="64"
              @change="handleChange"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- REST接口配置 -->
      <el-collapse-item v-if="propValue.dataSource === 'rest'" title="REST接口配置" name="restConfig">
        <el-form label-position="top" size="small">
          <el-form-item label="接口地址">
            <el-input
              v-model="restConfigLocal.url"
              placeholder="请输入REST接口地址"
              @input="handleRestConfigChange"
            />
          </el-form-item>

          <el-form-item label="请求方法">
            <el-select
              v-model="restConfigLocal.method"
              placeholder="请选择请求方法"
              @change="handleRestConfigChange"
            >
              <el-option label="GET" value="GET" />
              <el-option label="POST" value="POST" />
            </el-select>
          </el-form-item>

          <el-form-item label="请求头">
            <div class="config-list">
              <div
                v-for="(header, index) in restConfigLocal.headers"
                :key="index"
                class="config-item"
              >
                <el-input
                  v-model="header.key"
                  placeholder="Header名称"
                  @input="handleRestConfigChange"
                />
                <el-input
                  v-model="header.value"
                  placeholder="Header值"
                  @input="handleRestConfigChange"
                />
                <el-button type="danger" size="small" text @click="removeRestHeader(index)">删除</el-button>
              </div>
              <el-button type="primary" size="small" @click="addRestHeader">添加Header</el-button>
            </div>
          </el-form-item>

          <el-form-item label="请求参数">
            <div class="config-list">
              <div
                v-for="(param, index) in restConfigLocal.params"
                :key="index"
                class="config-item"
              >
                <el-input
                  v-model="param.key"
                  placeholder="参数名称"
                  @input="handleRestConfigChange"
                />
                <el-input
                  v-model="param.value"
                  placeholder="参数值"
                  @input="handleRestConfigChange"
                />
                <el-button type="danger" size="small" text @click="removeRestParam(index)">删除</el-button>
              </div>
              <el-button type="primary" size="small" @click="addRestParam">添加参数</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 字段映射配置 -->
      <el-collapse-item v-if="propValue.dataSource === 'rest'" title="字段映射配置" name="fieldMapping">
        <el-form label-position="top" size="small">
          <el-form-item label="标题字段">
            <el-input
              v-model="propValue.fieldMapping.title"
              placeholder="请输入标题字段名"
              @input="handleChange"
            />
          </el-form-item>

          <el-form-item label="描述字段">
            <el-input
              v-model="propValue.fieldMapping.description"
              placeholder="请输入描述字段名"
              @input="handleChange"
            />
          </el-form-item>

          <el-form-item label="时间字段">
            <el-input
              v-model="propValue.fieldMapping.time"
              placeholder="请输入时间字段名"
              @input="handleChange"
            />
          </el-form-item>

          <el-form-item v-if="propValue.listStyle === 'task'" label="状态字段">
            <el-input
              v-model="propValue.fieldMapping.status"
              placeholder="请输入状态字段名"
              @input="handleChange"
            />
          </el-form-item>

          <el-form-item v-if="['task', 'warning'].includes(propValue.listStyle)" label="优先级字段">
            <el-input
              v-model="propValue.fieldMapping.priority"
              placeholder="请输入优先级字段名"
              @input="handleChange"
            />
          </el-form-item>

          <el-form-item label="操作文本字段">
            <el-input
              v-model="propValue.fieldMapping.actionText"
              placeholder="请输入操作文本字段名"
              @input="handleChange"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 边框配置 -->
      <el-collapse-item title="边框配置" name="borderConfig">
        <el-form label-position="top" size="small">
          <!-- 容器边框 -->
          <el-form-item>
            <template #label>
              <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                <span>容器边框</span>
                <el-switch
                  v-model="propValue.borderConfig.showContainerBorder"
                  @change="handleChange"
                />
              </div>
            </template>
          </el-form-item>

          <template v-if="propValue.borderConfig.showContainerBorder">
            <el-form-item label="容器边框颜色">
              <el-color-picker
                v-model="propValue.borderConfig.containerBorderColor"
                @change="handleChange"
              />
            </el-form-item>

            <el-form-item label="容器边框宽度">
              <el-select
                v-model="propValue.borderConfig.containerBorderWidth"
                @change="handleChange"
              >
                <el-option label="1px" value="1px" />
                <el-option label="2px" value="2px" />
                <el-option label="3px" value="3px" />
                <el-option label="4px" value="4px" />
              </el-select>
            </el-form-item>

            <el-form-item label="容器边框样式">
              <el-select
                v-model="propValue.borderConfig.containerBorderStyle"
                @change="handleChange"
              >
                <el-option label="实线" value="solid" />
                <el-option label="虚线" value="dashed" />
                <el-option label="点线" value="dotted" />
              </el-select>
            </el-form-item>
          </template>

          <!-- 头部边框 -->
          <el-form-item>
            <template #label>
              <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                <span>头部边框</span>
                <el-switch
                  v-model="propValue.borderConfig.showHeaderBorder"
                  @change="handleChange"
                />
              </div>
            </template>
          </el-form-item>

          <template v-if="propValue.borderConfig.showHeaderBorder">
            <el-form-item label="头部边框颜色">
              <el-color-picker
                v-model="propValue.borderConfig.headerBorderColor"
                @change="handleChange"
              />
            </el-form-item>

            <el-form-item label="头部边框宽度">
              <el-select
                v-model="propValue.borderConfig.headerBorderWidth"
                @change="handleChange"
              >
                <el-option label="1px" value="1px" />
                <el-option label="2px" value="2px" />
                <el-option label="3px" value="3px" />
                <el-option label="4px" value="4px" />
              </el-select>
            </el-form-item>

            <el-form-item label="头部边框样式">
              <el-select
                v-model="propValue.borderConfig.headerBorderStyle"
                @change="handleChange"
              >
                <el-option label="实线" value="solid" />
                <el-option label="虚线" value="dashed" />
                <el-option label="点线" value="dotted" />
              </el-select>
            </el-form-item>
          </template>

          <!-- 列表项边框 -->
          <el-form-item>
            <template #label>
              <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                <span>列表项边框</span>
                <el-switch
                  v-model="propValue.borderConfig.showItemBorder"
                  @change="handleChange"
                />
              </div>
            </template>
          </el-form-item>

          <template v-if="propValue.borderConfig.showItemBorder">
            <el-form-item label="列表项边框颜色">
              <el-color-picker
                v-model="propValue.borderConfig.itemBorderColor"
                @change="handleChange"
              />
            </el-form-item>

            <el-form-item label="列表项边框宽度">
              <el-select
                v-model="propValue.borderConfig.itemBorderWidth"
                @change="handleChange"
              >
                <el-option label="1px" value="1px" />
                <el-option label="2px" value="2px" />
                <el-option label="3px" value="3px" />
                <el-option label="4px" value="4px" />
              </el-select>
            </el-form-item>

            <el-form-item label="列表项边框样式">
              <el-select
                v-model="propValue.borderConfig.itemBorderStyle"
                @change="handleChange"
              >
                <el-option label="实线" value="solid" />
                <el-option label="虚线" value="dashed" />
                <el-option label="点线" value="dotted" />
              </el-select>
            </el-form-item>
          </template>
        </el-form>
      </el-collapse-item>

      <!-- 静态数据配置 -->
      <el-collapse-item v-if="propValue.dataSource === 'static'" title="静态数据配置" name="dataConfig">
        <div class="data-config">
          <div class="config-header">
            <span>列表项配置</span>
            <el-button type="primary" size="small" @click="addItem">添加项</el-button>
          </div>
          
          <div v-if="propValue.items.length === 0" class="empty-data">
            暂无数据项，点击"添加项"按钮添加
          </div>
          
          <div v-else class="items-list">
            <div
              v-for="(item, index) in propValue.items"
              :key="index"
              class="item-config"
            >
              <div class="item-header">
                <span>项目 {{ index + 1 }}</span>
                <el-button type="danger" size="small" text @click="removeItem(index)">删除</el-button>
              </div>
              
              <el-form label-position="top" size="small">
                <el-form-item label="标题">
                  <el-input
                    v-model="item.title"
                    placeholder="请输入标题"
                    @input="handleChange"
                  />
                </el-form-item>
                
                <el-form-item v-if="propValue.showDescription" label="描述">
                  <el-input
                    v-model="item.description"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入描述"
                    @input="handleChange"
                  />
                </el-form-item>
                
                <el-form-item v-if="propValue.showTime" label="时间">
                  <el-input
                    v-model="item.time"
                    placeholder="请输入时间"
                    @input="handleChange"
                  />
                </el-form-item>
                
                <el-form-item v-if="propValue.showIcon" label="图片">
                  <div class="image-upload-container">
                    <div v-if="item.icon && item.icon.trim()" class="image-preview">
                      <img :src="item.icon" alt="预览" class="preview-image" />
                      <div class="image-actions">
                        <el-button type="danger" size="small" text @click="removeItemImage(index)">删除</el-button>
                      </div>
                    </div>
                    <div v-else class="upload-area">
                      <!-- 使用原生input作为备选方案 -->
                      <!-- <input
                        type="file"
                        accept="image/*"
                        @change="(event) => handleNativeImageChange(event, index)"
                        style="display: none"
                        :id="`file-input-${index}`"
                      />
                      <el-button
                        type="primary"
                        size="small"
                        @click="() => document.getElementById(`file-input-${index}`).click()"
                      >
                        上传图片
                      </el-button> -->

                      <!-- Element Plus Upload 作为备选 -->
                      <div style="margin-top: 8px;">
                        <el-upload
                          :show-file-list="false"
                          :on-change="(file) => handleImageChange(file, index)"
                          :before-upload="() => false"
                          accept="image/*"
                          :auto-upload="false"
                        >
                          <el-button type="primary" size="small">上传图片</el-button>
                        </el-upload>
                      </div>

                      <div class="upload-tip">支持 jpg、png、gif 格式</div>
                      <div class="debug-info" style="font-size: 12px; color: #999; margin-top: 4px;">
                        调试: 当前图片状态 - {{ item.icon ? '有图片' : '无图片' }}
                      </div>
                    </div>
                  </div>
                </el-form-item>

                <el-form-item v-if="propValue.showAction" label="操作文本">
                  <el-input
                    v-model="item.actionText"
                    placeholder="请输入操作文本"
                    @input="handleChange"
                  />
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
    
    <!-- 通用属性 -->
    <CommonAttr
      :themes="themes"
      :element="curComponent"
      :background-color-picker-width="197"
      :background-border-select-width="197"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, toRefs, computed, watch, nextTick } from 'vue'
import CommonAttr from '@/custom-component/common/CommonAttr.vue'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import { snapshotStoreWithOut } from '@/store/modules/data-visualization/snapshot'
import { storeToRefs } from 'pinia'

const props = withDefaults(
  defineProps<{
    themes?: EditorTheme
  }>(),
  {
    themes: 'dark'
  }
)

const { themes } = toRefs(props)
const dvMainStore = dvMainStoreWithOut()
const snapshotStore = snapshotStoreWithOut()
const { curComponent } = storeToRefs(dvMainStore)

const activeName = ref(['basicConfig'])

// 使用计算属性来获取响应式的propValue
const propValue = computed(() => {
  return curComponent.value?.propValue || {}
})

// REST配置的本地副本
const restConfigLocal = ref({
  url: '',
  method: 'GET',
  headers: [],
  params: []
})

// 初始化REST配置
const initRestConfig = () => {
  if (propValue.value.restConfig) {
    restConfigLocal.value = {
      url: propValue.value.restConfig.url || '',
      method: propValue.value.restConfig.method || 'GET',
      headers: propValue.value.restConfig.headers || [],
      params: propValue.value.restConfig.params || []
    }
  } else {
    restConfigLocal.value = {
      url: '',
      method: 'GET',
      headers: [],
      params: []
    }
  }
}

// 监听propValue变化，同步REST配置
watch(() => propValue.value.restConfig, () => {
  initRestConfig()
}, { deep: true, immediate: true })

// 初始化边框配置
const initBorderConfig = () => {
  if (!propValue.value.borderConfig) {
    propValue.value.borderConfig = {
      showContainerBorder: true,
      containerBorderColor: '#e8e8e8',
      containerBorderWidth: '1px',
      containerBorderStyle: 'solid',
      showItemBorder: true,
      itemBorderColor: '#f0f0f0',
      itemBorderWidth: '1px',
      itemBorderStyle: 'solid',
      showHeaderBorder: true,
      headerBorderColor: '#e8e8e8',
      headerBorderWidth: '1px',
      headerBorderStyle: 'solid'
    }
  }
}

// 监听propValue变化，初始化边框配置
watch(() => propValue.value, () => {
  initBorderConfig()
}, { deep: true, immediate: true })

const handleChange = () => {
  // 触发组件更新
  snapshotStore.recordSnapshotCache('propValue')
}

const onChange = () => {
  // 折叠面板状态变化
}

// 添加列表项
const addItem = () => {
  if (!propValue.value.items) {
    propValue.value.items = []
  }

  propValue.value.items.push({
    title: `新项目 ${propValue.value.items.length + 1}`,
    description: '这是一个新的列表项描述',
    time: new Date().toISOString().split('T')[0],
    icon: 'Bell',
    actionText: '查看'
  })

  handleChange()
}

// 删除列表项
const removeItem = (index: number) => {
  propValue.value.items.splice(index, 1)
  handleChange()
}

// 处理列表样式变化
const handleListStyleChange = () => {
  // 根据列表样式调整默认配置
  if (propValue.value.listStyle === 'timeline') {
    propValue.value.showIcon = false
    propValue.value.showAction = false
  } else if (propValue.value.listStyle === 'task') {
    propValue.value.showIcon = false
    propValue.value.actionText = '处理'
  } else if (propValue.value.listStyle === 'warning') {
    propValue.value.showIcon = false
    propValue.value.actionText = '查看'
    propValue.value.actionType = 'button'
  } else {
    // notice样式
    propValue.value.showIcon = true
    propValue.value.actionText = '查看'
    propValue.value.actionType = 'icon'
  }

  handleChange()
}

// 处理数据源变化
const handleDataSourceChange = () => {
  if (propValue.value.dataSource === 'rest') {
    // 初始化REST配置
    if (!propValue.value.restConfig) {
      propValue.value.restConfig = {
        url: '',
        method: 'GET',
        headers: [],
        params: []
      }
    }

    // 初始化字段映射
    if (!propValue.value.fieldMapping) {
      propValue.value.fieldMapping = {
        title: 'title',
        description: 'description',
        time: 'time',
        icon: 'icon',
        status: 'status',
        priority: 'priority',
        actionText: 'actionText'
      }
    }

    initRestConfig()
  }

  // 初始化边框配置
  if (!propValue.value.borderConfig) {
    propValue.value.borderConfig = {
      showContainerBorder: true,
      containerBorderColor: '#e8e8e8',
      containerBorderWidth: '1px',
      containerBorderStyle: 'solid',
      showItemBorder: true,
      itemBorderColor: '#f0f0f0',
      itemBorderWidth: '1px',
      itemBorderStyle: 'solid',
      showHeaderBorder: true,
      headerBorderColor: '#e8e8e8',
      headerBorderWidth: '1px',
      headerBorderStyle: 'solid'
    }
  }

  handleChange()
}

// 处理REST配置变化
const handleRestConfigChange = () => {
  propValue.value.restConfig = { ...restConfigLocal.value }
  handleChange()
}

// 添加REST请求头
const addRestHeader = () => {
  restConfigLocal.value.headers.push({ key: '', value: '' })
  handleRestConfigChange()
}

// 删除REST请求头
const removeRestHeader = (index: number) => {
  restConfigLocal.value.headers.splice(index, 1)
  handleRestConfigChange()
}

// 添加REST请求参数
const addRestParam = () => {
  restConfigLocal.value.params.push({ key: '', value: '' })
  handleRestConfigChange()
}

// 删除REST请求参数
const removeRestParam = (index: number) => {
  restConfigLocal.value.params.splice(index, 1)
  handleRestConfigChange()
}

// 图片上传相关方法
const handleImageChange = (uploadFile: any, itemIndex: number) => {
  console.log('开始处理图片上传:', { uploadFile, itemIndex })

  const file = uploadFile.raw || uploadFile

  if (!file) {
    console.error('未选择文件')
    return
  }

  console.log('文件信息:', { name: file.name, type: file.type, size: file.size })

  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    console.error('只能上传图片文件!', file.type)
    return
  }
  if (!isLt2M) {
    console.error('图片大小不能超过 2MB!', file.size / 1024 / 1024, 'MB')
    return
  }

  // 确保items数组存在
  if (!propValue.value.items) {
    propValue.value.items = []
    console.log('创建items数组')
  }

  // 确保指定索引的项存在
  if (!propValue.value.items[itemIndex]) {
    console.error('列表项不存在:', itemIndex, '当前items长度:', propValue.value.items.length)
    return
  }

  console.log('开始读取文件为base64...')

  // 将图片转换为base64
  const reader = new FileReader()
  reader.onload = async (e) => {
    if (e.target?.result) {
      const base64Data = e.target.result as string
      propValue.value.items[itemIndex].icon = base64Data

      // 使用nextTick确保DOM更新
      await nextTick()
      handleChange()

      console.log('图片上传成功!')
      console.log('项目索引:', itemIndex)
      console.log('图片数据长度:', base64Data.length)
      console.log('图片数据前缀:', base64Data.substring(0, 50))
      console.log('更新后的item:', propValue.value.items[itemIndex])
    }
  }
  reader.onerror = (error) => {
    console.error('图片读取失败:', error)
  }
  reader.readAsDataURL(file)
}

// 原生input处理图片上传
const handleNativeImageChange = (event: Event, itemIndex: number) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) {
    console.log('未选择文件')
    return
  }

  console.log('原生input文件选择:', { name: file.name, type: file.type, size: file.size })

  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    console.error('只能上传图片文件!', file.type)
    return
  }
  if (!isLt2M) {
    console.error('图片大小不能超过 2MB!', file.size / 1024 / 1024, 'MB')
    return
  }

  // 确保items数组存在
  if (!propValue.value.items) {
    propValue.value.items = []
  }

  // 确保指定索引的项存在
  if (!propValue.value.items[itemIndex]) {
    console.error('列表项不存在:', itemIndex)
    return
  }

  // 将图片转换为base64
  const reader = new FileReader()
  reader.onload = async (e) => {
    if (e.target?.result) {
      const base64Data = e.target.result as string
      propValue.value.items[itemIndex].icon = base64Data

      // 使用nextTick确保DOM更新
      await nextTick()
      handleChange()

      console.log('原生input图片上传成功!')
      console.log('项目索引:', itemIndex)
      console.log('图片数据长度:', base64Data.length)

      // 清空input值，允许重复选择同一文件
      target.value = ''
    }
  }
  reader.onerror = (error) => {
    console.error('图片读取失败:', error)
  }
  reader.readAsDataURL(file)
}

// 删除列表项图片
const removeItemImage = (itemIndex: number) => {
  if (propValue.value.items && propValue.value.items[itemIndex]) {
    propValue.value.items[itemIndex].icon = ''
    handleChange()
    console.log('图片删除成功，项目索引:', itemIndex)
  } else {
    console.error('无法删除图片，列表项不存在:', itemIndex)
  }
}
</script>

<style lang="less" scoped>
.attr-list {
  width: 100%;
  
  :deep(.el-collapse-item__header) {
    font-weight: bold;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
  
  :deep(.el-form-item__label) {
    padding-bottom: 4px;
    font-size: 12px;
  }
  
  :deep(.el-input__inner) {
    font-size: 12px;
  }
  
  :deep(.el-input-number) {
    width: 100%;
  }
}

.data-config {
  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-weight: bold;
  }
  
  .empty-data {
    text-align: center;
    color: #999;
    padding: 20px;
    font-size: 12px;
  }
  
  .items-list {
    .item-config {
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      padding: 12px;
      margin-bottom: 12px;

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        font-weight: bold;
        font-size: 12px;
      }
    }
  }
}

.config-list {
  .config-item {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 8px;

    .el-input {
      flex: 1;
    }
  }
}

.image-upload-container {
  .image-preview {
    position: relative;
    display: inline-block;

    .preview-image {
      width: 60px;
      height: 60px;
      object-fit: cover;
      border-radius: 4px;
      border: 1px solid #e8e8e8;
    }

    .image-actions {
      position: absolute;
      top: -8px;
      right: -8px;
    }
  }

  .upload-area {
    text-align: center;

    .upload-tip {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
    }
  }
}
</style>
