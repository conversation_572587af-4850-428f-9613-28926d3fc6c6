<template>
  <div class="attr-list">
    <el-collapse v-model="state.activeName" class="style-collapse">
      <!-- 基础设置 -->
      <el-collapse-item name="basic" title="基础设置">
        <el-form label-position="top" size="small">
          <el-form-item label="按钮文本">
            <el-input
              v-model="buttonText"
              placeholder="请输入按钮文本"
            />
          </el-form-item>
          
          <el-form-item label="按钮类型">
            <el-select v-model="buttonType">
              <el-option label="主要按钮" value="primary" />
              <el-option label="成功按钮" value="success" />
              <el-option label="信息按钮" value="info" />
              <el-option label="警告按钮" value="warning" />
              <el-option label="危险按钮" value="danger" />
              <el-option label="默认按钮" value="default" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="按钮尺寸">
            <el-select v-model="buttonSize">
              <el-option label="大" value="large" />
              <el-option label="默认" value="default" />
              <el-option label="小" value="small" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-checkbox v-model="buttonDisabled">
              禁用状态
            </el-checkbox>
          </el-form-item>

          <el-form-item>
            <el-checkbox v-model="buttonLoading">
              加载状态
            </el-checkbox>
          </el-form-item>

          <el-form-item>
            <el-checkbox v-model="buttonRound">
              圆角按钮
            </el-checkbox>
          </el-form-item>

          <el-form-item>
            <el-checkbox v-model="buttonPlain">
              朴素按钮
            </el-checkbox>
          </el-form-item>

          <el-form-item>
            <el-checkbox v-model="buttonFullWidth">
              全宽度
            </el-checkbox>
          </el-form-item>
        </el-form>
      </el-collapse-item>
      
      <!-- 样式设置 -->
      <el-collapse-item name="style" title="样式设置">
        <el-form label-position="top" size="small">
          <el-form-item>
            <el-checkbox v-model="customColor">
              自定义颜色
            </el-checkbox>
          </el-form-item>

          <template v-if="customColor">
            <el-form-item label="文字颜色">
              <el-color-picker v-model="textColor" />
            </el-form-item>

            <el-form-item label="背景颜色">
              <el-color-picker v-model="backgroundColor" />
            </el-form-item>

            <el-form-item label="边框颜色">
              <el-color-picker v-model="borderColor" />
            </el-form-item>
          </template>
          
          <el-form-item label="字体大小">
            <el-input-number
              v-model="fontSize"
              :min="12"
              :max="48"
            />
          </el-form-item>

          <el-form-item label="字体粗细">
            <el-select v-model="fontWeight">
              <el-option label="正常" value="normal" />
              <el-option label="粗体" value="bold" />
              <el-option label="细体" value="lighter" />
            </el-select>
          </el-form-item>

          <el-form-item label="水平对齐">
            <el-select v-model="horizontalAlign">
              <el-option label="左对齐" value="flex-start" />
              <el-option label="居中" value="center" />
              <el-option label="右对齐" value="flex-end" />
            </el-select>
          </el-form-item>

          <el-form-item label="垂直对齐">
            <el-select v-model="verticalAlign">
              <el-option label="顶部" value="flex-start" />
              <el-option label="居中" value="center" />
              <el-option label="底部" value="flex-end" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-collapse-item>
      
      <!-- 交互设置 -->
      <el-collapse-item name="interaction" title="交互设置">
        <el-form label-position="top" size="small">
          <el-form-item>
            <el-checkbox v-model="enableJump">
              启用跳转
            </el-checkbox>
          </el-form-item>

          <template v-if="enableJump">
            <el-form-item label="跳转链接">
              <el-input
                v-model="jumpUrl"
                placeholder="请输入跳转链接"
              />
            </el-form-item>

            <el-form-item label="打开方式">
              <el-select v-model="jumpTarget">
                <el-option label="当前窗口" value="_self" />
                <el-option label="新窗口" value="_blank" />
              </el-select>
            </el-form-item>
          </template>

          <el-form-item>
            <el-checkbox v-model="enableModal">
              打开弹框
            </el-checkbox>
          </el-form-item>

          <template v-if="enableModal">
            <el-form-item label="目标弹框">
              <el-select
                v-model="targetModalId"
                placeholder="请选择要打开的弹框组件"
                clearable
              >
                <el-option
                  v-for="modal in availableModals"
                  :key="modal.id"
                  :label="modal.label"
                  :value="modal.id"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-form>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, toRefs } from 'vue'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'
import { snapshotStoreWithOut } from '@/store/modules/data-visualization/snapshot'
import { storeToRefs } from 'pinia'

const props = defineProps({
  themes: {
    type: String,
    default: 'dark'
  }
})

const { themes } = toRefs(props)
const dvMainStore = dvMainStoreWithOut()
const snapshotStore = snapshotStoreWithOut()
const { curComponent, componentData } = storeToRefs(dvMainStore)

const state = reactive({
  activeName: ['basic', 'style', 'interaction']
})

// 创建单独的computed属性用于双向绑定
const createPropComputed = (propName: string, defaultValue: any) => {
  return computed({
    get: () => {
      return curComponent.value?.propValue?.[propName] ?? defaultValue
    },
    set: (value) => {
      if (curComponent.value) {
        if (!curComponent.value.propValue) {
          curComponent.value.propValue = {}
        }
        curComponent.value.propValue[propName] = value
        updateConfig()
      }
    }
  })
}

// 按钮基础配置
const buttonText = createPropComputed('text', '按钮')
const buttonType = createPropComputed('type', 'primary')
const buttonSize = createPropComputed('size', 'default')
const buttonDisabled = createPropComputed('disabled', false)
const buttonLoading = createPropComputed('loading', false)
const buttonRound = createPropComputed('round', false)
const buttonPlain = createPropComputed('plain', false)
const buttonFullWidth = createPropComputed('fullWidth', false)

// 样式配置
const customColor = createPropComputed('customColor', false)
const textColor = createPropComputed('textColor', '#ffffff')
const backgroundColor = createPropComputed('backgroundColor', '#409eff')
const borderColor = createPropComputed('borderColor', '#409eff')

// 交互配置
const enableJump = createPropComputed('enableJump', false)
const jumpUrl = createPropComputed('jumpUrl', '')
const jumpTarget = createPropComputed('jumpTarget', '_self')
const enableModal = createPropComputed('enableModal', false)
const targetModalId = createPropComputed('targetModalId', '')

// 创建样式computed属性
const createStyleComputed = (styleName: string, defaultValue: any) => {
  return computed({
    get: () => {
      return curComponent.value?.style?.[styleName] ?? defaultValue
    },
    set: (value) => {
      if (curComponent.value) {
        if (!curComponent.value.style) {
          curComponent.value.style = {}
        }
        curComponent.value.style[styleName] = value
        updateStyle()
      }
    }
  })
}

// 样式配置
const fontSize = createStyleComputed('fontSize', 14)
const fontWeight = createStyleComputed('fontWeight', 'normal')
const horizontalAlign = createStyleComputed('horizontalAlign', 'center')
const verticalAlign = createStyleComputed('verticalAlign', 'center')

// 获取可用的弹框组件列表
const availableModals = computed(() => {
  return componentData.value
    .filter((comp: any) => comp.component === 'ModalDialog')
    .map((comp: any) => ({
      id: comp.id,
      label: comp.propValue?.title || `弹框组件 ${comp.id.slice(-6)}`
    }))
})

// 更新配置
const updateConfig = () => {
  snapshotStore.recordSnapshotCache('propValue')
}

// 更新样式
const updateStyle = () => {
  snapshotStore.recordSnapshotCache('style')
}
</script>

<style lang="less" scoped>
.attr-list {
  padding: 8px;
}

.style-collapse {
  border: none;
  
  :deep(.el-collapse-item__header) {
    background-color: transparent;
    border: none;
    padding: 0 8px;
    font-weight: bold;
  }
  
  :deep(.el-collapse-item__content) {
    padding: 8px;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
  
  :deep(.el-form-item__label) {
    font-size: 12px;
    color: #606266;
  }
}
</style>
