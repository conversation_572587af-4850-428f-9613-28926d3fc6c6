<template>
  <div class="image-base64-example">
    <h2>图片Base64处理示例</h2>
    
    <!-- 基础上传示例 -->
    <div class="example-section">
      <h3>1. 基础图片上传（Base64模式）</h3>
      <DeUploadBase64
        :img-url="basicImageUrl"
        :themes="'dark'"
        @onImgChange="handleBasicImageChange"
      />
      <div v-if="basicImageUrl" class="image-info">
        <p>图片类型: {{ getImageType(basicImageUrl) }}</p>
        <p>图片大小: {{ getImageSize(basicImageUrl) }}</p>
      </div>
    </div>

    <!-- 高级配置示例 -->
    <div class="example-section">
      <h3>2. 高级配置示例</h3>
      <DeUploadBase64
        :img-url="advancedImageUrl"
        :themes="'light'"
        :compress="true"
        :quality="0.6"
        :max-width="800"
        :max-height="600"
        @onImgChange="handleAdvancedImageChange"
      />
      <div class="config-info">
        <p>压缩质量: 0.6</p>
        <p>最大尺寸: 800x600</p>
      </div>
    </div>

    <!-- 统一服务示例 -->
    <div class="example-section">
      <h3>3. 使用统一图片服务</h3>
      <input
        type="file"
        accept="image/*"
        @change="handleFileSelect"
        ref="fileInput"
      />
      <button @click="uploadWithService" :disabled="!selectedFile">
        使用图片服务上传
      </button>
      <div v-if="serviceResult" class="service-result">
        <p>处理模式: {{ serviceResult.mode }}</p>
        <p>处理结果: {{ serviceResult.success ? '成功' : '失败' }}</p>
        <p v-if="serviceResult.message">消息: {{ serviceResult.message }}</p>
        <img v-if="serviceResult.success && serviceResult.data" 
             :src="serviceResult.data" 
             style="max-width: 200px; max-height: 200px;" />
      </div>
    </div>

    <!-- 批量上传示例 -->
    <div class="example-section">
      <h3>4. 批量图片上传</h3>
      <input
        type="file"
        accept="image/*"
        multiple
        @change="handleMultipleFileSelect"
        ref="multipleFileInput"
      />
      <button @click="uploadMultiple" :disabled="!selectedFiles.length">
        批量上传 ({{ selectedFiles.length }} 个文件)
      </button>
      <div v-if="uploadProgress.total > 0" class="progress">
        <p>上传进度: {{ uploadProgress.completed }}/{{ uploadProgress.total }}</p>
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: (uploadProgress.completed / uploadProgress.total * 100) + '%' }"
          ></div>
        </div>
      </div>
      <div v-if="batchResults.length" class="batch-results">
        <h4>批量上传结果:</h4>
        <div v-for="(result, index) in batchResults" :key="index" class="batch-item">
          <span>文件 {{ index + 1 }}: {{ result.success ? '成功' : '失败' }}</span>
          <img v-if="result.success && result.data" 
               :src="result.data" 
               style="width: 50px; height: 50px; margin-left: 10px;" />
        </div>
      </div>
    </div>

    <!-- 配置信息显示 -->
    <div class="example-section">
      <h3>5. 当前配置信息</h3>
      <div class="config-display">
        <p>Base64模式: {{ currentConfig.useBase64 ? '启用' : '禁用' }}</p>
        <p>压缩启用: {{ currentConfig.compression.enabled ? '是' : '否' }}</p>
        <p>压缩质量: {{ currentConfig.compression.quality }}</p>
        <p>最大尺寸: {{ currentConfig.compression.maxWidth }}x{{ currentConfig.compression.maxHeight }}</p>
        <p>最大文件大小: {{ formatFileSize(currentConfig.maxFileSize) }}</p>
        <p>支持格式: {{ currentConfig.supportedTypes.join(', ') }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import DeUploadBase64 from '@/components/visualization/common/DeUploadBase64.vue'
import { uploadImage, uploadImages } from '@/services/imageService'
import { getImageConfig } from '@/config/imageConfig'
import { getBase64Size, formatFileSize, isBase64Image } from '@/utils/imageBase64Utils'

// 基础示例
const basicImageUrl = ref('')

// 高级示例
const advancedImageUrl = ref('')

// 统一服务示例
const selectedFile = ref<File | null>(null)
const serviceResult = ref(null)

// 批量上传示例
const selectedFiles = ref<File[]>([])
const uploadProgress = reactive({
  completed: 0,
  total: 0
})
const batchResults = ref([])

// 配置信息
const currentConfig = ref(getImageConfig())

// 基础图片上传处理
const handleBasicImageChange = (base64Data: string) => {
  basicImageUrl.value = base64Data
  console.log('基础图片上传:', base64Data ? '成功' : '清除')
}

// 高级图片上传处理
const handleAdvancedImageChange = (base64Data: string) => {
  advancedImageUrl.value = base64Data
  console.log('高级图片上传:', base64Data ? '成功' : '清除')
}

// 文件选择处理
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  selectedFile.value = target.files?.[0] || null
}

// 使用统一服务上传
const uploadWithService = async () => {
  if (!selectedFile.value) return

  try {
    const result = await uploadImage(selectedFile.value, {
      compress: true,
      quality: 0.7,
      maxWidth: 1200,
      maxHeight: 800
    })
    
    serviceResult.value = result
    console.log('统一服务上传结果:', result)
  } catch (error) {
    console.error('统一服务上传失败:', error)
  }
}

// 多文件选择处理
const handleMultipleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  selectedFiles.value = Array.from(target.files || [])
}

// 批量上传
const uploadMultiple = async () => {
  if (!selectedFiles.value.length) return

  uploadProgress.completed = 0
  uploadProgress.total = selectedFiles.value.length
  batchResults.value = []

  try {
    const results = await uploadImages(selectedFiles.value, {
      compress: true,
      quality: 0.8,
      onProgress: (completed, total) => {
        uploadProgress.completed = completed
        uploadProgress.total = total
      }
    })
    
    batchResults.value = results
    console.log('批量上传结果:', results)
  } catch (error) {
    console.error('批量上传失败:', error)
  }
}

// 获取图片类型
const getImageType = (url: string): string => {
  if (!url) return '无'
  
  if (isBase64Image(url)) {
    const match = url.match(/data:image\/([^;]+)/)
    return match ? match[1].toUpperCase() : 'Base64'
  }
  
  return '传统URL'
}

// 获取图片大小
const getImageSize = (url: string): string => {
  if (!url) return '0 B'
  
  if (isBase64Image(url)) {
    return formatFileSize(getBase64Size(url))
  }
  
  return '未知'
}

onMounted(() => {
  console.log('图片Base64示例组件已加载')
  console.log('当前配置:', currentConfig.value)
})
</script>

<style scoped lang="less">
.image-base64-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  .example-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;

    h3 {
      margin-top: 0;
      color: #333;
    }

    .image-info,
    .config-info,
    .service-result,
    .config-display {
      margin-top: 15px;
      padding: 10px;
      background-color: #f5f5f5;
      border-radius: 4px;

      p {
        margin: 5px 0;
        font-size: 14px;
      }
    }

    .progress {
      margin-top: 15px;

      .progress-bar {
        width: 100%;
        height: 20px;
        background-color: #e0e0e0;
        border-radius: 10px;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          background-color: #4caf50;
          transition: width 0.3s ease;
        }
      }
    }

    .batch-results {
      margin-top: 15px;

      .batch-item {
        display: flex;
        align-items: center;
        margin: 10px 0;
        padding: 10px;
        background-color: #f9f9f9;
        border-radius: 4px;

        img {
          border-radius: 4px;
          object-fit: cover;
        }
      }
    }

    button {
      margin: 10px 5px 10px 0;
      padding: 8px 16px;
      background-color: #1976d2;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;

      &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }

      &:hover:not(:disabled) {
        background-color: #1565c0;
      }
    }

    input[type="file"] {
      margin: 10px 0;
    }
  }
}
</style>
