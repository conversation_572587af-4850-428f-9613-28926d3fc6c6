<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus-secondary'
import { useI18n } from '@/hooks/web/useI18n'
import { Plus, Delete, ArrowDown } from '@element-plus/icons-vue'
import RestFieldEditor from './RestFieldEditor.vue'
import { debounce } from 'lodash-es'
import { callRestApi, type RestConfig as RestApiConfig } from '@/utils/restApi'
import { dvMainStoreWithOut } from '@/store/modules/data-visualization/dvMain'

const { t } = useI18n()
const dvMainStore = dvMainStoreWithOut()

interface RestConfig {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers: Array<{ key: string; value: string }>
  params: Array<{ key: string; value: string }>
  body: string
  timeout: number
  dataPath: string // JSON路径，用于提取数据
  pagination: {
    enabled: boolean
    pageParam: string
    sizeParam: string
    pageSize: number
  }
}

interface RestField {
  id: string // 唯一标识
  name: string // 显示名称
  originalName: string // 原始字段名
  type: 'string' | 'number' | 'date' | 'boolean'
  groupType: 'd' | 'q' // d: 维度, q: 指标
  path: string // JSON路径
  deType: number // DataEase字段类型
  enabled: boolean // 是否启用
  summary?: string // 聚合方式（仅指标）
  sort?: 'asc' | 'desc' | 'none' // 排序方式
  customName?: string // 自定义显示名称
}

const props = defineProps<{
  modelValue?: RestConfig
  themes?: string
}>()

const emits = defineEmits<{
  'update:modelValue': [value: RestConfig]
  onConfigChange: [config: RestConfig]
  onFieldsChange: [fields: RestField[]]
}>()

// 清除测试结果和检测字段等状态
const clearTestStates = () => {
  detectedFields.value = []
  customFields.value = []
  testResult.value = ''
  loading.value = false
  showDataPreview.value = false
  previewData.value = []
  previewLoading.value = false
  showFieldEditor.value = false
  console.log('已清除REST配置对话框的测试状态和字段配置')
}

const restConfig = reactive<RestConfig>({
  url: '',
  method: 'GET',
  headers: [{ key: 'Content-Type', value: 'application/json' }],
  params: [],
  body: '',
  timeout: 30,
  dataPath: '',
  pagination: {
    enabled: false,
    pageParam: 'page',
    sizeParam: 'size',
    pageSize: 100
  }
})

const detectedFields = ref<RestField[]>([])
const customFields = ref<RestField[]>([])
const loading = ref(false)
const testResult = ref('')
const fieldEditor = ref()
const showFieldEditor = ref(false)
const showDataPreview = ref(false)
const previewData = ref([])
const previewLoading = ref(false)

// 使用ref来存储上次的配置，避免循环更新
const lastExternalConfig = ref(null)

// 全局变量相关
const globalVariables = computed(() => dvMainStore.globalVariables)
const showVariableSelector = ref(false)
const currentInputType = ref('') // 'header' | 'param'
const currentInputIndex = ref(-1)

// 监听外部传入的配置变化，同步到内部状态
watch(
  () => props.modelValue,
  newConfig => {
    // 如果新配置与上次外部配置相同，跳过（避免循环）
    if (
      lastExternalConfig.value &&
      JSON.stringify(newConfig) === JSON.stringify(lastExternalConfig.value)
    ) {
      return
    }

    console.log('REST配置组件接收到新配置:', newConfig)
    lastExternalConfig.value = newConfig ? JSON.parse(JSON.stringify(newConfig)) : null

    if (newConfig && typeof newConfig === 'object') {
      // 深拷贝新配置，避免引用问题
      const configCopy = JSON.parse(JSON.stringify(newConfig))
      Object.assign(restConfig, configCopy)
      console.log('REST配置已更新:', restConfig)
    } else {
      // 如果传入空配置，重置为默认值
      const defaultConfig = {
        url: '',
        method: 'GET',
        headers: [{ key: 'Content-Type', value: 'application/json' }],
        params: [],
        body: '',
        timeout: 30,
        dataPath: '',
        pagination: {
          enabled: false,
          pageParam: 'page',
          sizeParam: 'size',
          pageSize: 100
        }
      }
      Object.assign(restConfig, defaultConfig)
      console.log('REST配置已重置为默认值')
    }
  },
  { immediate: true }
)

// 使用防抖来监听配置变化，避免频繁触发
const debouncedConfigChange = debounce(newConfig => {
  // 更新外部配置记录
  lastExternalConfig.value = JSON.parse(JSON.stringify(newConfig))
  emits('update:modelValue', newConfig)
  emits('onConfigChange', newConfig)
}, 100)

// 监听配置变化
watch(
  restConfig,
  newConfig => {
    debouncedConfigChange(newConfig)
  },
  { deep: true }
)

// 监听自定义字段变化 - 使用防抖避免循环更新
const debouncedFieldsChange = debounce(newFields => {
  emits('onFieldsChange', newFields)
}, 300)

watch(
  customFields,
  newFields => {
    debouncedFieldsChange(newFields)
  },
  { deep: true }
)

// 添加请求头
const addHeader = () => {
  restConfig.headers.push({ key: '', value: '' })
}

// 删除请求头
const removeHeader = (index: number) => {
  restConfig.headers.splice(index, 1)
}

// 添加参数
const addParam = () => {
  restConfig.params.push({ key: '', value: '' })
}

// 删除参数
const removeParam = (index: number) => {
  restConfig.params.splice(index, 1)
}

// 测试REST接口
const testConnection = async () => {
  if (!restConfig.url) {
    ElMessage.warning('请输入REST接口URL')
    return
  }

  loading.value = true
  try {
    let response
    try {
      // 使用统一的REST API工具
      response = await callRestApi(restConfig as RestApiConfig, {
        enableMockData: true,
        logPrefix: 'REST配置测试'
      })
    } catch (apiError) {
      // 如果API调用失败，显示错误信息
      console.error('REST API调用失败:', apiError)
      ElMessage.error('连接测试失败: ' + apiError.message)
      return
    }

    testResult.value = JSON.stringify(response, null, 2)

    // 自动检测字段
    if (response) {
      detectedFields.value = detectFields(response, restConfig.dataPath)
      // 如果没有自定义字段，使用检测到的字段
      if (customFields.value.length === 0) {
        // 使用 nextTick 避免循环更新
        nextTick(() => {
          customFields.value = [...detectedFields.value]
        })
      }
    }

    ElMessage.success('连接测试成功')
  } catch (error) {
    ElMessage.error('连接测试失败: ' + error.message)
    testResult.value = error.message
  } finally {
    loading.value = false
  }
}

// 检测字段结构
const detectFields = (data: any, dataPath: string): RestField[] => {
  let targetData = data

  // 如果指定了数据路径，提取对应数据
  if (dataPath) {
    const paths = dataPath.split('.')
    for (const path of paths) {
      if (targetData && typeof targetData === 'object') {
        targetData = targetData[path]
      }
    }
  }

  // 如果是数组，取第一个元素分析结构
  if (Array.isArray(targetData) && targetData.length > 0) {
    targetData = targetData[0]
  }

  const fields: RestField[] = []

  if (targetData && typeof targetData === 'object') {
    Object.keys(targetData).forEach(key => {
      const value = targetData[key]
      let type: 'string' | 'number' | 'date' | 'boolean' = 'string'
      let groupType: 'd' | 'q' = 'd'

      // 根据值类型推断字段类型
      if (typeof value === 'number') {
        type = 'number'
        groupType = 'q'
      } else if (typeof value === 'boolean') {
        type = 'boolean'
        groupType = 'd' // 布尔值通常作为维度
      } else if (typeof value === 'string') {
        // 尝试判断是否为日期
        if (isDateString(value)) {
          type = 'date'
          groupType = 'd' // 日期通常作为维度
        } else {
          // 检查是否为数字字符串，但要排除一些明显应该保持为字符串的情况
          if (!isNaN(Number(value)) && value.trim() !== '' && shouldTreatAsNumber(value)) {
            type = 'number'
            groupType = 'q'
          }
        }
      }

      // 检查是否已有用户配置的字段类型
      const existingField = customFields.value.find(f => f.name === key || f.originalName === key)
      if (existingField && existingField.type) {
        // 如果用户已经配置了字段类型，保持用户配置
        type = existingField.type
        groupType = existingField.groupType || 'd'
        console.log(`保持用户配置的字段类型: ${key} -> ${type}`)
      } else {
        // 根据字段名推断类型（启发式规则）
        const lowerKey = key.toLowerCase()
        if (
          lowerKey.includes('id') ||
          lowerKey.includes('name') ||
          lowerKey.includes('title') ||
          lowerKey.includes('category') ||
          lowerKey.includes('type') ||
          lowerKey.includes('status')
        ) {
          // 这些字段通常是维度，且应该保持为字符串类型
          groupType = 'd'
          if (lowerKey.includes('name') || lowerKey.includes('title') || lowerKey.includes('category')) {
            type = 'string' // 强制设置为字符串类型
          }
        } else if (
          lowerKey.includes('count') ||
          lowerKey.includes('amount') ||
          lowerKey.includes('value') ||
          lowerKey.includes('price') ||
          lowerKey.includes('total') ||
          lowerKey.includes('sum')
        ) {
          groupType = 'q' // 这些通常是指标
        }
      }

      fields.push({
        id: generateFieldId(),
        name: key,
        originalName: key,
        type,
        groupType,
        path: key, // 统一默认为字段名称，不考虑数据路径
        deType: getDeTypeFromRestType(type),
        enabled: true,
        sort: 'none',
        summary: groupType === 'q' ? 'sum' : undefined
      })
    })
  }

  return fields
}

// 判断字符串是否为日期格式
const isDateString = (str: string): boolean => {
  const date = new Date(str)
  return !isNaN(date.getTime()) && str.length > 8
}

// 判断字符串是否应该被当作数字处理
const shouldTreatAsNumber = (str: string): boolean => {
  // 如果包含前导零（但不是单独的"0"），应该保持为字符串
  if (str.length > 1 && str.startsWith('0') && !str.startsWith('0.')) {
    return false
  }

  // 如果包含非数字字符（除了小数点、负号、科学计数法），应该保持为字符串
  if (!/^-?(\d+\.?\d*|\.\d+)([eE][+-]?\d+)?$/.test(str)) {
    return false
  }

  // 如果字符串很长（超过15位），可能是ID或编码，应该保持为字符串
  if (str.length > 15) {
    return false
  }

  return true
}

// 格式化JSON
const formatJson = () => {
  try {
    if (restConfig.body) {
      restConfig.body = JSON.stringify(JSON.parse(restConfig.body), null, 2)
    }
  } catch (error) {
    ElMessage.warning('JSON格式不正确')
  }
}

// 生成字段ID
const generateFieldId = () => {
  return 'field_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
}

// 将REST字段类型转换为DataEase字段类型
const getDeTypeFromRestType = (restType: string): number => {
  switch (restType) {
    case 'string':
      return 0 // 文本
    case 'date':
      return 1 // 时间
    case 'number':
      return 2 // 整型数值
    case 'boolean':
      return 4 // 布尔
    default:
      return 0
  }
}

// 使用检测到的字段初始化自定义字段
const useDetectedFields = () => {
  nextTick(() => {
    customFields.value = [...detectedFields.value]
    showFieldEditor.value = true
  })
}

// 自定义字段配置
const configureFields = () => {
  showFieldEditor.value = true
}

// 字段配置变化处理
const onCustomFieldsChange = (fields: RestField[]) => {
  // 避免循环更新，只在字段真正变化时更新
  if (JSON.stringify(customFields.value) !== JSON.stringify(fields)) {
    customFields.value = fields
  }
}

// 预览数据
const previewRestData = async () => {
  if (!restConfig.url) {
    ElMessage.warning('请先配置REST接口URL')
    return
  }

  if (customFields.value.length === 0) {
    ElMessage.warning('请先配置字段映射')
    return
  }

  previewLoading.value = true
  try {
    // 获取REST数据
    const response = await callRestApi(restConfig as RestApiConfig, {
      enableMockData: true,
      logPrefix: 'REST数据预览'
    })

    // 转换数据为预览格式
    const transformedData = transformDataForPreview(response, customFields.value)
    previewData.value = transformedData
    showDataPreview.value = true

    ElMessage.success('数据预览成功')
  } catch (error) {
    ElMessage.error('数据预览失败: ' + error.message)
  } finally {
    previewLoading.value = false
  }
}

// 转换数据为预览格式
const transformDataForPreview = (data: any, fields: RestField[]) => {
  let targetData = data

  // 如果指定了数据路径，提取对应数据
  if (restConfig.dataPath) {
    const paths = restConfig.dataPath.split('.')
    for (const path of paths) {
      if (targetData && typeof targetData === 'object') {
        targetData = targetData[path]
      }
    }
  }

  // 确保数据是数组
  if (!Array.isArray(targetData)) {
    if (targetData && typeof targetData === 'object') {
      targetData = [targetData]
    } else {
      return []
    }
  }

  // 只取前10条数据用于预览
  const previewRows = targetData.slice(0, 10)

  // 根据字段配置转换数据
  return previewRows.map((item: any) => {
    const row: Record<string, any> = {}
    fields
      .filter(f => f.enabled)
      .forEach(field => {
        const value = getValueByPath(item, field.path)
        row[field.customName || field.name] = value
      })
    return row
  })
}

// 根据路径获取值
const getValueByPath = (obj: any, path: string) => {
  if (!path || !obj) return null

  const paths = path.split('.')
  let value = obj

  for (const p of paths) {
    if (value && typeof value === 'object' && p in value) {
      value = value[p]
    } else {
      return null
    }
  }

  return value
}

// 全局变量选择相关方法
const openVariableSelector = (type: 'header' | 'param', index: number) => {
  currentInputType.value = type
  currentInputIndex.value = index
  showVariableSelector.value = true
}

const selectGlobalVariable = (variableKey: string) => {
  const variableReference = `\${${variableKey}}`

  if (currentInputType.value === 'header' && currentInputIndex.value >= 0) {
    restConfig.headers[currentInputIndex.value].value = variableReference
  } else if (currentInputType.value === 'param' && currentInputIndex.value >= 0) {
    restConfig.params[currentInputIndex.value].value = variableReference
  }

  showVariableSelector.value = false
}

const getVariableDisplayText = (variable: any) => {
  const resolvedValue = dvMainStore.getGlobalVariableValue(variable.key)
  const displayValue = resolvedValue !== undefined ? ` (${resolvedValue})` : ' (未解析)'
  return `${variable.key}${displayValue}`
}

defineExpose({
  clearTestStates,
  testConnection,
  getFields: () => (customFields.value.length > 0 ? customFields.value : detectedFields.value),
  getCustomFields: () => customFields.value
})
</script>

<template>
  <div class="rest-data-source" :class="{ dark: themes === 'dark' }">
    <el-form :model="restConfig" label-width="100px" size="small">
      <!-- 基础配置 -->
      <el-form-item label="接口URL" required>
        <el-input
          v-model="restConfig.url"
          placeholder="请输入REST接口URL，如：https://api.example.com/data"
          clearable
        />
        <div class="form-tip">支持HTTP/HTTPS协议，建议使用返回JSON格式数据的接口</div>
      </el-form-item>

      <el-form-item label="请求方法">
        <el-select v-model="restConfig.method" style="width: 120px">
          <el-option label="GET" value="GET" />
          <el-option label="POST" value="POST" />
          <el-option label="PUT" value="PUT" />
          <el-option label="DELETE" value="DELETE" />
        </el-select>
      </el-form-item>

      <el-form-item label="超时时间">
        <el-input-number v-model="restConfig.timeout" :min="1" :max="300" style="width: 120px" />
        <span style="margin-left: 8px">秒</span>
      </el-form-item>

      <!-- 请求头配置 -->
      <el-form-item label="请求头">
        <div class="header-params">
          <div v-for="(header, index) in restConfig.headers" :key="index" class="param-row">
            <el-input v-model="header.key" placeholder="Header名称" style="width: 150px" />
            <el-input
              v-model="header.value"
              placeholder="Header值，支持 ${变量名} 格式"
              style="width: 200px; margin-left: 8px"
            >
              <template #append>
                <el-button @click="openVariableSelector('header', index)" size="small">
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
              </template>
            </el-input>
            <el-button
              type="danger"
              :icon="Delete"
              size="small"
              @click="removeHeader(index)"
              style="margin-left: 8px"
            />
          </div>
          <el-button type="primary" :icon="Plus" size="small" @click="addHeader">
            添加请求头
          </el-button>
        </div>
      </el-form-item>

      <!-- 请求参数 -->
      <el-form-item label="请求参数">
        <div class="header-params">
          <div v-for="(param, index) in restConfig.params" :key="index" class="param-row">
            <el-input v-model="param.key" placeholder="参数名" style="width: 150px" />
            <el-input
              v-model="param.value"
              placeholder="参数值，支持 ${变量名} 格式"
              style="width: 200px; margin-left: 8px"
            >
              <template #append>
                <el-button @click="openVariableSelector('param', index)" size="small">
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
              </template>
            </el-input>
            <el-button
              type="danger"
              :icon="Delete"
              size="small"
              @click="removeParam(index)"
              style="margin-left: 8px"
            />
          </div>
          <el-button type="primary" :icon="Plus" size="small" @click="addParam">
            添加参数
          </el-button>
        </div>
      </el-form-item>

      <!-- 请求体 (POST/PUT) -->
      <el-form-item
        v-if="restConfig.method === 'POST' || restConfig.method === 'PUT'"
        label="请求体"
      >
        <el-input
          v-model="restConfig.body"
          type="textarea"
          :rows="4"
          placeholder="请输入JSON格式的请求体"
          @blur="formatJson"
        />
      </el-form-item>

      <!-- 数据路径 -->
      <el-form-item label="数据路径">
        <el-input
          v-model="restConfig.dataPath"
          placeholder="例如: data.items (用于提取嵌套数据)"
          clearable
        />
        <div class="form-tip">
          如果API返回的数据嵌套在某个字段中，请指定路径。例如：data.items、result.list等
        </div>
      </el-form-item>

      <!-- 分页配置 -->
      <!-- <el-form-item label="分页配置">
        <el-checkbox v-model="restConfig.pagination.enabled">
          启用分页
        </el-checkbox>
        <div v-if="restConfig.pagination.enabled" style="margin-top: 8px">
          <el-input
            v-model="restConfig.pagination.pageParam"
            placeholder="页码参数名"
            style="width: 120px"
          />
          <el-input
            v-model="restConfig.pagination.sizeParam"
            placeholder="页大小参数名"
            style="width: 120px; margin-left: 8px"
          />
          <el-input-number
            v-model="restConfig.pagination.pageSize"
            :min="1"
            :max="1000"
            style="width: 120px; margin-left: 8px"
          />
        </div>
      </el-form-item> -->

      <!-- 测试按钮 -->
      <el-form-item>
        <el-button type="primary" @click="testConnection" :loading="loading"> 测试连接 </el-button>
        <el-button
          v-if="detectedFields.length > 0"
          type="success"
          @click="useDetectedFields"
          style="margin-left: 8px"
        >
          使用检测字段
        </el-button>
        <el-button type="info" @click="configureFields" style="margin-left: 8px">
          自定义字段
        </el-button>
        <el-button
          v-if="customFields.length > 0"
          type="warning"
          @click="previewRestData"
          :loading="previewLoading"
          style="margin-left: 8px"
        >
          数据预览
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 测试结果 -->
    <div v-if="testResult" class="test-result">
      <h4>测试结果:</h4>
      <el-input v-model="testResult" type="textarea" :rows="8" readonly />
    </div>

    <!-- 检测到的字段 -->
    <div v-if="detectedFields.length > 0" class="detected-fields">
      <h4>检测到的字段:</h4>
      <el-table :data="detectedFields" size="small">
        <el-table-column prop="name" label="字段名" />
        <el-table-column prop="type" label="类型" />
        <el-table-column prop="groupType" label="分组">
          <template #default="{ row }">
            {{ row.groupType === 'd' ? '维度' : '指标' }}
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路径" />
      </el-table>
    </div>

    <!-- 自定义字段配置 -->
    <div v-if="customFields.length > 0" class="custom-fields">
      <h4>自定义字段配置:</h4>
      <RestFieldEditor
        ref="fieldEditor"
        :fields="customFields"
        :themes="themes"
        @onFieldsChange="onCustomFieldsChange"
      />
    </div>

    <!-- 字段配置对话框 -->
    <el-dialog v-model="showFieldEditor" title="字段配置" width="80%" :close-on-click-modal="false">
      <RestFieldEditor
        :fields="customFields"
        :themes="themes"
        @onFieldsChange="onCustomFieldsChange"
      />
      <template #footer>
        <el-button @click="showFieldEditor = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 数据预览对话框 -->
    <el-dialog v-model="showDataPreview" title="数据预览" width="80%" :close-on-click-modal="false">
      <div v-if="previewData.length > 0">
        <p>预览前10条数据（基于当前字段配置）：</p>
        <el-table :data="previewData" size="small" border style="width: 100%" max-height="400">
          <el-table-column
            v-for="field in customFields.filter(f => f.enabled)"
            :key="field.id"
            :prop="field.customName || field.name"
            :label="field.customName || field.name"
            :width="150"
            show-overflow-tooltip
          >
            <template #header>
              <div>
                <div>{{ field.customName || field.name }}</div>
                <div style="font-size: 12px; color: #999">
                  {{ field.groupType === 'd' ? '维度' : '指标' }} | {{ field.type }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else>
        <el-empty description="暂无预览数据" />
      </div>

      <template #footer>
        <el-button @click="showDataPreview = false">关闭</el-button>
        <el-button type="primary" @click="previewRestData" :loading="previewLoading">
          刷新预览
        </el-button>
      </template>
    </el-dialog>

    <!-- 全局变量选择器 -->
    <el-dialog
      v-model="showVariableSelector"
      title="选择全局变量"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="variable-selector">
        <div v-if="globalVariables.length === 0" class="no-variables">
          <p>暂无全局变量</p>
          <p class="tip">请先在工具栏中添加全局变量</p>
        </div>
        <div v-else class="variable-list">
          <div
            v-for="variable in globalVariables"
            :key="variable.id"
            class="variable-item"
            :class="{ disabled: !variable.enabled }"
            @click="variable.enabled && selectGlobalVariable(variable.key)"
          >
            <div class="variable-info">
              <div class="variable-name">{{ variable.key }}</div>
              <div class="variable-desc">{{ variable.description || '无描述' }}</div>
            </div>
            <div class="variable-value">
              <el-tag :type="variable.type === 'static' ? 'success' : 'info'" size="small">
                {{ variable.type === 'static' ? '静态值' : '路由参数' }}
              </el-tag>
              <div class="resolved-value">{{ getVariableDisplayText(variable) }}</div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showVariableSelector = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="less">
.rest-data-source {
  padding: 16px;

  &.dark {
    background-color: #1a1a1a;
    color: #ebebeb;
  }

  .header-params {
    width: 100%;

    .param-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }
  }

  .test-result {
    margin-top: 16px;

    h4 {
      margin-bottom: 8px;
    }
  }

  .detected-fields {
    margin-top: 16px;

    h4 {
      margin-bottom: 8px;
    }
  }

  .custom-fields {
    margin-top: 16px;

    h4 {
      margin-bottom: 8px;
      color: #409eff;
    }
  }

  .form-tip {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
    line-height: 1.4;
  }

  .status-indicator {
    display: inline-flex;
    align-items: center;
    margin-left: 8px;
    font-size: 12px;

    &.success {
      color: #67c23a;
    }

    &.error {
      color: #f56c6c;
    }

    &.warning {
      color: #e6a23c;
    }
  }
}

.dark {
  .form-tip {
    color: #ccc;
  }

  .custom-fields h4 {
    color: #409eff;
  }
}

.variable-selector {
  .no-variables {
    text-align: center;
    padding: 40px 20px;
    color: #909399;

    .tip {
      font-size: 12px;
      margin-top: 8px;
    }
  }

  .variable-list {
    max-height: 400px;
    overflow-y: auto;

    .variable-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover:not(.disabled) {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-color: #f5f7fa;
      }

      .variable-info {
        flex: 1;

        .variable-name {
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
        }

        .variable-desc {
          font-size: 12px;
          color: #909399;
        }
      }

      .variable-value {
        text-align: right;

        .resolved-value {
          font-size: 12px;
          color: #606266;
          margin-top: 4px;
        }
      }
    }
  }
}
</style>
