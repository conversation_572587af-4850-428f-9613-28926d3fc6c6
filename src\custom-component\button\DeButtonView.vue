<template>
  <div class="de-button-view" :style="containerStyle">
    <el-button
      :type="buttonConfig.type"
      :size="buttonConfig.size"
      :disabled="buttonConfig.disabled"
      :loading="buttonConfig.loading"
      :icon="buttonConfig.icon"
      :round="buttonConfig.round"
      :circle="buttonConfig.circle"
      :plain="buttonConfig.plain"
      :text="buttonConfig.textButton"
      :bg="buttonConfig.bg"
      :link="buttonConfig.link"
      :style="buttonStyle"
      @click="handleClick"
    >
      {{ buttonConfig.text || '按钮' }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs } from 'vue'
import emitter from '@/utils/eventBus'

const props = defineProps({
  element: {
    type: Object,
    required: true
  },
  active: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showPosition: {
    type: String,
    default: 'canvas'
  },
  scale: {
    type: Number,
    default: 1
  },
  themes: {
    type: String,
    default: 'light'
  }
})

const emit = defineEmits(['onComponentEvent', 'onPointClick'])

const { element } = toRefs(props)

// 按钮配置
const buttonConfig = computed(() => {
  const config = element.value.propValue || {}
  return {
    text: config.text || '按钮',
    type: config.type || 'primary',
    size: config.size || 'default',
    disabled: config.disabled || false,
    loading: config.loading || false,
    icon: config.icon || '',
    round: config.round || false,
    circle: config.circle || false,
    plain: config.plain || false,
    textButton: config.textButton || false,
    bg: config.bg || false,
    link: config.link || false
  }
})

// 容器样式
const containerStyle = computed(() => {
  const style = element.value.style || {}
  return {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: style.verticalAlign || 'center',
    justifyContent: style.horizontalAlign || 'center',
    padding: style.padding || '0px',
    backgroundColor: style.backgroundColor || 'transparent'
  }
})

// 按钮样式
const buttonStyle = computed(() => {
  const style = element.value.style || {}
  const config = element.value.propValue || {}
  
  return {
    fontSize: style.fontSize ? style.fontSize + 'px' : '14px',
    fontWeight: style.fontWeight || 'normal',
    color: config.customColor ? config.textColor : undefined,
    backgroundColor: config.customColor ? config.backgroundColor : undefined,
    borderColor: config.customColor ? config.borderColor : undefined,
    borderRadius: style.borderRadius ? style.borderRadius + 'px' : undefined,
    width: config.fullWidth ? '100%' : 'auto',
    height: config.customHeight ? config.height + 'px' : 'auto'
  }
})

// 点击事件处理
const handleClick = () => {
  const config = element.value.propValue || {}

  // 触发组件事件
  emit('onComponentEvent')

  // 如果配置了跳转链接
  if (config.enableJump && config.jumpUrl) {
    if (config.jumpTarget === '_blank') {
      window.open(config.jumpUrl)
    } else {
      window.location.href = config.jumpUrl
    }
  }

  // 如果配置了打开弹框
  if (config.enableModal && config.targetModalId) {
    emitter.emit(`open-modal-${config.targetModalId}`)
  }

  // 触发点击事件
  emit('onPointClick', {
    type: 'button-click',
    data: config,
    element: element.value
  })
}
</script>

<style lang="less" scoped>
.de-button-view {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: stretch;
  justify-content: stretch;
  padding: 0;
  margin: 0;
  background: transparent;
  border: none;

  :deep(.el-button) {
    transition: all 0.3s;
    width: 100% !important;
    height: 100% !important;
    min-height: 100% !important;
    border-radius: 4px;
    margin: 0 !important;
    padding: 8px 16px;
    flex: 1;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: translateY(0);
    }
  }
}
</style>
