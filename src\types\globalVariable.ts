/**
 * 全局变量相关类型定义
 */

export interface GlobalVariable {
  id: string;           // 唯一标识
  key: string;          // 变量名称（用于引用）
  type: 'static' | 'route'; // 变量类型：静态值或路由参数
  value?: string;       // 静态值（当type为static时）
  routeParam?: string;  // 路由参数名（当type为route时）
  description?: string; // 变量描述
  enabled: boolean;     // 是否启用
  createTime?: string;  // 创建时间
  updateTime?: string;  // 更新时间
}

export interface GlobalVariableStore {
  variables: GlobalVariable[];
  resolvedValues: Record<string, any>; // 解析后的变量值缓存
}

export interface GlobalVariableFormData {
  key: string;
  type: 'static' | 'route';
  value?: string;
  routeParam?: string;
  description?: string;
  enabled: boolean;
}

// 全局变量验证规则
export const GLOBAL_VARIABLE_RULES = {
  key: [
    { required: true, message: '请输入变量名称', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, 
      message: '变量名只能包含字母、数字和下划线，且不能以数字开头', 
      trigger: 'blur' 
    },
    { min: 1, max: 50, message: '变量名长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '请输入变量值', trigger: 'blur' }
  ],
  routeParam: [
    { required: true, message: '请输入路由参数名', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, 
      message: '路由参数名只能包含字母、数字和下划线，且不能以数字开头', 
      trigger: 'blur' 
    }
  ]
}

// 常用的路由参数名称
export const COMMON_ROUTE_PARAMS = [
  { label: 'id', value: 'id', description: '资源ID' },
  { label: 'resourceId', value: 'resourceId', description: '资源ID' },
  { label: 'token', value: 'token', description: '访问令牌' },
  { label: 'userId', value: 'userId', description: '用户ID' },
  { label: 'orgId', value: 'orgId', description: '组织ID' },
  { label: 'type', value: 'type', description: '类型参数' },
  { label: 'category', value: 'category', description: '分类参数' }
]

// 全局变量引用格式
export const VARIABLE_REFERENCE_PATTERNS = {
  DOLLAR_BRACE: /\$\{([^}]+)\}/g,  // ${variableName}
  DOUBLE_BRACE: /\{\{([^}]+)\}\}/g  // {{variableName}}
}
