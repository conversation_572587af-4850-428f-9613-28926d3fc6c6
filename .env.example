# 图片处理配置示例
# Image Processing Configuration Example

# 是否启用base64模式（默认: true）
# Whether to enable base64 mode (default: true)
VITE_IMAGE_USE_BASE64=true

# 是否启用图片压缩（默认: true）
# Whether to enable image compression (default: true)
VITE_IMAGE_COMPRESSION_ENABLED=true

# 压缩质量 0-1（默认: 0.8）
# Compression quality 0-1 (default: 0.8)
VITE_IMAGE_COMPRESSION_QUALITY=0.8

# 最大宽度（默认: 1920）
# Maximum width (default: 1920)
VITE_IMAGE_MAX_WIDTH=1920

# 最大高度（默认: 1080）
# Maximum height (default: 1080)
VITE_IMAGE_MAX_HEIGHT=1080

# 最大文件大小，字节（默认: 15MB = 15728640）
# Maximum file size in bytes (default: 15MB = 15728640)
VITE_IMAGE_MAX_FILE_SIZE=15728640

# 其他现有配置...
# Other existing configurations...
